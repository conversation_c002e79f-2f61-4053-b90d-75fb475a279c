{"ast": null, "code": "var _jsxFileName = \"C:\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\cavi\\\\CertificazioneCaviImproved.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, forwardRef, useImperativeHandle } from 'react';\nimport { Box, Typography, Button, Paper, Grid, Card, CardContent, Dialog, DialogTitle, DialogContent, DialogActions, TextField, FormControl, InputLabel, Select, MenuItem, Autocomplete, CircularProgress, Alert, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, IconButton, Tabs, Tab, Pagination, InputAdornment, Divider, Stack, Chip, Tooltip, Badge, LinearProgress, Collapse, List, ListItem, ListItemText, ListItemIcon, Snackbar, AppBar, Toolbar, Container, Fab, SpeedDial, SpeedDialAction, SpeedDialIcon } from '@mui/material';\nimport { Add as AddIcon, Search as SearchIcon, FilterList as FilterIcon, PictureAsPdf as PdfIcon, Download as DownloadIcon, Visibility as ViewIcon, Delete as DeleteIcon, Edit as EditIcon, Save as SaveIcon, Clear as ClearIcon, Build as BuildIcon, CheckCircle as CheckIcon, Warning as WarningIcon, GetApp as ExportIcon, Print as PrintIcon, Email as EmailIcon, CloudUpload as UploadIcon, Assessment as ReportIcon, Settings as SettingsIcon, Refresh as RefreshIcon, ExpandMore as ExpandMoreIcon, ExpandLess as ExpandLessIcon, Info as InfoIcon, Error as ErrorIcon, Schedule as ScheduleIcon, Person as PersonIcon, Cable as CableIcon, Science as ScienceIcon, Block as BlockIcon } from '@mui/icons-material';\nimport certificazioneService from '../../services/certificazioneService';\nimport caviService from '../../services/caviService';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst CertificazioneCaviImproved = /*#__PURE__*/_s(/*#__PURE__*/forwardRef(_c = _s(({\n  cantiereId,\n  onSuccess,\n  onError\n}, ref) => {\n  _s();\n  // Stati principali\n  const [loading, setLoading] = useState(false);\n  const [activeTab, setActiveTab] = useState(0);\n  const [certificazioni, setCertificazioni] = useState([]);\n  const [cavi, setCavi] = useState([]);\n  const [strumenti, setStrumenti] = useState([]);\n\n  // Stati per ricerca e filtri avanzati\n  const [searchTerm, setSearchTerm] = useState('');\n  const [filteredCavi, setFilteredCavi] = useState([]);\n  const [filteredCertificazioni, setFilteredCertificazioni] = useState([]);\n  const [advancedFiltersOpen, setAdvancedFiltersOpen] = useState(false);\n  const [filters, setFilters] = useState({\n    stato: '',\n    tipologia: '',\n    operatore: '',\n    dataInizio: '',\n    dataFine: '',\n    valoreIsolamento: '',\n    risultatoTest: '',\n    strumento: '',\n    certificazione: ''\n  });\n\n  // Stati per paginazione e ordinamento\n  const [currentPage, setCurrentPage] = useState(1);\n  const [itemsPerPage, setItemsPerPage] = useState(10);\n  const [sortBy, setSortBy] = useState('data_certificazione');\n  const [sortOrder, setSortOrder] = useState('desc');\n\n  // Stati per dialogs e modali\n  const [openDialog, setOpenDialog] = useState(false);\n  const [dialogType, setDialogType] = useState('');\n  const [selectedItem, setSelectedItem] = useState(null);\n  const [bulkSelection, setBulkSelection] = useState([]);\n  const [bulkMode, setBulkMode] = useState(false);\n\n  // Stati per notifiche e feedback\n  const [snackbar, setSnackbar] = useState({\n    open: false,\n    message: '',\n    severity: 'info'\n  });\n  const [progress, setProgress] = useState(0);\n  const [operationInProgress, setOperationInProgress] = useState(false);\n\n  // Stati per form certificazione avanzato\n  const [formData, setFormData] = useState({\n    id_cavo: '',\n    id_operatore: '',\n    id_strumento: '',\n    lunghezza_misurata: '',\n    valore_continuita: 'OK',\n    valore_isolamento: '',\n    valore_resistenza: 'OK',\n    note: '',\n    temperatura_ambiente: '',\n    umidita: '',\n    tensione_prova: '',\n    durata_prova: '',\n    risultato_finale: 'CONFORME'\n  });\n\n  // Stati per statistiche e dashboard\n  const [statistics, setStatistics] = useState({\n    totaleCavi: 0,\n    caviCertificati: 0,\n    caviNonCertificati: 0,\n    percentualeCompletamento: 0,\n    certificazioniOggi: 0,\n    certificazioniSettimana: 0\n  });\n\n  // Carica dati iniziali\n  useEffect(() => {\n    loadInitialData();\n  }, [cantiereId]);\n\n  // Filtra cavi in base alla ricerca\n  useEffect(() => {\n    filterCavi();\n  }, [cavi, searchTerm, filters, sortBy, sortOrder]);\n\n  // Filtra certificazioni\n  useEffect(() => {\n    filterCertificazioni();\n  }, [certificazioni, searchTerm, filters, sortBy, sortOrder]);\n\n  // Ricalcola statistiche quando cambiano i dati\n  useEffect(() => {\n    calculateStatistics();\n  }, [cavi, certificazioni]);\n  const loadInitialData = async () => {\n    try {\n      setLoading(true);\n      setProgress(0);\n\n      // Carica dati in sequenza con progress\n      setProgress(25);\n      await loadCavi();\n      setProgress(50);\n      await loadCertificazioni();\n      setProgress(75);\n      await loadStrumenti();\n      setProgress(100);\n      calculateStatistics();\n    } catch (error) {\n      showSnackbar('Errore nel caricamento dei dati iniziali', 'error');\n      onError('Errore nel caricamento dei dati iniziali');\n    } finally {\n      setLoading(false);\n      setProgress(0);\n    }\n  };\n  const loadCertificazioni = async () => {\n    try {\n      const data = await certificazioneService.getCertificazioni(cantiereId);\n      setCertificazioni(data);\n      return data;\n    } catch (error) {\n      console.error('Errore nel caricamento delle certificazioni:', error);\n      throw error;\n    }\n  };\n  const loadCavi = async () => {\n    try {\n      const data = await caviService.getCavi(cantiereId);\n      setCavi(data);\n      return data;\n    } catch (error) {\n      console.error('Errore nel caricamento dei cavi:', error);\n      throw error;\n    }\n  };\n  const loadStrumenti = async () => {\n    try {\n      const data = await certificazioneService.getStrumenti(cantiereId);\n      setStrumenti(data);\n      return data;\n    } catch (error) {\n      console.error('Errore nel caricamento degli strumenti:', error);\n      throw error;\n    }\n  };\n\n  // Calcola statistiche avanzate\n  const calculateStatistics = () => {\n    const totaleCavi = cavi.length;\n    const caviCertificati = certificazioni.length;\n    const caviNonCertificati = totaleCavi - caviCertificati;\n    const percentualeCompletamento = totaleCavi > 0 ? Math.round(caviCertificati / totaleCavi * 100) : 0;\n\n    // Calcola certificazioni di oggi\n    const oggi = new Date().toDateString();\n    const certificazioniOggi = certificazioni.filter(cert => new Date(cert.data_certificazione).toDateString() === oggi).length;\n\n    // Calcola certificazioni della settimana\n    const unaSettimanaFa = new Date();\n    unaSettimanaFa.setDate(unaSettimanaFa.getDate() - 7);\n    const certificazioniSettimana = certificazioni.filter(cert => new Date(cert.data_certificazione) >= unaSettimanaFa).length;\n\n    // Calcola cavi certificabili (solo posati/installati)\n    const caviCertificabili = cavi.filter(cavo => puoEssereCertificato(cavo)).length;\n    const caviNonCertificabili = totaleCavi - caviCertificabili;\n\n    // Calcola cavi collegati completamente\n    const caviCollegati = cavi.filter(cavo => isCavoCollegato(cavo)).length;\n    setStatistics({\n      totaleCavi,\n      caviCertificati,\n      caviNonCertificati,\n      caviCertificabili,\n      caviNonCertificabili,\n      caviCollegati,\n      percentualeCompletamento,\n      certificazioniOggi,\n      certificazioniSettimana\n    });\n  };\n\n  // Gestione snackbar\n  const showSnackbar = (message, severity = 'info') => {\n    setSnackbar({\n      open: true,\n      message,\n      severity\n    });\n  };\n  const closeSnackbar = () => {\n    setSnackbar({\n      ...snackbar,\n      open: false\n    });\n  };\n  const filterCavi = () => {\n    let filtered = cavi;\n\n    // Filtro per ricerca testuale avanzata\n    if (searchTerm) {\n      const searchLower = searchTerm.toLowerCase();\n      filtered = filtered.filter(cavo => {\n        var _cavo$tipologia, _cavo$ubicazione_part, _cavo$ubicazione_arri, _cavo$sezione, _cavo$utility;\n        return cavo.id_cavo.toLowerCase().includes(searchLower) || ((_cavo$tipologia = cavo.tipologia) === null || _cavo$tipologia === void 0 ? void 0 : _cavo$tipologia.toLowerCase().includes(searchLower)) || ((_cavo$ubicazione_part = cavo.ubicazione_partenza) === null || _cavo$ubicazione_part === void 0 ? void 0 : _cavo$ubicazione_part.toLowerCase().includes(searchLower)) || ((_cavo$ubicazione_arri = cavo.ubicazione_arrivo) === null || _cavo$ubicazione_arri === void 0 ? void 0 : _cavo$ubicazione_arri.toLowerCase().includes(searchLower)) || ((_cavo$sezione = cavo.sezione) === null || _cavo$sezione === void 0 ? void 0 : _cavo$sezione.toLowerCase().includes(searchLower)) || ((_cavo$utility = cavo.utility) === null || _cavo$utility === void 0 ? void 0 : _cavo$utility.toLowerCase().includes(searchLower));\n      });\n    }\n\n    // Filtri specifici avanzati\n    if (filters.stato) {\n      filtered = filtered.filter(cavo => cavo.stato_installazione === filters.stato);\n    }\n    if (filters.tipologia) {\n      filtered = filtered.filter(cavo => cavo.tipologia === filters.tipologia);\n    }\n\n    // Filtro per stato certificazione\n    if (filters.certificazione) {\n      if (filters.certificazione === 'CERTIFICATO') {\n        filtered = filtered.filter(cavo => certificazioni.some(cert => cert.id_cavo === cavo.id_cavo));\n      } else if (filters.certificazione === 'NON_CERTIFICATO') {\n        filtered = filtered.filter(cavo => !certificazioni.some(cert => cert.id_cavo === cavo.id_cavo));\n      }\n    }\n\n    // Ordinamento\n    filtered.sort((a, b) => {\n      let aValue = a[sortBy];\n      let bValue = b[sortBy];\n      if (typeof aValue === 'string') {\n        aValue = aValue.toLowerCase();\n        bValue = bValue.toLowerCase();\n      }\n      if (sortOrder === 'asc') {\n        return aValue > bValue ? 1 : -1;\n      } else {\n        return aValue < bValue ? 1 : -1;\n      }\n    });\n    setFilteredCavi(filtered);\n  };\n  const filterCertificazioni = () => {\n    let filtered = certificazioni;\n\n    // Ricerca testuale avanzata\n    if (searchTerm) {\n      const searchLower = searchTerm.toLowerCase();\n      filtered = filtered.filter(cert => {\n        var _cert$operatore, _cert$numero_certific, _cert$note;\n        return cert.id_cavo.toLowerCase().includes(searchLower) || ((_cert$operatore = cert.operatore) === null || _cert$operatore === void 0 ? void 0 : _cert$operatore.toLowerCase().includes(searchLower)) || ((_cert$numero_certific = cert.numero_certificato) === null || _cert$numero_certific === void 0 ? void 0 : _cert$numero_certific.toLowerCase().includes(searchLower)) || ((_cert$note = cert.note) === null || _cert$note === void 0 ? void 0 : _cert$note.toLowerCase().includes(searchLower));\n      });\n    }\n\n    // Filtri avanzati\n    if (filters.operatore) {\n      filtered = filtered.filter(cert => cert.operatore === filters.operatore);\n    }\n    if (filters.strumento) {\n      filtered = filtered.filter(cert => cert.strumento === filters.strumento);\n    }\n    if (filters.risultatoTest) {\n      filtered = filtered.filter(cert => cert.risultato_finale === filters.risultatoTest);\n    }\n    if (filters.dataInizio) {\n      filtered = filtered.filter(cert => new Date(cert.data_certificazione) >= new Date(filters.dataInizio));\n    }\n    if (filters.dataFine) {\n      filtered = filtered.filter(cert => new Date(cert.data_certificazione) <= new Date(filters.dataFine));\n    }\n    if (filters.valoreIsolamento) {\n      const valore = parseFloat(filters.valoreIsolamento);\n      filtered = filtered.filter(cert => parseFloat(cert.valore_isolamento) >= valore);\n    }\n\n    // Ordinamento\n    filtered.sort((a, b) => {\n      let aValue = a[sortBy];\n      let bValue = b[sortBy];\n      if (sortBy === 'data_certificazione') {\n        aValue = new Date(aValue);\n        bValue = new Date(bValue);\n      } else if (typeof aValue === 'string') {\n        aValue = aValue.toLowerCase();\n        bValue = bValue.toLowerCase();\n      }\n      if (sortOrder === 'asc') {\n        return aValue > bValue ? 1 : -1;\n      } else {\n        return aValue < bValue ? 1 : -1;\n      }\n    });\n    setFilteredCertificazioni(filtered);\n  };\n\n  // Gestione selezione multipla - SOLO per certificazioni\n  const toggleBulkMode = () => {\n    if (activeTab !== 1) {\n      showSnackbar('La selezione multipla è disponibile solo per le certificazioni', 'warning');\n      return;\n    }\n    setBulkMode(!bulkMode);\n    setBulkSelection([]);\n    showSnackbar(!bulkMode ? 'Modalità selezione attivata - Clicca sulle certificazioni per selezionarle' : 'Modalità selezione disattivata', 'info');\n  };\n  const toggleItemSelection = itemId => {\n    setBulkSelection(prev => {\n      const newSelection = prev.includes(itemId) ? prev.filter(id => id !== itemId) : [...prev, itemId];\n      showSnackbar(`${newSelection.length} certificazioni selezionate`, 'info');\n      return newSelection;\n    });\n  };\n  const selectAllItems = () => {\n    if (activeTab !== 1) return;\n    const allIds = filteredCertificazioni.map(cert => cert.id_certificazione);\n    setBulkSelection(allIds);\n    showSnackbar(`Tutte le ${allIds.length} certificazioni selezionate`, 'success');\n  };\n  const clearSelection = () => {\n    setBulkSelection([]);\n    showSnackbar('Selezione cancellata', 'info');\n  };\n\n  // Funzione per verificare se un cavo è certificato\n  const isCavoCertificato = idCavo => {\n    return certificazioni.some(cert => cert.id_cavo === idCavo);\n  };\n\n  // Funzione per verificare se un cavo può essere certificato\n  const puoEssereCertificato = cavo => {\n    // Verifica che il cavo sia installato/posato\n    const isInstallato = cavo.stato_installazione === 'Installato' || cavo.stato_installazione === 'INSTALLATO' || cavo.stato_installazione === 'POSATO';\n\n    // Per la certificazione basta che sia posato\n    // Il collegamento può essere gestito durante la certificazione\n    return isInstallato;\n  };\n\n  // Funzione per verificare se un cavo è completamente collegato\n  const isCavoCollegato = cavo => {\n    const isCollegato = cavo.collegamenti === 3;\n    const hasResponsabili = cavo.responsabile_partenza && cavo.responsabile_arrivo;\n    return isCollegato && hasResponsabili;\n  };\n\n  // Funzione per ottenere il messaggio di errore per cavi non certificabili\n  const getMessaggioErroreCertificazione = cavo => {\n    const isInstallato = cavo.stato_installazione === 'Installato' || cavo.stato_installazione === 'INSTALLATO' || cavo.stato_installazione === 'POSATO';\n    if (!isInstallato) {\n      return 'Il cavo deve essere posato/installato prima di poter essere certificato';\n    }\n    return 'Cavo non certificabile per motivi sconosciuti';\n  };\n\n  // Gestione tabs\n  const handleTabChange = (event, newValue) => {\n    setActiveTab(newValue);\n    setCurrentPage(1);\n    setSearchTerm('');\n    setFilters({\n      stato: '',\n      tipologia: '',\n      operatore: ''\n    });\n  };\n\n  // Gestione dialogs\n  const openCreateDialog = (cavoPreselezionato = null) => {\n    setDialogType('create');\n    setSelectedItem(null);\n\n    // Se viene passato un cavo, precompila il form\n    if (cavoPreselezionato) {\n      setFormData({\n        id_cavo: cavoPreselezionato.id_cavo,\n        id_operatore: '',\n        id_strumento: '',\n        lunghezza_misurata: cavoPreselezionato.metratura_reale || cavoPreselezionato.metri_teorici || '',\n        valore_continuita: 'OK',\n        valore_isolamento: '',\n        valore_resistenza: 'OK',\n        note: '',\n        temperatura_ambiente: '',\n        umidita: '',\n        tensione_prova: '',\n        durata_prova: '',\n        risultato_finale: 'CONFORME'\n      });\n      showSnackbar(`Cavo ${cavoPreselezionato.id_cavo} selezionato automaticamente`, 'success');\n    } else {\n      // Reset form per nuova certificazione generica\n      setFormData({\n        id_cavo: '',\n        id_operatore: '',\n        id_strumento: '',\n        lunghezza_misurata: '',\n        valore_continuita: 'OK',\n        valore_isolamento: '',\n        valore_resistenza: 'OK',\n        note: '',\n        temperatura_ambiente: '',\n        umidita: '',\n        tensione_prova: '',\n        durata_prova: '',\n        risultato_finale: 'CONFORME'\n      });\n    }\n    setOpenDialog(true);\n  };\n  const closeDialog = () => {\n    setOpenDialog(false);\n    setSelectedItem(null);\n    setDialogType('');\n  };\n\n  // Gestione form\n  const handleFormChange = (field, value) => {\n    setFormData(prev => ({\n      ...prev,\n      [field]: value\n    }));\n  };\n  const handleCavoSelect = cavo => {\n    setFormData(prev => ({\n      ...prev,\n      id_cavo: cavo.id_cavo,\n      lunghezza_misurata: cavo.metratura_reale || cavo.metri_teorici || ''\n    }));\n  };\n\n  // Operazioni CRUD avanzate\n  const handleCreateCertificazione = async () => {\n    try {\n      if (!formData.id_cavo || !formData.id_operatore || !formData.id_strumento || !formData.valore_isolamento) {\n        showSnackbar('Compila tutti i campi obbligatori', 'warning');\n        return;\n      }\n\n      // Verifica che il cavo possa essere certificato\n      const cavo = cavi.find(c => c.id_cavo === formData.id_cavo);\n      if (!cavo) {\n        showSnackbar('Cavo non trovato', 'error');\n        return;\n      }\n      if (!puoEssereCertificato(cavo)) {\n        const messaggio = getMessaggioErroreCertificazione(cavo);\n        showSnackbar(`Impossibile certificare il cavo: ${messaggio}`, 'error');\n        return;\n      }\n\n      // Verifica che il cavo non sia già certificato\n      if (isCavoCertificato(formData.id_cavo)) {\n        showSnackbar('Il cavo è già stato certificato', 'warning');\n        return;\n      }\n\n      // Verifica se il cavo è collegato, altrimenti chiedi conferma\n      if (!isCavoCollegato(cavo)) {\n        const conferma = window.confirm(`ATTENZIONE: Il cavo ${cavo.id_cavo} non risulta completamente collegato.\\n\\n` + `Stato collegamenti: ${cavo.collegamenti === 0 ? 'Non collegato' : cavo.collegamenti === 1 ? 'Solo partenza collegata' : cavo.collegamenti === 2 ? 'Solo arrivo collegato' : 'Stato sconosciuto'}\\n\\n` + `Vuoi procedere comunque con la certificazione?\\n` + `(Ricorda di completare i collegamenti prima della messa in servizio)`);\n        if (!conferma) {\n          return;\n        }\n      }\n      setOperationInProgress(true);\n      await certificazioneService.createCertificazione(cantiereId, formData);\n      showSnackbar('Certificazione creata con successo', 'success');\n      closeDialog();\n      await loadCertificazioni();\n      calculateStatistics();\n    } catch (error) {\n      showSnackbar('Errore nella creazione della certificazione: ' + (error.message || 'Errore sconosciuto'), 'error');\n    } finally {\n      setOperationInProgress(false);\n    }\n  };\n  const handleGeneratePdf = async certificazione => {\n    try {\n      setOperationInProgress(true);\n      showSnackbar('Generazione PDF in corso...', 'info');\n      const response = await certificazioneService.generatePdf(cantiereId, certificazione.id_certificazione);\n      if (response.file_url) {\n        // Apri il PDF in una nuova finestra\n        const newWindow = window.open(response.file_url, '_blank');\n        if (newWindow) {\n          showSnackbar('PDF generato e aperto in una nuova finestra', 'success');\n        } else {\n          // Se il popup è bloccato, offri il download diretto\n          const link = document.createElement('a');\n          link.href = response.file_url;\n          link.download = `certificazione_${certificazione.numero_certificato}.pdf`;\n          document.body.appendChild(link);\n          link.click();\n          document.body.removeChild(link);\n          showSnackbar('PDF scaricato nella cartella Download', 'success');\n        }\n      } else if (response.pdf_content) {\n        // Se il PDF viene restituito come contenuto base64\n        const blob = new Blob([atob(response.pdf_content)], {\n          type: 'application/pdf'\n        });\n        const url = URL.createObjectURL(blob);\n        const link = document.createElement('a');\n        link.href = url;\n        link.download = `certificazione_${certificazione.numero_certificato}.pdf`;\n        document.body.appendChild(link);\n        link.click();\n        document.body.removeChild(link);\n        URL.revokeObjectURL(url);\n        showSnackbar('PDF scaricato con successo', 'success');\n      } else {\n        showSnackbar('Errore: Formato PDF non riconosciuto', 'error');\n      }\n    } catch (error) {\n      console.error('Errore generazione PDF:', error);\n      showSnackbar('Errore nella generazione del PDF: ' + (error.message || 'Errore sconosciuto'), 'error');\n    } finally {\n      setOperationInProgress(false);\n    }\n  };\n  const handleDeleteCertificazione = async certificazione => {\n    if (window.confirm(`Sei sicuro di voler eliminare la certificazione ${certificazione.numero_certificato}?`)) {\n      try {\n        setOperationInProgress(true);\n        await certificazioneService.deleteCertificazione(cantiereId, certificazione.id_certificazione);\n        showSnackbar('Certificazione eliminata con successo', 'success');\n        await loadCertificazioni();\n        calculateStatistics();\n      } catch (error) {\n        showSnackbar('Errore nell\\'eliminazione della certificazione: ' + (error.message || 'Errore sconosciuto'), 'error');\n      } finally {\n        setOperationInProgress(false);\n      }\n    }\n  };\n\n  // Operazioni bulk\n  const handleBulkDelete = async () => {\n    if (bulkSelection.length === 0) {\n      showSnackbar('Seleziona almeno un elemento', 'warning');\n      return;\n    }\n    if (window.confirm(`Sei sicuro di voler eliminare ${bulkSelection.length} certificazioni?`)) {\n      try {\n        setOperationInProgress(true);\n        for (const id of bulkSelection) {\n          await certificazioneService.deleteCertificazione(cantiereId, id);\n        }\n        showSnackbar(`${bulkSelection.length} certificazioni eliminate con successo`, 'success');\n        setBulkSelection([]);\n        await loadCertificazioni();\n        calculateStatistics();\n      } catch (error) {\n        showSnackbar('Errore nell\\'eliminazione delle certificazioni', 'error');\n      } finally {\n        setOperationInProgress(false);\n      }\n    }\n  };\n  const handleBulkExport = async () => {\n    if (bulkSelection.length === 0) {\n      showSnackbar('Seleziona almeno un elemento', 'warning');\n      return;\n    }\n    try {\n      setOperationInProgress(true);\n      // Implementa export bulk\n      const selectedCerts = certificazioni.filter(cert => bulkSelection.includes(cert.id_certificazione));\n\n      // Crea CSV\n      const csvContent = generateCSV(selectedCerts);\n      downloadCSV(csvContent, `certificazioni_${new Date().toISOString().split('T')[0]}.csv`);\n      showSnackbar(`${bulkSelection.length} certificazioni esportate`, 'success');\n    } catch (error) {\n      showSnackbar('Errore nell\\'esportazione', 'error');\n    } finally {\n      setOperationInProgress(false);\n    }\n  };\n\n  // Funzioni di export\n  const generateCSV = data => {\n    const headers = ['ID Cavo', 'Numero Certificato', 'Data', 'Operatore', 'Strumento', 'Lunghezza', 'Isolamento', 'Risultato'];\n    const rows = data.map(cert => [cert.id_cavo, cert.numero_certificato, new Date(cert.data_certificazione).toLocaleDateString(), cert.operatore, cert.strumento, cert.lunghezza_misurata, cert.valore_isolamento, cert.risultato_finale]);\n    return [headers, ...rows].map(row => row.join(',')).join('\\n');\n  };\n  const downloadCSV = (content, filename) => {\n    const blob = new Blob([content], {\n      type: 'text/csv;charset=utf-8;'\n    });\n    const link = document.createElement('a');\n    if (link.download !== undefined) {\n      const url = URL.createObjectURL(blob);\n      link.setAttribute('href', url);\n      link.setAttribute('download', filename);\n      link.style.visibility = 'hidden';\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n    }\n  };\n  const handleExportAll = () => {\n    const csvContent = generateCSV(filteredCertificazioni);\n    downloadCSV(csvContent, `tutte_certificazioni_${new Date().toISOString().split('T')[0]}.csv`);\n    showSnackbar('Esportazione completata', 'success');\n  };\n\n  // Espone metodi tramite ref\n  useImperativeHandle(ref, () => ({\n    handleOptionSelect: option => {\n      if (option === 'creaCertificazione') {\n        openCreateDialog();\n      } else if (option === 'visualizzaCertificazioni') {\n        setActiveTab(1);\n      }\n    }\n  }));\n\n  // Calcola elementi per paginazione\n  const getCurrentPageItems = items => {\n    const startIndex = (currentPage - 1) * itemsPerPage;\n    const endIndex = startIndex + itemsPerPage;\n    return items.slice(startIndex, endIndex);\n  };\n  const getTotalPages = items => Math.ceil(items.length / itemsPerPage);\n\n  // Ottieni opzioni uniche per filtri\n  const getUniqueValues = (array, field) => {\n    return [...new Set(array.map(item => item[field]).filter(Boolean))];\n  };\n\n  // Dashboard minimal con statistiche essenziali\n  const renderDashboard = () => /*#__PURE__*/_jsxDEV(Paper, {\n    sx: {\n      p: 2,\n      mb: 3,\n      bgcolor: 'grey.50'\n    },\n    children: /*#__PURE__*/_jsxDEV(Stack, {\n      direction: \"row\",\n      spacing: 4,\n      alignItems: \"center\",\n      justifyContent: \"space-between\",\n      flexWrap: \"wrap\",\n      children: [/*#__PURE__*/_jsxDEV(Stack, {\n        direction: \"row\",\n        alignItems: \"center\",\n        spacing: 1,\n        children: [/*#__PURE__*/_jsxDEV(CableIcon, {\n          color: \"primary\",\n          fontSize: \"small\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 799,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            fontWeight: \"bold\",\n            sx: {\n              lineHeight: 1\n            },\n            children: statistics.totaleCavi\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 801,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"caption\",\n            color: \"text.secondary\",\n            children: \"Totale\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 804,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 800,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 798,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Stack, {\n        direction: \"row\",\n        alignItems: \"center\",\n        spacing: 1,\n        children: [/*#__PURE__*/_jsxDEV(CheckIcon, {\n          color: \"success\",\n          fontSize: \"small\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 811,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            fontWeight: \"bold\",\n            sx: {\n              lineHeight: 1\n            },\n            children: statistics.caviCertificati\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 813,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"caption\",\n            color: \"text.secondary\",\n            children: \"Certificati\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 816,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 812,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 810,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Stack, {\n        direction: \"row\",\n        alignItems: \"center\",\n        spacing: 1,\n        children: [/*#__PURE__*/_jsxDEV(BuildIcon, {\n          color: \"info\",\n          fontSize: \"small\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 823,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            fontWeight: \"bold\",\n            sx: {\n              lineHeight: 1\n            },\n            children: statistics.caviCertificabili\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 825,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"caption\",\n            color: \"text.secondary\",\n            children: \"Pronti\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 828,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 824,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 822,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Stack, {\n        direction: \"row\",\n        alignItems: \"center\",\n        spacing: 1,\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            width: 32,\n            height: 32,\n            borderRadius: '50%',\n            bgcolor: statistics.percentualeCompletamento >= 80 ? 'success.main' : statistics.percentualeCompletamento >= 50 ? 'warning.main' : 'error.main',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center'\n          },\n          children: /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"caption\",\n            fontWeight: \"bold\",\n            color: \"white\",\n            children: [statistics.percentualeCompletamento, \"%\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 845,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 835,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            fontWeight: \"medium\",\n            sx: {\n              lineHeight: 1\n            },\n            children: \"Completamento\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 850,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"caption\",\n            color: \"text.secondary\",\n            children: [statistics.certificazioniOggi, \" oggi\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 853,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 849,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 834,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"outlined\",\n        size: \"small\",\n        startIcon: /*#__PURE__*/_jsxDEV(ReportIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 863,\n          columnNumber: 22\n        }, this),\n        onClick: () => window.open(`/cantieri/${cantiereId}/report-avanzamento`, '_blank'),\n        sx: {\n          ml: 'auto'\n        },\n        children: \"Report Dettagliato\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 860,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 796,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 795,\n    columnNumber: 5\n  }, this);\n\n  // Componente barra di ricerca avanzata\n  const renderSearchAndFilters = () => /*#__PURE__*/_jsxDEV(Paper, {\n    sx: {\n      p: 2,\n      mb: 3\n    },\n    children: [/*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 2,\n      alignItems: \"center\",\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 4,\n        children: /*#__PURE__*/_jsxDEV(TextField, {\n          fullWidth: true,\n          placeholder: \"Cerca cavi, certificazioni, operatori...\",\n          value: searchTerm,\n          onChange: e => setSearchTerm(e.target.value),\n          InputProps: {\n            startAdornment: /*#__PURE__*/_jsxDEV(InputAdornment, {\n              position: \"start\",\n              children: /*#__PURE__*/_jsxDEV(SearchIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 886,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 885,\n              columnNumber: 17\n            }, this),\n            endAdornment: searchTerm && /*#__PURE__*/_jsxDEV(InputAdornment, {\n              position: \"end\",\n              children: /*#__PURE__*/_jsxDEV(IconButton, {\n                onClick: () => setSearchTerm(''),\n                size: \"small\",\n                children: /*#__PURE__*/_jsxDEV(ClearIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 892,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 891,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 890,\n              columnNumber: 17\n            }, this)\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 878,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 877,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 2,\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          fullWidth: true,\n          variant: \"outlined\",\n          startIcon: /*#__PURE__*/_jsxDEV(FilterIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 904,\n            columnNumber: 24\n          }, this),\n          onClick: () => setAdvancedFiltersOpen(!advancedFiltersOpen),\n          color: Object.values(filters).some(f => f) ? 'primary' : 'inherit',\n          children: [\"Filtri \", Object.values(filters).filter(f => f).length > 0 && `(${Object.values(filters).filter(f => f).length})`]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 901,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 900,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 2,\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          fullWidth: true,\n          variant: \"outlined\",\n          startIcon: bulkMode ? /*#__PURE__*/_jsxDEV(ClearIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 916,\n            columnNumber: 35\n          }, this) : /*#__PURE__*/_jsxDEV(CheckIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 916,\n            columnNumber: 51\n          }, this),\n          onClick: toggleBulkMode,\n          color: bulkMode ? 'secondary' : 'inherit',\n          disabled: activeTab === 0,\n          children: bulkMode ? 'Esci Selezione' : 'Selezione Multipla'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 913,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 912,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 2,\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          fullWidth: true,\n          variant: \"outlined\",\n          startIcon: /*#__PURE__*/_jsxDEV(ExportIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 929,\n            columnNumber: 24\n          }, this),\n          onClick: handleExportAll,\n          disabled: activeTab === 0 || filteredCertificazioni.length === 0,\n          children: activeTab === 0 ? 'Export (solo certificazioni)' : 'Esporta Certificazioni'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 926,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 925,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 2,\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          fullWidth: true,\n          variant: \"contained\",\n          startIcon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 941,\n            columnNumber: 24\n          }, this),\n          onClick: openCreateDialog,\n          children: \"Nuova Certificazione\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 938,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 937,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 876,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Collapse, {\n      in: advancedFiltersOpen,\n      children: [/*#__PURE__*/_jsxDEV(Divider, {\n        sx: {\n          my: 2\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 951,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body2\",\n        color: \"text.secondary\",\n        sx: {\n          mb: 2\n        },\n        children: activeTab === 0 ? 'Filtri per Cavi' : 'Filtri per Certificazioni'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 952,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 2,\n        children: [activeTab === 0 && /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(FormControl, {\n              fullWidth: true,\n              size: \"small\",\n              children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                children: \"Stato Installazione\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 962,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Select, {\n                value: filters.stato,\n                onChange: e => setFilters({\n                  ...filters,\n                  stato: e.target.value\n                }),\n                children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"\",\n                  children: \"Tutti\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 967,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"INSTALLATO\",\n                  children: \"Installato\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 968,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"NON_INSTALLATO\",\n                  children: \"Non Installato\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 969,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"IN_CORSO\",\n                  children: \"In Corso\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 970,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 963,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 961,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 960,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(FormControl, {\n              fullWidth: true,\n              size: \"small\",\n              children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                children: \"Tipologia\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 977,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Select, {\n                value: filters.tipologia,\n                onChange: e => setFilters({\n                  ...filters,\n                  tipologia: e.target.value\n                }),\n                children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"\",\n                  children: \"Tutte\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 982,\n                  columnNumber: 21\n                }, this), [...new Set(cavi.map(c => c.tipologia))].filter(Boolean).map(tip => /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: tip,\n                  children: tip\n                }, tip, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 984,\n                  columnNumber: 23\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 978,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 976,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 975,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(FormControl, {\n              fullWidth: true,\n              size: \"small\",\n              children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                children: \"Stato Certificazione\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 992,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Select, {\n                value: filters.certificazione,\n                onChange: e => setFilters({\n                  ...filters,\n                  certificazione: e.target.value\n                }),\n                children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"\",\n                  children: \"Tutti\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 997,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"CERTIFICATO\",\n                  children: \"Certificato\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 998,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"NON_CERTIFICATO\",\n                  children: \"Non Certificato\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 999,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 993,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 991,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 990,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true), activeTab === 1 && /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(FormControl, {\n              fullWidth: true,\n              size: \"small\",\n              children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                children: \"Operatore\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1011,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Select, {\n                value: filters.operatore,\n                onChange: e => setFilters({\n                  ...filters,\n                  operatore: e.target.value\n                }),\n                children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"\",\n                  children: \"Tutti\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1016,\n                  columnNumber: 21\n                }, this), [...new Set(certificazioni.map(c => c.operatore))].filter(Boolean).map(op => /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: op,\n                  children: op\n                }, op, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1018,\n                  columnNumber: 23\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1012,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1010,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1009,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(FormControl, {\n              fullWidth: true,\n              size: \"small\",\n              children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                children: \"Risultato Test\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1026,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Select, {\n                value: filters.risultatoTest,\n                onChange: e => setFilters({\n                  ...filters,\n                  risultatoTest: e.target.value\n                }),\n                children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"\",\n                  children: \"Tutti\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1031,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"CONFORME\",\n                  children: \"Conforme\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1032,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"NON_CONFORME\",\n                  children: \"Non Conforme\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1033,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"DA_VERIFICARE\",\n                  children: \"Da Verificare\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1034,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1027,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1025,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1024,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              size: \"small\",\n              label: \"Isolamento Min (M\\u03A9)\",\n              type: \"number\",\n              value: filters.valoreIsolamento,\n              onChange: e => setFilters({\n                ...filters,\n                valoreIsolamento: e.target.value\n              }),\n              placeholder: \"es. 500\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1040,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1039,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              size: \"small\",\n              label: \"Data Inizio\",\n              type: \"date\",\n              value: filters.dataInizio,\n              onChange: e => setFilters({\n                ...filters,\n                dataInizio: e.target.value\n              }),\n              InputLabelProps: {\n                shrink: true\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1052,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1051,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              size: \"small\",\n              label: \"Data Fine\",\n              type: \"date\",\n              value: filters.dataFine,\n              onChange: e => setFilters({\n                ...filters,\n                dataFine: e.target.value\n              }),\n              InputLabelProps: {\n                shrink: true\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1064,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1063,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          children: /*#__PURE__*/_jsxDEV(Stack, {\n            direction: \"row\",\n            spacing: 1,\n            justifyContent: \"flex-end\",\n            children: /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"outlined\",\n              size: \"small\",\n              onClick: () => setFilters({\n                stato: '',\n                tipologia: '',\n                operatore: '',\n                dataInizio: '',\n                dataFine: '',\n                valoreIsolamento: '',\n                risultatoTest: '',\n                strumento: '',\n                certificazione: ''\n              }),\n              children: \"Pulisci Tutti i Filtri\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1079,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1078,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1077,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 956,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 950,\n      columnNumber: 7\n    }, this), bulkMode && bulkSelection.length > 0 && /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(Divider, {\n        sx: {\n          my: 2\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1098,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Stack, {\n        direction: \"row\",\n        spacing: 2,\n        alignItems: \"center\",\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          children: [bulkSelection.length, \" elementi selezionati\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1100,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          size: \"small\",\n          variant: \"outlined\",\n          onClick: selectAllItems,\n          children: \"Seleziona Tutto\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1103,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          size: \"small\",\n          variant: \"outlined\",\n          onClick: clearSelection,\n          children: \"Deseleziona\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1110,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          size: \"small\",\n          variant: \"outlined\",\n          startIcon: /*#__PURE__*/_jsxDEV(ExportIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1120,\n            columnNumber: 26\n          }, this),\n          onClick: handleBulkExport,\n          children: \"Esporta Selezionati\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1117,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          size: \"small\",\n          variant: \"outlined\",\n          color: \"error\",\n          startIcon: /*#__PURE__*/_jsxDEV(DeleteIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1129,\n            columnNumber: 26\n          }, this),\n          onClick: handleBulkDelete,\n          children: \"Elimina Selezionati\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1125,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1099,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 875,\n    columnNumber: 5\n  }, this);\n\n  // Renderizza la tabella dei cavi\n  const renderCaviTable = () => {\n    const currentItems = getCurrentPageItems(filteredCavi);\n    if (filteredCavi.length === 0) {\n      return /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"info\",\n        children: searchTerm || filters.stato || filters.tipologia ? 'Nessun cavo trovato con i filtri applicati' : 'Nessun cavo disponibile'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1146,\n        columnNumber: 9\n      }, this);\n    }\n    return /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(TableContainer, {\n        component: Paper,\n        children: /*#__PURE__*/_jsxDEV(Table, {\n          size: \"small\",\n          children: [/*#__PURE__*/_jsxDEV(TableHead, {\n            children: /*#__PURE__*/_jsxDEV(TableRow, {\n              children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"ID Cavo\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1160,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Tipologia\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1161,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Sezione\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1162,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Partenza\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1163,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Arrivo\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1164,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Metri\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1165,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Stato\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1166,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Collegamenti\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1167,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Certificato\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1168,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Azioni\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1169,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1159,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1158,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n            children: currentItems.map(cavo => {\n              const isCertificato = isCavoCertificato(cavo.id_cavo);\n              const puoCertificare = puoEssereCertificato(cavo);\n              const messaggioErrore = !puoCertificare ? getMessaggioErroreCertificazione(cavo) : '';\n              return /*#__PURE__*/_jsxDEV(TableRow, {\n                children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                  children: /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    fontWeight: \"medium\",\n                    children: cavo.id_cavo\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1181,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1180,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: cavo.tipologia\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1185,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: cavo.sezione\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1186,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: cavo.ubicazione_partenza\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1187,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: cavo.ubicazione_arrivo\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1188,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: [cavo.metratura_reale || cavo.metri_teorici, \" m\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1189,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: /*#__PURE__*/_jsxDEV(Chip, {\n                    size: \"small\",\n                    label: cavo.stato_installazione,\n                    color: cavo.stato_installazione === 'INSTALLATO' ? 'success' : 'default'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1191,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1190,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: (() => {\n                    const collegamenti = cavo.collegamenti || 0;\n                    const statoCollegamento = collegamenti === 0 ? 'Non collegato' : collegamenti === 1 ? 'Solo partenza' : collegamenti === 2 ? 'Solo arrivo' : collegamenti === 3 ? 'Completo' : 'Sconosciuto';\n                    const colore = collegamenti === 3 ? 'success' : collegamenti === 0 ? 'error' : 'warning';\n                    return /*#__PURE__*/_jsxDEV(Tooltip, {\n                      title: `Partenza: ${cavo.responsabile_partenza || 'Non collegato'} | Arrivo: ${cavo.responsabile_arrivo || 'Non collegato'}`,\n                      children: /*#__PURE__*/_jsxDEV(Chip, {\n                        size: \"small\",\n                        label: statoCollegamento,\n                        color: colore,\n                        icon: collegamenti === 3 ? /*#__PURE__*/_jsxDEV(CheckIcon, {}, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1214,\n                          columnNumber: 58\n                        }, this) : /*#__PURE__*/_jsxDEV(WarningIcon, {}, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1214,\n                          columnNumber: 74\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1210,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1209,\n                      columnNumber: 27\n                    }, this);\n                  })()\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1197,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: isCertificato ? /*#__PURE__*/_jsxDEV(Chip, {\n                    size: \"small\",\n                    icon: /*#__PURE__*/_jsxDEV(CheckIcon, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1224,\n                      columnNumber: 33\n                    }, this),\n                    label: \"Certificato\",\n                    color: \"success\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1222,\n                    columnNumber: 25\n                  }, this) : /*#__PURE__*/_jsxDEV(Chip, {\n                    size: \"small\",\n                    icon: /*#__PURE__*/_jsxDEV(WarningIcon, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1231,\n                      columnNumber: 33\n                    }, this),\n                    label: \"Non certificato\",\n                    color: \"warning\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1229,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1220,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: isCertificato ? /*#__PURE__*/_jsxDEV(Tooltip, {\n                    title: \"Cavo gi\\xE0 certificato\",\n                    children: /*#__PURE__*/_jsxDEV(Chip, {\n                      icon: /*#__PURE__*/_jsxDEV(CheckIcon, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1241,\n                        columnNumber: 35\n                      }, this),\n                      label: \"Certificato\",\n                      color: \"success\",\n                      size: \"small\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1240,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1239,\n                    columnNumber: 25\n                  }, this) : puoCertificare ? /*#__PURE__*/_jsxDEV(Tooltip, {\n                    title: \"Crea certificazione per questo cavo\",\n                    children: /*#__PURE__*/_jsxDEV(IconButton, {\n                      size: \"small\",\n                      onClick: () => openCreateDialog(cavo),\n                      color: \"primary\",\n                      children: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1254,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1249,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1248,\n                    columnNumber: 25\n                  }, this) : /*#__PURE__*/_jsxDEV(Tooltip, {\n                    title: messaggioErrore,\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: /*#__PURE__*/_jsxDEV(IconButton, {\n                        size: \"small\",\n                        disabled: true,\n                        onClick: () => showSnackbar(messaggioErrore, 'warning'),\n                        children: /*#__PURE__*/_jsxDEV(BlockIcon, {}, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1265,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1260,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1259,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1258,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1237,\n                  columnNumber: 21\n                }, this)]\n              }, cavo.id_cavo, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1179,\n                columnNumber: 19\n              }, this);\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1172,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1157,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1156,\n        columnNumber: 9\n      }, this), getTotalPages(filteredCavi) > 1 && /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          justifyContent: 'center',\n          mt: 2\n        },\n        children: /*#__PURE__*/_jsxDEV(Pagination, {\n          count: getTotalPages(filteredCavi),\n          page: currentPage,\n          onChange: (event, value) => setCurrentPage(value),\n          color: \"primary\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1280,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1279,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true);\n  };\n\n  // Renderizza la tabella delle certificazioni\n  const renderCertificazioniTable = () => {\n    const currentItems = getCurrentPageItems(filteredCertificazioni);\n    if (filteredCertificazioni.length === 0) {\n      return /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"info\",\n        children: searchTerm || filters.operatore ? 'Nessuna certificazione trovata con i filtri applicati' : 'Nessuna certificazione disponibile'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1298,\n        columnNumber: 9\n      }, this);\n    }\n    return /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(TableContainer, {\n        component: Paper,\n        children: /*#__PURE__*/_jsxDEV(Table, {\n          size: \"small\",\n          children: [/*#__PURE__*/_jsxDEV(TableHead, {\n            children: /*#__PURE__*/_jsxDEV(TableRow, {\n              children: [bulkMode && /*#__PURE__*/_jsxDEV(TableCell, {\n                padding: \"checkbox\",\n                children: /*#__PURE__*/_jsxDEV(IconButton, {\n                  size: \"small\",\n                  onClick: bulkSelection.length === filteredCertificazioni.length ? clearSelection : selectAllItems,\n                  children: bulkSelection.length === filteredCertificazioni.length ? /*#__PURE__*/_jsxDEV(ClearIcon, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1318,\n                    columnNumber: 81\n                  }, this) : /*#__PURE__*/_jsxDEV(CheckIcon, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1318,\n                    columnNumber: 97\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1314,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1313,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: /*#__PURE__*/_jsxDEV(Stack, {\n                  direction: \"row\",\n                  alignItems: \"center\",\n                  spacing: 1,\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    fontWeight: \"bold\",\n                    children: \"N\\xB0 Certificato\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1324,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n                    size: \"small\",\n                    onClick: () => {\n                      setSortBy('numero_certificato');\n                      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');\n                    },\n                    children: sortBy === 'numero_certificato' ? sortOrder === 'asc' ? /*#__PURE__*/_jsxDEV(ExpandLessIcon, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1329,\n                      columnNumber: 81\n                    }, this) : /*#__PURE__*/_jsxDEV(ExpandMoreIcon, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1329,\n                      columnNumber: 102\n                    }, this) : /*#__PURE__*/_jsxDEV(ExpandMoreIcon, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1329,\n                      columnNumber: 124\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1325,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1323,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1322,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"ID Cavo\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1333,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: /*#__PURE__*/_jsxDEV(Stack, {\n                  direction: \"row\",\n                  alignItems: \"center\",\n                  spacing: 1,\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    fontWeight: \"bold\",\n                    children: \"Data\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1336,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n                    size: \"small\",\n                    onClick: () => {\n                      setSortBy('data_certificazione');\n                      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');\n                    },\n                    children: sortBy === 'data_certificazione' ? sortOrder === 'asc' ? /*#__PURE__*/_jsxDEV(ExpandLessIcon, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1341,\n                      columnNumber: 82\n                    }, this) : /*#__PURE__*/_jsxDEV(ExpandMoreIcon, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1341,\n                      columnNumber: 103\n                    }, this) : /*#__PURE__*/_jsxDEV(ExpandMoreIcon, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1341,\n                      columnNumber: 125\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1337,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1335,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1334,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Operatore\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1345,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Strumento\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1346,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Lunghezza\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1347,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Isolamento\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1348,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Risultato\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1349,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Azioni\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1350,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1311,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1310,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n            children: currentItems.map(cert => /*#__PURE__*/_jsxDEV(TableRow, {\n              selected: bulkSelection.includes(cert.id_certificazione),\n              hover: true,\n              children: [bulkMode && /*#__PURE__*/_jsxDEV(TableCell, {\n                padding: \"checkbox\",\n                children: /*#__PURE__*/_jsxDEV(IconButton, {\n                  size: \"small\",\n                  onClick: () => toggleItemSelection(cert.id_certificazione),\n                  color: bulkSelection.includes(cert.id_certificazione) ? 'primary' : 'default',\n                  children: bulkSelection.includes(cert.id_certificazione) ? /*#__PURE__*/_jsxDEV(CheckIcon, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1367,\n                    columnNumber: 75\n                  }, this) : /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1367,\n                    columnNumber: 91\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1362,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1361,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  fontWeight: \"medium\",\n                  children: cert.numero_certificato\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1372,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1371,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: /*#__PURE__*/_jsxDEV(Chip, {\n                  size: \"small\",\n                  label: cert.id_cavo,\n                  variant: \"outlined\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1377,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1376,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: new Date(cert.data_certificazione).toLocaleDateString()\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1379,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: /*#__PURE__*/_jsxDEV(Stack, {\n                  direction: \"row\",\n                  alignItems: \"center\",\n                  spacing: 1,\n                  children: [/*#__PURE__*/_jsxDEV(PersonIcon, {\n                    fontSize: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1382,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    children: cert.operatore || cert.id_operatore\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1383,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1381,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1380,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  children: cert.id_strumento ? (() => {\n                    const strumento = strumenti.find(s => s.id_strumento === cert.id_strumento);\n                    return strumento ? `${strumento.nome} - ${strumento.marca}` : 'Strumento non trovato';\n                  })() : cert.strumento_utilizzato || 'N/A'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1387,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1386,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  children: [cert.lunghezza_misurata, \" m\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1398,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1397,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: /*#__PURE__*/_jsxDEV(Chip, {\n                  size: \"small\",\n                  label: `${cert.valore_isolamento} MΩ`,\n                  color: parseFloat(cert.valore_isolamento) >= 500 ? 'success' : 'warning',\n                  icon: parseFloat(cert.valore_isolamento) >= 500 ? /*#__PURE__*/_jsxDEV(CheckIcon, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1405,\n                    columnNumber: 73\n                  }, this) : /*#__PURE__*/_jsxDEV(WarningIcon, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1405,\n                    columnNumber: 89\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1401,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1400,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: /*#__PURE__*/_jsxDEV(Chip, {\n                  size: \"small\",\n                  label: cert.risultato_finale || 'CONFORME',\n                  color: cert.risultato_finale === 'CONFORME' ? 'success' : cert.risultato_finale === 'NON_CONFORME' ? 'error' : 'warning'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1409,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1408,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: /*#__PURE__*/_jsxDEV(Stack, {\n                  direction: \"row\",\n                  spacing: 0.5,\n                  children: [/*#__PURE__*/_jsxDEV(Tooltip, {\n                    title: \"Visualizza dettagli\",\n                    children: /*#__PURE__*/_jsxDEV(IconButton, {\n                      size: \"small\",\n                      onClick: () => {\n                        setSelectedItem(cert);\n                        setDialogType('view');\n                        setOpenDialog(true);\n                      },\n                      children: /*#__PURE__*/_jsxDEV(ViewIcon, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1426,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1418,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1417,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                    title: \"Genera PDF\",\n                    children: /*#__PURE__*/_jsxDEV(IconButton, {\n                      size: \"small\",\n                      onClick: () => handleGeneratePdf(cert),\n                      disabled: operationInProgress,\n                      children: /*#__PURE__*/_jsxDEV(PdfIcon, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1435,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1430,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1429,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                    title: \"Elimina\",\n                    children: /*#__PURE__*/_jsxDEV(IconButton, {\n                      size: \"small\",\n                      color: \"error\",\n                      onClick: () => handleDeleteCertificazione(cert),\n                      disabled: operationInProgress,\n                      children: /*#__PURE__*/_jsxDEV(DeleteIcon, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1445,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1439,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1438,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1416,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1415,\n                columnNumber: 19\n              }, this)]\n            }, cert.id_certificazione, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1355,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1353,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1309,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1308,\n        columnNumber: 9\n      }, this), getTotalPages(filteredCertificazioni) > 1 && /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          justifyContent: 'center',\n          mt: 2\n        },\n        children: /*#__PURE__*/_jsxDEV(Pagination, {\n          count: getTotalPages(filteredCertificazioni),\n          page: currentPage,\n          onChange: (event, value) => setCurrentPage(value),\n          color: \"primary\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1458,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1457,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true);\n  };\n\n  // Renderizza il dialog per creare/modificare certificazione\n  const renderCertificazioneDialog = () => {\n    if (dialogType !== 'create' && dialogType !== 'edit') return null;\n    return /*#__PURE__*/_jsxDEV(Dialog, {\n      open: openDialog,\n      onClose: closeDialog,\n      maxWidth: \"md\",\n      fullWidth: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: dialogType === 'create' ? 'Nuova Certificazione' : 'Modifica Certificazione'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1476,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 2,\n          sx: {\n            mt: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(Autocomplete, {\n              options: cavi.filter(cavo => {\n                // Mostra solo cavi che possono essere certificati o quello già selezionato\n                const isSelected = cavo.id_cavo === formData.id_cavo;\n                const isNotCertified = !certificazioni.some(cert => cert.id_cavo === cavo.id_cavo);\n                const canBeCertified = puoEssereCertificato(cavo);\n                return isSelected || isNotCertified && canBeCertified;\n              }),\n              getOptionLabel: option => `${option.id_cavo} - ${option.tipologia}`,\n              value: cavi.find(c => c.id_cavo === formData.id_cavo) || null,\n              onChange: (event, newValue) => {\n                if (newValue) {\n                  handleCavoSelect(newValue);\n                } else {\n                  setFormData(prev => ({\n                    ...prev,\n                    id_cavo: '',\n                    lunghezza_misurata: ''\n                  }));\n                }\n              },\n              renderInput: params => /*#__PURE__*/_jsxDEV(TextField, {\n                ...params,\n                label: \"Cavo *\",\n                placeholder: \"Seleziona un cavo posato\",\n                required: true,\n                helperText: \"Solo cavi posati/installati (il collegamento pu\\xF2 essere gestito al momento)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1501,\n                columnNumber: 19\n              }, this),\n              renderOption: (props, option) => {\n                const collegamenti = option.collegamenti || 0;\n                const isCollegato = collegamenti === 3;\n                return /*#__PURE__*/_jsxDEV(Box, {\n                  component: \"li\",\n                  ...props,\n                  children: /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      width: '100%'\n                    },\n                    children: /*#__PURE__*/_jsxDEV(Stack, {\n                      direction: \"row\",\n                      justifyContent: \"space-between\",\n                      alignItems: \"center\",\n                      children: [/*#__PURE__*/_jsxDEV(Box, {\n                        children: [/*#__PURE__*/_jsxDEV(Typography, {\n                          variant: \"body2\",\n                          fontWeight: \"medium\",\n                          children: option.id_cavo\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1518,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                          variant: \"caption\",\n                          color: \"text.secondary\",\n                          children: [option.tipologia, \" - \", option.ubicazione_partenza, \" \\u2192 \", option.ubicazione_arrivo]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1521,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1517,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(Stack, {\n                        direction: \"row\",\n                        spacing: 1,\n                        children: [/*#__PURE__*/_jsxDEV(Chip, {\n                          size: \"small\",\n                          label: option.stato_installazione,\n                          color: option.stato_installazione === 'INSTALLATO' ? 'success' : 'default'\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1526,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                          size: \"small\",\n                          label: isCollegato ? 'Collegato' : 'Da collegare',\n                          color: isCollegato ? 'success' : 'warning',\n                          icon: isCollegato ? /*#__PURE__*/_jsxDEV(CheckIcon, {}, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1535,\n                            columnNumber: 51\n                          }, this) : /*#__PURE__*/_jsxDEV(WarningIcon, {}, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1535,\n                            columnNumber: 67\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1531,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1525,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1516,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1515,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1514,\n                  columnNumber: 21\n                }, this);\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1482,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1481,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Operatore *\",\n              value: formData.id_operatore,\n              onChange: e => handleFormChange('id_operatore', e.target.value),\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1547,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1546,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(FormControl, {\n              fullWidth: true,\n              required: true,\n              children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                children: \"Strumento *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1558,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Select, {\n                value: formData.id_strumento,\n                onChange: e => handleFormChange('id_strumento', e.target.value),\n                label: \"Strumento *\",\n                children: strumenti.map(strumento => /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: strumento.id_strumento,\n                  children: [strumento.nome, \" - \", strumento.marca, \" \", strumento.modello, \" (S/N: \", strumento.numero_serie, \")\"]\n                }, strumento.id_strumento, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1565,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1559,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1557,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1556,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Lunghezza Misurata (m) *\",\n              type: \"number\",\n              value: formData.lunghezza_misurata,\n              onChange: e => handleFormChange('lunghezza_misurata', e.target.value),\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1574,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1573,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 4,\n            children: /*#__PURE__*/_jsxDEV(FormControl, {\n              fullWidth: true,\n              children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                children: \"Continuit\\xE0\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1586,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Select, {\n                value: formData.valore_continuita,\n                onChange: e => handleFormChange('valore_continuita', e.target.value),\n                label: \"Continuit\\xE0\",\n                children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"OK\",\n                  children: \"OK\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1592,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"NOK\",\n                  children: \"NOK\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1593,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1587,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1585,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1584,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 4,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Isolamento (M\\u03A9) *\",\n              type: \"number\",\n              value: formData.valore_isolamento,\n              onChange: e => handleFormChange('valore_isolamento', e.target.value),\n              required: true,\n              helperText: \"Valore minimo consigliato: 500 M\\u03A9\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1599,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1598,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 4,\n            children: /*#__PURE__*/_jsxDEV(FormControl, {\n              fullWidth: true,\n              children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                children: \"Resistenza\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1612,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Select, {\n                value: formData.valore_resistenza,\n                onChange: e => handleFormChange('valore_resistenza', e.target.value),\n                label: \"Resistenza\",\n                children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"OK\",\n                  children: \"OK\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1618,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"NOK\",\n                  children: \"NOK\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1619,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1613,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1611,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1610,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            children: /*#__PURE__*/_jsxDEV(Divider, {\n              sx: {\n                my: 2\n              },\n              children: /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                color: \"text.secondary\",\n                children: \"Stato Collegamenti Cavo\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1627,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1626,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1625,\n            columnNumber: 13\n          }, this), formData.id_cavo && (() => {\n            const cavo = cavi.find(c => c.id_cavo === formData.id_cavo);\n            if (!cavo) return null;\n            const collegamenti = cavo.collegamenti || 0;\n            const isCollegato = collegamenti === 3;\n            return /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              children: /*#__PURE__*/_jsxDEV(Paper, {\n                sx: {\n                  p: 2,\n                  bgcolor: isCollegato ? 'success.light' : 'warning.light'\n                },\n                children: /*#__PURE__*/_jsxDEV(Stack, {\n                  direction: \"row\",\n                  alignItems: \"center\",\n                  spacing: 2,\n                  children: [isCollegato ? /*#__PURE__*/_jsxDEV(CheckIcon, {\n                    color: \"success\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1644,\n                    columnNumber: 38\n                  }, this) : /*#__PURE__*/_jsxDEV(WarningIcon, {\n                    color: \"warning\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1644,\n                    columnNumber: 70\n                  }, this), /*#__PURE__*/_jsxDEV(Box, {\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      fontWeight: \"bold\",\n                      children: isCollegato ? 'Cavo Completamente Collegato' : 'Cavo Non Completamente Collegato'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1646,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"caption\",\n                      children: [\"Stato: \", collegamenti === 0 ? 'Non collegato' : collegamenti === 1 ? 'Solo partenza collegata' : collegamenti === 2 ? 'Solo arrivo collegato' : collegamenti === 3 ? 'Completamente collegato' : 'Stato sconosciuto']\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1649,\n                      columnNumber: 25\n                    }, this), !isCollegato && /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"caption\",\n                      display: \"block\",\n                      sx: {\n                        mt: 1\n                      },\n                      children: \"\\u26A0\\uFE0F Il cavo pu\\xF2 essere certificato ma ricorda di completare i collegamenti prima della messa in servizio\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1657,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1645,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1643,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1642,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1641,\n              columnNumber: 17\n            }, this);\n          })(), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            children: /*#__PURE__*/_jsxDEV(Divider, {\n              sx: {\n                my: 2\n              },\n              children: /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                color: \"text.secondary\",\n                children: \"Parametri Ambientali e Test Avanzati\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1671,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1670,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1669,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Temperatura Ambiente (\\xB0C)\",\n              type: \"number\",\n              value: formData.temperatura_ambiente,\n              onChange: e => handleFormChange('temperatura_ambiente', e.target.value),\n              helperText: \"Temperatura durante il test\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1678,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1677,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Umidit\\xE0 (%)\",\n              type: \"number\",\n              value: formData.umidita,\n              onChange: e => handleFormChange('umidita', e.target.value),\n              helperText: \"Umidit\\xE0 relativa\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1689,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1688,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Tensione di Prova (V)\",\n              type: \"number\",\n              value: formData.tensione_prova,\n              onChange: e => handleFormChange('tensione_prova', e.target.value),\n              helperText: \"Tensione applicata per il test\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1700,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1699,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Durata Prova (min)\",\n              type: \"number\",\n              value: formData.durata_prova,\n              onChange: e => handleFormChange('durata_prova', e.target.value),\n              helperText: \"Durata del test in minuti\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1711,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1710,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(FormControl, {\n              fullWidth: true,\n              children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                children: \"Risultato Finale\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1723,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Select, {\n                value: formData.risultato_finale,\n                onChange: e => handleFormChange('risultato_finale', e.target.value),\n                label: \"Risultato Finale\",\n                children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"CONFORME\",\n                  children: /*#__PURE__*/_jsxDEV(Stack, {\n                    direction: \"row\",\n                    alignItems: \"center\",\n                    spacing: 1,\n                    children: [/*#__PURE__*/_jsxDEV(CheckIcon, {\n                      color: \"success\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1731,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      children: \"Conforme\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1732,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1730,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1729,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"NON_CONFORME\",\n                  children: /*#__PURE__*/_jsxDEV(Stack, {\n                    direction: \"row\",\n                    alignItems: \"center\",\n                    spacing: 1,\n                    children: [/*#__PURE__*/_jsxDEV(ErrorIcon, {\n                      color: \"error\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1737,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      children: \"Non Conforme\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1738,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1736,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1735,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"DA_VERIFICARE\",\n                  children: /*#__PURE__*/_jsxDEV(Stack, {\n                    direction: \"row\",\n                    alignItems: \"center\",\n                    spacing: 1,\n                    children: [/*#__PURE__*/_jsxDEV(WarningIcon, {\n                      color: \"warning\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1743,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      children: \"Da Verificare\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1744,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1742,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1741,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1724,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1722,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1721,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Note\",\n              multiline: true,\n              rows: 3,\n              value: formData.note,\n              onChange: e => handleFormChange('note', e.target.value),\n              placeholder: \"Inserisci eventuali note, osservazioni o anomalie riscontrate durante il test...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1752,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1751,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1480,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1479,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: closeDialog,\n          children: \"Annulla\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1765,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleCreateCertificazione,\n          variant: \"contained\",\n          disabled: loading || !formData.id_cavo || !formData.id_operatore || !formData.id_strumento || !formData.valore_isolamento,\n          startIcon: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n            size: 20\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1770,\n            columnNumber: 34\n          }, this) : /*#__PURE__*/_jsxDEV(SaveIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1770,\n            columnNumber: 67\n          }, this),\n          children: dialogType === 'create' ? 'Crea Certificazione' : 'Salva Modifiche'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1766,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1764,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1475,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Renderizza il dialog di visualizzazione dettagli\n  const renderViewDialog = () => {\n    if (dialogType !== 'view' || !selectedItem) return null;\n    return /*#__PURE__*/_jsxDEV(Dialog, {\n      open: openDialog,\n      onClose: closeDialog,\n      maxWidth: \"md\",\n      fullWidth: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: [\"Dettagli Certificazione - \", selectedItem.numero_certificato]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1785,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 2,\n          sx: {\n            mt: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              variant: \"outlined\",\n              children: /*#__PURE__*/_jsxDEV(CardContent, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  gutterBottom: true,\n                  children: \"Informazioni Cavo\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1793,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  children: [\"ID Cavo: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: selectedItem.id_cavo\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1797,\n                    columnNumber: 30\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1796,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  children: [\"Lunghezza Misurata: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: [selectedItem.lunghezza_misurata, \" m\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1800,\n                    columnNumber: 41\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1799,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1792,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1791,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1790,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              variant: \"outlined\",\n              children: /*#__PURE__*/_jsxDEV(CardContent, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  gutterBottom: true,\n                  children: \"Informazioni Certificazione\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1809,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  children: [\"Numero: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: selectedItem.numero_certificato\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1813,\n                    columnNumber: 29\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1812,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  children: [\"Data: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: new Date(selectedItem.data_certificazione).toLocaleDateString()\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1816,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1815,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  children: [\"Operatore: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: selectedItem.operatore || selectedItem.id_operatore\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1819,\n                    columnNumber: 32\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1818,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1808,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1807,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1806,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              variant: \"outlined\",\n              children: /*#__PURE__*/_jsxDEV(CardContent, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  gutterBottom: true,\n                  children: \"Risultati Test\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1828,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                  container: true,\n                  spacing: 2,\n                  children: [/*#__PURE__*/_jsxDEV(Grid, {\n                    item: true,\n                    xs: 4,\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      color: \"text.secondary\",\n                      children: \"Continuit\\xE0\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1833,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                      size: \"small\",\n                      label: selectedItem.valore_continuita,\n                      color: selectedItem.valore_continuita === 'OK' ? 'success' : 'error'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1836,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1832,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                    item: true,\n                    xs: 4,\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      color: \"text.secondary\",\n                      children: \"Isolamento\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1843,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                      size: \"small\",\n                      label: `${selectedItem.valore_isolamento} MΩ`,\n                      color: parseFloat(selectedItem.valore_isolamento) >= 500 ? 'success' : 'warning'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1846,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1842,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                    item: true,\n                    xs: 4,\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      color: \"text.secondary\",\n                      children: \"Resistenza\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1853,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                      size: \"small\",\n                      label: selectedItem.valore_resistenza,\n                      color: selectedItem.valore_resistenza === 'OK' ? 'success' : 'error'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1856,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1852,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1831,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1827,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1826,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1825,\n            columnNumber: 13\n          }, this), selectedItem.note && /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              variant: \"outlined\",\n              children: /*#__PURE__*/_jsxDEV(CardContent, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  gutterBottom: true,\n                  children: \"Note\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1871,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  children: selectedItem.note\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1874,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1870,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1869,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1868,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1789,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1788,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: closeDialog,\n          children: \"Chiudi\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1884,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: () => handleGeneratePdf(selectedItem),\n          variant: \"contained\",\n          startIcon: /*#__PURE__*/_jsxDEV(PdfIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1888,\n            columnNumber: 24\n          }, this),\n          disabled: loading,\n          children: \"Genera PDF\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1885,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1883,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1784,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Renderizza le statistiche\n  const renderStats = () => {\n    const totalCavi = cavi.length;\n    const caviInstallati = cavi.filter(c => c.stato_installazione === 'INSTALLATO').length;\n    const caviCertificati = certificazioni.length;\n    const percentualeCertificazione = totalCavi > 0 ? Math.round(caviCertificati / caviInstallati * 100) : 0;\n    return /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 2,\n      sx: {\n        mb: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              color: \"text.secondary\",\n              gutterBottom: true,\n              children: \"Cavi Totali\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1910,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h4\",\n              children: totalCavi\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1913,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1909,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1908,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1907,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              color: \"text.secondary\",\n              gutterBottom: true,\n              children: \"Cavi Installati\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1922,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h4\",\n              children: caviInstallati\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1925,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1921,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1920,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1919,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              color: \"text.secondary\",\n              gutterBottom: true,\n              children: \"Certificazioni\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1934,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h4\",\n              children: caviCertificati\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1937,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1933,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1932,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1931,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              color: \"text.secondary\",\n              gutterBottom: true,\n              children: \"% Certificazione\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1946,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h4\",\n              color: percentualeCertificazione >= 80 ? 'success.main' : 'warning.main',\n              children: [percentualeCertificazione, \"%\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1949,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1945,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1944,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1943,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1906,\n      columnNumber: 7\n    }, this);\n  };\n  return /*#__PURE__*/_jsxDEV(Container, {\n    maxWidth: \"xl\",\n    sx: {\n      py: 3\n    },\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mb: 4\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h4\",\n        component: \"h1\",\n        gutterBottom: true,\n        fontWeight: \"bold\",\n        children: \"\\uD83D\\uDD0C Sistema di Certificazione Cavi\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1963,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body1\",\n        color: \"text.secondary\",\n        sx: {\n          mb: 2\n        },\n        children: \"Gestione completa delle certificazioni elettriche secondo standard CEI 64-8\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1966,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 2,\n          bgcolor: 'info.light',\n          color: 'info.contrastText',\n          mb: 2\n        },\n        children: /*#__PURE__*/_jsxDEV(Stack, {\n          direction: \"row\",\n          alignItems: \"center\",\n          spacing: 2,\n          children: [/*#__PURE__*/_jsxDEV(InfoIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1973,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              fontWeight: \"bold\",\n              children: \"Come utilizzare il sistema:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1975,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              children: [\"\\u2022 \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Tab \\\"Cavi da Certificare\\\":\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1979,\n                columnNumber: 19\n              }, this), \" Visualizza tutti i cavi posati e clicca sul \\\"+\\\" per certificare \\u2022 \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Certificazione:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1980,\n                columnNumber: 19\n              }, this), \" Richiede solo che il cavo sia posato, il collegamento pu\\xF2 essere gestito al momento \\u2022 \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Tab \\\"Certificazioni Completate\\\":\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1981,\n                columnNumber: 19\n              }, this), \" Gestisci le certificazioni esistenti, attiva la \\\"Selezione\\\" per operazioni multiple \\u2022 \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Filtri:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1982,\n                columnNumber: 19\n              }, this), \" Usa i filtri specifici per ogni tab per trovare rapidamente quello che cerchi\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1978,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1974,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1972,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1971,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1962,\n      columnNumber: 7\n    }, this), renderDashboard(), (loading || operationInProgress) && /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mb: 2\n      },\n      children: [/*#__PURE__*/_jsxDEV(LinearProgress, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1995,\n        columnNumber: 11\n      }, this), progress > 0 && /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"caption\",\n        color: \"text.secondary\",\n        sx: {\n          mt: 1\n        },\n        children: [\"Caricamento... \", progress, \"%\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1997,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1994,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        mb: 3\n      },\n      children: /*#__PURE__*/_jsxDEV(Tabs, {\n        value: activeTab,\n        onChange: handleTabChange,\n        indicatorColor: \"primary\",\n        textColor: \"primary\",\n        variant: \"fullWidth\",\n        children: [/*#__PURE__*/_jsxDEV(Tab, {\n          label: /*#__PURE__*/_jsxDEV(Stack, {\n            direction: \"row\",\n            alignItems: \"center\",\n            spacing: 1,\n            children: [/*#__PURE__*/_jsxDEV(CableIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2016,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                fontWeight: \"bold\",\n                children: \"Cavi da Certificare\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2018,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"caption\",\n                color: \"text.secondary\",\n                children: [filteredCavi.length, \" cavi totali\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2021,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2017,\n              columnNumber: 17\n            }, this), statistics.caviNonCertificati > 0 && /*#__PURE__*/_jsxDEV(Badge, {\n              badgeContent: statistics.caviNonCertificati,\n              color: \"warning\",\n              children: /*#__PURE__*/_jsxDEV(WarningIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2027,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2026,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2015,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2013,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Tab, {\n          label: /*#__PURE__*/_jsxDEV(Stack, {\n            direction: \"row\",\n            alignItems: \"center\",\n            spacing: 1,\n            children: [/*#__PURE__*/_jsxDEV(ScienceIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2036,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                fontWeight: \"bold\",\n                children: \"Certificazioni Completate\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2038,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"caption\",\n                color: \"text.secondary\",\n                children: [filteredCertificazioni.length, \" certificazioni\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2041,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2037,\n              columnNumber: 17\n            }, this), statistics.certificazioniOggi > 0 && /*#__PURE__*/_jsxDEV(Badge, {\n              badgeContent: statistics.certificazioniOggi,\n              color: \"success\",\n              children: /*#__PURE__*/_jsxDEV(CheckIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2047,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2046,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2035,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2033,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 2006,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 2005,\n      columnNumber: 7\n    }, this), renderSearchAndFilters(), !loading && activeTab === 0 && renderCaviTable(), !loading && activeTab === 1 && renderCertificazioniTable(), renderCertificazioneDialog(), renderViewDialog(), /*#__PURE__*/_jsxDEV(Snackbar, {\n      open: snackbar.open,\n      autoHideDuration: 6000,\n      onClose: closeSnackbar,\n      anchorOrigin: {\n        vertical: 'bottom',\n        horizontal: 'right'\n      },\n      children: /*#__PURE__*/_jsxDEV(Alert, {\n        onClose: closeSnackbar,\n        severity: snackbar.severity,\n        sx: {\n          width: '100%'\n        },\n        children: snackbar.message\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2074,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 2068,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(SpeedDial, {\n      ariaLabel: \"Azioni rapide\",\n      sx: {\n        position: 'fixed',\n        bottom: 16,\n        right: 16\n      },\n      icon: /*#__PURE__*/_jsxDEV(SpeedDialIcon, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2083,\n        columnNumber: 15\n      }, this),\n      children: [/*#__PURE__*/_jsxDEV(SpeedDialAction, {\n        icon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2086,\n          columnNumber: 17\n        }, this),\n        tooltipTitle: \"Nuova Certificazione\",\n        onClick: openCreateDialog\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2085,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(SpeedDialAction, {\n        icon: /*#__PURE__*/_jsxDEV(ExportIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2091,\n          columnNumber: 17\n        }, this),\n        tooltipTitle: \"Esporta Tutto\",\n        onClick: handleExportAll\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2090,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(SpeedDialAction, {\n        icon: /*#__PURE__*/_jsxDEV(RefreshIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2096,\n          columnNumber: 17\n        }, this),\n        tooltipTitle: \"Aggiorna Dati\",\n        onClick: loadInitialData\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2095,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(SpeedDialAction, {\n        icon: /*#__PURE__*/_jsxDEV(ReportIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2101,\n          columnNumber: 17\n        }, this),\n        tooltipTitle: \"Report Avanzato\",\n        onClick: () => showSnackbar('Funzionalità in sviluppo', 'info')\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2100,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 2080,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 1960,\n    columnNumber: 5\n  }, this);\n}, \"Y6H2QcIXYjGgUXj6IRVp5RPodNQ=\")), \"Y6H2QcIXYjGgUXj6IRVp5RPodNQ=\");\n_c2 = CertificazioneCaviImproved;\nexport default CertificazioneCaviImproved;\nvar _c, _c2;\n$RefreshReg$(_c, \"CertificazioneCaviImproved$forwardRef\");\n$RefreshReg$(_c2, \"CertificazioneCaviImproved\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "forwardRef", "useImperativeHandle", "Box", "Typography", "<PERSON><PERSON>", "Paper", "Grid", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "TextField", "FormControl", "InputLabel", "Select", "MenuItem", "Autocomplete", "CircularProgress", "<PERSON><PERSON>", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "IconButton", "Tabs", "Tab", "Pagination", "InputAdornment", "Divider", "<PERSON><PERSON>", "Chip", "<PERSON><PERSON><PERSON>", "Badge", "LinearProgress", "Collapse", "List", "ListItem", "ListItemText", "ListItemIcon", "Snackbar", "AppBar", "<PERSON><PERSON><PERSON>", "Container", "Fab", "SpeedDial", "SpeedDialAction", "SpeedDialIcon", "Add", "AddIcon", "Search", "SearchIcon", "FilterList", "FilterIcon", "PictureAsPdf", "PdfIcon", "Download", "DownloadIcon", "Visibility", "ViewIcon", "Delete", "DeleteIcon", "Edit", "EditIcon", "Save", "SaveIcon", "Clear", "ClearIcon", "Build", "BuildIcon", "CheckCircle", "CheckIcon", "Warning", "WarningIcon", "GetApp", "ExportIcon", "Print", "PrintIcon", "Email", "EmailIcon", "CloudUpload", "UploadIcon", "Assessment", "ReportIcon", "Settings", "SettingsIcon", "Refresh", "RefreshIcon", "ExpandMore", "ExpandMoreIcon", "ExpandLess", "ExpandLessIcon", "Info", "InfoIcon", "Error", "ErrorIcon", "Schedule", "ScheduleIcon", "Person", "PersonIcon", "Cable", "CableIcon", "Science", "ScienceIcon", "Block", "BlockIcon", "certificazioneService", "caviService", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "CertificazioneCaviImproved", "_s", "_c", "cantiereId", "onSuccess", "onError", "ref", "loading", "setLoading", "activeTab", "setActiveTab", "certificazioni", "setCertificazioni", "cavi", "<PERSON><PERSON><PERSON>", "strumenti", "setStrumenti", "searchTerm", "setSearchTerm", "filteredCavi", "setFilteredCavi", "filteredCertificazioni", "setFilteredCertificazioni", "advancedFiltersOpen", "setAdvancedFiltersOpen", "filters", "setFilters", "stato", "tipologia", "operatore", "dataInizio", "dataFine", "valoreIsolamento", "risultatoTest", "strumento", "certificazione", "currentPage", "setCurrentPage", "itemsPerPage", "setItemsPerPage", "sortBy", "setSortBy", "sortOrder", "setSortOrder", "openDialog", "setOpenDialog", "dialogType", "setDialogType", "selectedItem", "setSelectedItem", "bulkSelection", "setBulkSelection", "bulkMode", "setBulkMode", "snackbar", "setSnackbar", "open", "message", "severity", "progress", "setProgress", "operationInProgress", "setOperationInProgress", "formData", "setFormData", "id_cavo", "id_operatore", "id_strumento", "<PERSON><PERSON><PERSON>_misurata", "valore_continuita", "valore_isolamento", "valore_resistenza", "note", "temperatura_ambiente", "<PERSON><PERSON><PERSON>", "tensione_prova", "durata_prova", "risultato_finale", "statistics", "setStatistics", "totaleCavi", "caviCertificati", "caviNonCertificati", "percentualeCompletamento", "certificazioniOggi", "certificazioniSettimana", "loadInitialData", "filterCavi", "filterCertificazioni", "calculateStatistics", "loadCavi", "loadCertificazioni", "loadStrumenti", "error", "showSnackbar", "data", "getCertificazioni", "console", "get<PERSON><PERSON>", "getStrumenti", "length", "Math", "round", "oggi", "Date", "toDateString", "filter", "cert", "data_certificazione", "unaSettimanaFa", "setDate", "getDate", "caviCertificabili", "cavo", "puoEssereCertificato", "caviNonCertificabili", "caviCollegati", "isCavoCollegato", "closeSnackbar", "filtered", "searchLower", "toLowerCase", "_cavo$tipologia", "_cavo$ubicazione_part", "_cavo$ubicazione_arri", "_cavo$sezione", "_cavo$utility", "includes", "ubicazione_partenza", "ubicazione_arrivo", "sezione", "utility", "stato_installazione", "some", "sort", "a", "b", "aValue", "bValue", "_cert$operatore", "_cert$numero_certific", "_cert$note", "numero_certificato", "valore", "parseFloat", "toggleBulkMode", "toggleItemSelection", "itemId", "prev", "newSelection", "id", "selectAllItems", "allIds", "map", "id_certificazione", "clearSelection", "isCavoCertificato", "idCavo", "isInstallato", "isCollegato", "colle<PERSON>nti", "hasResponsabili", "responsabile_partenza", "responsabile_arrivo", "getMessaggioErroreCertificazione", "handleTabChange", "event", "newValue", "openCreateDialog", "cavoPreselezionato", "metratura_reale", "metri_te<PERSON>ci", "closeDialog", "handleFormChange", "field", "value", "handleCavoSelect", "handleCreateCertificazione", "find", "c", "messaggio", "conferma", "window", "confirm", "createCertificazione", "handleGeneratePdf", "response", "generatePdf", "file_url", "newWindow", "link", "document", "createElement", "href", "download", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "pdf_content", "blob", "Blob", "atob", "type", "url", "URL", "createObjectURL", "revokeObjectURL", "handleDeleteCertificazione", "deleteCertificazione", "handleBulkDelete", "handleBulkExport", "<PERSON><PERSON><PERSON><PERSON>", "csv<PERSON><PERSON>nt", "generateCSV", "downloadCSV", "toISOString", "split", "headers", "rows", "toLocaleDateString", "row", "join", "content", "filename", "undefined", "setAttribute", "style", "visibility", "handleExportAll", "handleOptionSelect", "option", "getCurrentPageItems", "items", "startIndex", "endIndex", "slice", "getTotalPages", "ceil", "getUniqueValues", "array", "Set", "item", "Boolean", "renderDashboard", "sx", "p", "mb", "bgcolor", "children", "direction", "spacing", "alignItems", "justifyContent", "flexWrap", "color", "fontSize", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "fontWeight", "lineHeight", "width", "height", "borderRadius", "display", "size", "startIcon", "onClick", "ml", "renderSearchAndFilters", "container", "xs", "md", "fullWidth", "placeholder", "onChange", "e", "target", "InputProps", "startAdornment", "position", "endAdornment", "Object", "values", "f", "disabled", "in", "my", "tip", "op", "label", "InputLabelProps", "shrink", "renderCaviTable", "currentItems", "component", "isCertificato", "puoCertificare", "messaggioErrore", "statoCollegamento", "colore", "title", "icon", "mt", "count", "page", "renderCertificazioniTable", "padding", "selected", "hover", "s", "nome", "marca", "strumento_utilizzato", "renderCertificazioneDialog", "onClose", "max<PERSON><PERSON><PERSON>", "options", "isSelected", "isNotCertified", "canBeCertified", "getOptionLabel", "renderInput", "params", "required", "helperText", "renderOption", "props", "modello", "numero_serie", "multiline", "renderViewDialog", "gutterBottom", "renderStats", "totalCavi", "caviInstallati", "percentualeCertificazione", "sm", "py", "indicatorColor", "textColor", "badgeContent", "autoHideDuration", "anchor<PERSON><PERSON><PERSON>", "vertical", "horizontal", "aria<PERSON><PERSON><PERSON>", "bottom", "right", "tooltipTitle", "_c2", "$RefreshReg$"], "sources": ["C:/CMS/webapp/frontend/src/components/cavi/CertificazioneCaviImproved.js"], "sourcesContent": ["import React, { useState, useEffect, forwardRef, useImperativeHandle } from 'react';\nimport {\n  Box,\n  Typo<PERSON>,\n  Button,\n  Paper,\n  Grid,\n  Card,\n  CardContent,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  TextField,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  Autocomplete,\n  CircularProgress,\n  Alert,\n  Table,\n  TableBody,\n  TableCell,\n  TableContainer,\n  TableHead,\n  TableRow,\n  IconButton,\n  Tabs,\n  Tab,\n  Pagination,\n  InputAdornment,\n  Divider,\n  Stack,\n  Chip,\n  Tooltip,\n  Badge,\n  LinearProgress,\n  Collapse,\n  List,\n  ListItem,\n  ListItemText,\n  ListItemIcon,\n  Snackbar,\n  AppBar,\n  Toolbar,\n  Container,\n  Fab,\n  SpeedDial,\n  SpeedDialAction,\n  SpeedDialIcon\n} from '@mui/material';\nimport {\n  Add as AddIcon,\n  Search as SearchIcon,\n  FilterList as FilterIcon,\n  PictureAsPdf as PdfIcon,\n  Download as DownloadIcon,\n  Visibility as ViewIcon,\n  Delete as DeleteIcon,\n  Edit as EditIcon,\n  Save as SaveIcon,\n  Clear as ClearIcon,\n  Build as BuildIcon,\n  CheckCircle as CheckIcon,\n  Warning as WarningIcon,\n  GetApp as ExportIcon,\n  Print as PrintIcon,\n  Email as EmailIcon,\n  CloudUpload as UploadIcon,\n  Assessment as ReportIcon,\n  Settings as SettingsIcon,\n  Refresh as RefreshIcon,\n  ExpandMore as ExpandMoreIcon,\n  ExpandLess as ExpandLessIcon,\n  Info as InfoIcon,\n  Error as ErrorIcon,\n  Schedule as ScheduleIcon,\n  Person as PersonIcon,\n  Cable as CableIcon,\n  Science as ScienceIcon,\n  Block as BlockIcon\n} from '@mui/icons-material';\n\nimport certificazioneService from '../../services/certificazioneService';\nimport caviService from '../../services/caviService';\n\nconst CertificazioneCaviImproved = forwardRef(({ cantiereId, onSuccess, onError }, ref) => {\n  // Stati principali\n  const [loading, setLoading] = useState(false);\n  const [activeTab, setActiveTab] = useState(0);\n  const [certificazioni, setCertificazioni] = useState([]);\n  const [cavi, setCavi] = useState([]);\n  const [strumenti, setStrumenti] = useState([]);\n\n  // Stati per ricerca e filtri avanzati\n  const [searchTerm, setSearchTerm] = useState('');\n  const [filteredCavi, setFilteredCavi] = useState([]);\n  const [filteredCertificazioni, setFilteredCertificazioni] = useState([]);\n  const [advancedFiltersOpen, setAdvancedFiltersOpen] = useState(false);\n  const [filters, setFilters] = useState({\n    stato: '',\n    tipologia: '',\n    operatore: '',\n    dataInizio: '',\n    dataFine: '',\n    valoreIsolamento: '',\n    risultatoTest: '',\n    strumento: '',\n    certificazione: ''\n  });\n\n  // Stati per paginazione e ordinamento\n  const [currentPage, setCurrentPage] = useState(1);\n  const [itemsPerPage, setItemsPerPage] = useState(10);\n  const [sortBy, setSortBy] = useState('data_certificazione');\n  const [sortOrder, setSortOrder] = useState('desc');\n\n  // Stati per dialogs e modali\n  const [openDialog, setOpenDialog] = useState(false);\n  const [dialogType, setDialogType] = useState('');\n  const [selectedItem, setSelectedItem] = useState(null);\n  const [bulkSelection, setBulkSelection] = useState([]);\n  const [bulkMode, setBulkMode] = useState(false);\n\n  // Stati per notifiche e feedback\n  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'info' });\n  const [progress, setProgress] = useState(0);\n  const [operationInProgress, setOperationInProgress] = useState(false);\n\n  // Stati per form certificazione avanzato\n  const [formData, setFormData] = useState({\n    id_cavo: '',\n    id_operatore: '',\n    id_strumento: '',\n    lunghezza_misurata: '',\n    valore_continuita: 'OK',\n    valore_isolamento: '',\n    valore_resistenza: 'OK',\n    note: '',\n    temperatura_ambiente: '',\n    umidita: '',\n    tensione_prova: '',\n    durata_prova: '',\n    risultato_finale: 'CONFORME'\n  });\n\n  // Stati per statistiche e dashboard\n  const [statistics, setStatistics] = useState({\n    totaleCavi: 0,\n    caviCertificati: 0,\n    caviNonCertificati: 0,\n    percentualeCompletamento: 0,\n    certificazioniOggi: 0,\n    certificazioniSettimana: 0\n  });\n\n  // Carica dati iniziali\n  useEffect(() => {\n    loadInitialData();\n  }, [cantiereId]);\n\n  // Filtra cavi in base alla ricerca\n  useEffect(() => {\n    filterCavi();\n  }, [cavi, searchTerm, filters, sortBy, sortOrder]);\n\n  // Filtra certificazioni\n  useEffect(() => {\n    filterCertificazioni();\n  }, [certificazioni, searchTerm, filters, sortBy, sortOrder]);\n\n  // Ricalcola statistiche quando cambiano i dati\n  useEffect(() => {\n    calculateStatistics();\n  }, [cavi, certificazioni]);\n\n  const loadInitialData = async () => {\n    try {\n      setLoading(true);\n      setProgress(0);\n\n      // Carica dati in sequenza con progress\n      setProgress(25);\n      await loadCavi();\n\n      setProgress(50);\n      await loadCertificazioni();\n\n      setProgress(75);\n      await loadStrumenti();\n\n      setProgress(100);\n      calculateStatistics();\n\n    } catch (error) {\n      showSnackbar('Errore nel caricamento dei dati iniziali', 'error');\n      onError('Errore nel caricamento dei dati iniziali');\n    } finally {\n      setLoading(false);\n      setProgress(0);\n    }\n  };\n\n  const loadCertificazioni = async () => {\n    try {\n      const data = await certificazioneService.getCertificazioni(cantiereId);\n      setCertificazioni(data);\n      return data;\n    } catch (error) {\n      console.error('Errore nel caricamento delle certificazioni:', error);\n      throw error;\n    }\n  };\n\n  const loadCavi = async () => {\n    try {\n      const data = await caviService.getCavi(cantiereId);\n      setCavi(data);\n      return data;\n    } catch (error) {\n      console.error('Errore nel caricamento dei cavi:', error);\n      throw error;\n    }\n  };\n\n  const loadStrumenti = async () => {\n    try {\n      const data = await certificazioneService.getStrumenti(cantiereId);\n      setStrumenti(data);\n      return data;\n    } catch (error) {\n      console.error('Errore nel caricamento degli strumenti:', error);\n      throw error;\n    }\n  };\n\n  // Calcola statistiche avanzate\n  const calculateStatistics = () => {\n    const totaleCavi = cavi.length;\n    const caviCertificati = certificazioni.length;\n    const caviNonCertificati = totaleCavi - caviCertificati;\n    const percentualeCompletamento = totaleCavi > 0 ? Math.round((caviCertificati / totaleCavi) * 100) : 0;\n\n    // Calcola certificazioni di oggi\n    const oggi = new Date().toDateString();\n    const certificazioniOggi = certificazioni.filter(cert =>\n      new Date(cert.data_certificazione).toDateString() === oggi\n    ).length;\n\n    // Calcola certificazioni della settimana\n    const unaSettimanaFa = new Date();\n    unaSettimanaFa.setDate(unaSettimanaFa.getDate() - 7);\n    const certificazioniSettimana = certificazioni.filter(cert =>\n      new Date(cert.data_certificazione) >= unaSettimanaFa\n    ).length;\n\n    // Calcola cavi certificabili (solo posati/installati)\n    const caviCertificabili = cavi.filter(cavo => puoEssereCertificato(cavo)).length;\n    const caviNonCertificabili = totaleCavi - caviCertificabili;\n\n    // Calcola cavi collegati completamente\n    const caviCollegati = cavi.filter(cavo => isCavoCollegato(cavo)).length;\n\n    setStatistics({\n      totaleCavi,\n      caviCertificati,\n      caviNonCertificati,\n      caviCertificabili,\n      caviNonCertificabili,\n      caviCollegati,\n      percentualeCompletamento,\n      certificazioniOggi,\n      certificazioniSettimana\n    });\n  };\n\n  // Gestione snackbar\n  const showSnackbar = (message, severity = 'info') => {\n    setSnackbar({ open: true, message, severity });\n  };\n\n  const closeSnackbar = () => {\n    setSnackbar({ ...snackbar, open: false });\n  };\n\n  const filterCavi = () => {\n    let filtered = cavi;\n\n    // Filtro per ricerca testuale avanzata\n    if (searchTerm) {\n      const searchLower = searchTerm.toLowerCase();\n      filtered = filtered.filter(cavo =>\n        cavo.id_cavo.toLowerCase().includes(searchLower) ||\n        cavo.tipologia?.toLowerCase().includes(searchLower) ||\n        cavo.ubicazione_partenza?.toLowerCase().includes(searchLower) ||\n        cavo.ubicazione_arrivo?.toLowerCase().includes(searchLower) ||\n        cavo.sezione?.toLowerCase().includes(searchLower) ||\n        cavo.utility?.toLowerCase().includes(searchLower)\n      );\n    }\n\n    // Filtri specifici avanzati\n    if (filters.stato) {\n      filtered = filtered.filter(cavo => cavo.stato_installazione === filters.stato);\n    }\n    if (filters.tipologia) {\n      filtered = filtered.filter(cavo => cavo.tipologia === filters.tipologia);\n    }\n\n    // Filtro per stato certificazione\n    if (filters.certificazione) {\n      if (filters.certificazione === 'CERTIFICATO') {\n        filtered = filtered.filter(cavo =>\n          certificazioni.some(cert => cert.id_cavo === cavo.id_cavo)\n        );\n      } else if (filters.certificazione === 'NON_CERTIFICATO') {\n        filtered = filtered.filter(cavo =>\n          !certificazioni.some(cert => cert.id_cavo === cavo.id_cavo)\n        );\n      }\n    }\n\n    // Ordinamento\n    filtered.sort((a, b) => {\n      let aValue = a[sortBy];\n      let bValue = b[sortBy];\n\n      if (typeof aValue === 'string') {\n        aValue = aValue.toLowerCase();\n        bValue = bValue.toLowerCase();\n      }\n\n      if (sortOrder === 'asc') {\n        return aValue > bValue ? 1 : -1;\n      } else {\n        return aValue < bValue ? 1 : -1;\n      }\n    });\n\n    setFilteredCavi(filtered);\n  };\n\n  const filterCertificazioni = () => {\n    let filtered = certificazioni;\n\n    // Ricerca testuale avanzata\n    if (searchTerm) {\n      const searchLower = searchTerm.toLowerCase();\n      filtered = filtered.filter(cert =>\n        cert.id_cavo.toLowerCase().includes(searchLower) ||\n        cert.operatore?.toLowerCase().includes(searchLower) ||\n        cert.numero_certificato?.toLowerCase().includes(searchLower) ||\n        cert.note?.toLowerCase().includes(searchLower)\n      );\n    }\n\n    // Filtri avanzati\n    if (filters.operatore) {\n      filtered = filtered.filter(cert => cert.operatore === filters.operatore);\n    }\n    if (filters.strumento) {\n      filtered = filtered.filter(cert => cert.strumento === filters.strumento);\n    }\n    if (filters.risultatoTest) {\n      filtered = filtered.filter(cert => cert.risultato_finale === filters.risultatoTest);\n    }\n    if (filters.dataInizio) {\n      filtered = filtered.filter(cert =>\n        new Date(cert.data_certificazione) >= new Date(filters.dataInizio)\n      );\n    }\n    if (filters.dataFine) {\n      filtered = filtered.filter(cert =>\n        new Date(cert.data_certificazione) <= new Date(filters.dataFine)\n      );\n    }\n    if (filters.valoreIsolamento) {\n      const valore = parseFloat(filters.valoreIsolamento);\n      filtered = filtered.filter(cert =>\n        parseFloat(cert.valore_isolamento) >= valore\n      );\n    }\n\n    // Ordinamento\n    filtered.sort((a, b) => {\n      let aValue = a[sortBy];\n      let bValue = b[sortBy];\n\n      if (sortBy === 'data_certificazione') {\n        aValue = new Date(aValue);\n        bValue = new Date(bValue);\n      } else if (typeof aValue === 'string') {\n        aValue = aValue.toLowerCase();\n        bValue = bValue.toLowerCase();\n      }\n\n      if (sortOrder === 'asc') {\n        return aValue > bValue ? 1 : -1;\n      } else {\n        return aValue < bValue ? 1 : -1;\n      }\n    });\n\n    setFilteredCertificazioni(filtered);\n  };\n\n  // Gestione selezione multipla - SOLO per certificazioni\n  const toggleBulkMode = () => {\n    if (activeTab !== 1) {\n      showSnackbar('La selezione multipla è disponibile solo per le certificazioni', 'warning');\n      return;\n    }\n    setBulkMode(!bulkMode);\n    setBulkSelection([]);\n    showSnackbar(\n      !bulkMode\n        ? 'Modalità selezione attivata - Clicca sulle certificazioni per selezionarle'\n        : 'Modalità selezione disattivata',\n      'info'\n    );\n  };\n\n  const toggleItemSelection = (itemId) => {\n    setBulkSelection(prev => {\n      const newSelection = prev.includes(itemId)\n        ? prev.filter(id => id !== itemId)\n        : [...prev, itemId];\n\n      showSnackbar(\n        `${newSelection.length} certificazioni selezionate`,\n        'info'\n      );\n      return newSelection;\n    });\n  };\n\n  const selectAllItems = () => {\n    if (activeTab !== 1) return;\n\n    const allIds = filteredCertificazioni.map(cert => cert.id_certificazione);\n    setBulkSelection(allIds);\n    showSnackbar(`Tutte le ${allIds.length} certificazioni selezionate`, 'success');\n  };\n\n  const clearSelection = () => {\n    setBulkSelection([]);\n    showSnackbar('Selezione cancellata', 'info');\n  };\n\n  // Funzione per verificare se un cavo è certificato\n  const isCavoCertificato = (idCavo) => {\n    return certificazioni.some(cert => cert.id_cavo === idCavo);\n  };\n\n  // Funzione per verificare se un cavo può essere certificato\n  const puoEssereCertificato = (cavo) => {\n    // Verifica che il cavo sia installato/posato\n    const isInstallato = cavo.stato_installazione === 'Installato' ||\n                        cavo.stato_installazione === 'INSTALLATO' ||\n                        cavo.stato_installazione === 'POSATO';\n\n    // Per la certificazione basta che sia posato\n    // Il collegamento può essere gestito durante la certificazione\n    return isInstallato;\n  };\n\n  // Funzione per verificare se un cavo è completamente collegato\n  const isCavoCollegato = (cavo) => {\n    const isCollegato = cavo.collegamenti === 3;\n    const hasResponsabili = cavo.responsabile_partenza && cavo.responsabile_arrivo;\n    return isCollegato && hasResponsabili;\n  };\n\n  // Funzione per ottenere il messaggio di errore per cavi non certificabili\n  const getMessaggioErroreCertificazione = (cavo) => {\n    const isInstallato = cavo.stato_installazione === 'Installato' ||\n                        cavo.stato_installazione === 'INSTALLATO' ||\n                        cavo.stato_installazione === 'POSATO';\n\n    if (!isInstallato) {\n      return 'Il cavo deve essere posato/installato prima di poter essere certificato';\n    }\n\n    return 'Cavo non certificabile per motivi sconosciuti';\n  };\n\n  // Gestione tabs\n  const handleTabChange = (event, newValue) => {\n    setActiveTab(newValue);\n    setCurrentPage(1);\n    setSearchTerm('');\n    setFilters({ stato: '', tipologia: '', operatore: '' });\n  };\n\n  // Gestione dialogs\n  const openCreateDialog = (cavoPreselezionato = null) => {\n    setDialogType('create');\n    setSelectedItem(null);\n\n    // Se viene passato un cavo, precompila il form\n    if (cavoPreselezionato) {\n      setFormData({\n        id_cavo: cavoPreselezionato.id_cavo,\n        id_operatore: '',\n        id_strumento: '',\n        lunghezza_misurata: cavoPreselezionato.metratura_reale || cavoPreselezionato.metri_teorici || '',\n        valore_continuita: 'OK',\n        valore_isolamento: '',\n        valore_resistenza: 'OK',\n        note: '',\n        temperatura_ambiente: '',\n        umidita: '',\n        tensione_prova: '',\n        durata_prova: '',\n        risultato_finale: 'CONFORME'\n      });\n      showSnackbar(`Cavo ${cavoPreselezionato.id_cavo} selezionato automaticamente`, 'success');\n    } else {\n      // Reset form per nuova certificazione generica\n      setFormData({\n        id_cavo: '',\n        id_operatore: '',\n        id_strumento: '',\n        lunghezza_misurata: '',\n        valore_continuita: 'OK',\n        valore_isolamento: '',\n        valore_resistenza: 'OK',\n        note: '',\n        temperatura_ambiente: '',\n        umidita: '',\n        tensione_prova: '',\n        durata_prova: '',\n        risultato_finale: 'CONFORME'\n      });\n    }\n\n    setOpenDialog(true);\n  };\n\n  const closeDialog = () => {\n    setOpenDialog(false);\n    setSelectedItem(null);\n    setDialogType('');\n  };\n\n  // Gestione form\n  const handleFormChange = (field, value) => {\n    setFormData(prev => ({\n      ...prev,\n      [field]: value\n    }));\n  };\n\n  const handleCavoSelect = (cavo) => {\n    setFormData(prev => ({\n      ...prev,\n      id_cavo: cavo.id_cavo,\n      lunghezza_misurata: cavo.metratura_reale || cavo.metri_teorici || ''\n    }));\n  };\n\n  // Operazioni CRUD avanzate\n  const handleCreateCertificazione = async () => {\n    try {\n      if (!formData.id_cavo || !formData.id_operatore || !formData.id_strumento || !formData.valore_isolamento) {\n        showSnackbar('Compila tutti i campi obbligatori', 'warning');\n        return;\n      }\n\n      // Verifica che il cavo possa essere certificato\n      const cavo = cavi.find(c => c.id_cavo === formData.id_cavo);\n      if (!cavo) {\n        showSnackbar('Cavo non trovato', 'error');\n        return;\n      }\n\n      if (!puoEssereCertificato(cavo)) {\n        const messaggio = getMessaggioErroreCertificazione(cavo);\n        showSnackbar(`Impossibile certificare il cavo: ${messaggio}`, 'error');\n        return;\n      }\n\n      // Verifica che il cavo non sia già certificato\n      if (isCavoCertificato(formData.id_cavo)) {\n        showSnackbar('Il cavo è già stato certificato', 'warning');\n        return;\n      }\n\n      // Verifica se il cavo è collegato, altrimenti chiedi conferma\n      if (!isCavoCollegato(cavo)) {\n        const conferma = window.confirm(\n          `ATTENZIONE: Il cavo ${cavo.id_cavo} non risulta completamente collegato.\\n\\n` +\n          `Stato collegamenti: ${cavo.collegamenti === 0 ? 'Non collegato' :\n                                cavo.collegamenti === 1 ? 'Solo partenza collegata' :\n                                cavo.collegamenti === 2 ? 'Solo arrivo collegato' :\n                                'Stato sconosciuto'}\\n\\n` +\n          `Vuoi procedere comunque con la certificazione?\\n` +\n          `(Ricorda di completare i collegamenti prima della messa in servizio)`\n        );\n\n        if (!conferma) {\n          return;\n        }\n      }\n\n      setOperationInProgress(true);\n      await certificazioneService.createCertificazione(cantiereId, formData);\n      showSnackbar('Certificazione creata con successo', 'success');\n      closeDialog();\n      await loadCertificazioni();\n      calculateStatistics();\n    } catch (error) {\n      showSnackbar('Errore nella creazione della certificazione: ' + (error.message || 'Errore sconosciuto'), 'error');\n    } finally {\n      setOperationInProgress(false);\n    }\n  };\n\n  const handleGeneratePdf = async (certificazione) => {\n    try {\n      setOperationInProgress(true);\n      showSnackbar('Generazione PDF in corso...', 'info');\n\n      const response = await certificazioneService.generatePdf(cantiereId, certificazione.id_certificazione);\n\n      if (response.file_url) {\n        // Apri il PDF in una nuova finestra\n        const newWindow = window.open(response.file_url, '_blank');\n        if (newWindow) {\n          showSnackbar('PDF generato e aperto in una nuova finestra', 'success');\n        } else {\n          // Se il popup è bloccato, offri il download diretto\n          const link = document.createElement('a');\n          link.href = response.file_url;\n          link.download = `certificazione_${certificazione.numero_certificato}.pdf`;\n          document.body.appendChild(link);\n          link.click();\n          document.body.removeChild(link);\n          showSnackbar('PDF scaricato nella cartella Download', 'success');\n        }\n      } else if (response.pdf_content) {\n        // Se il PDF viene restituito come contenuto base64\n        const blob = new Blob([atob(response.pdf_content)], { type: 'application/pdf' });\n        const url = URL.createObjectURL(blob);\n        const link = document.createElement('a');\n        link.href = url;\n        link.download = `certificazione_${certificazione.numero_certificato}.pdf`;\n        document.body.appendChild(link);\n        link.click();\n        document.body.removeChild(link);\n        URL.revokeObjectURL(url);\n        showSnackbar('PDF scaricato con successo', 'success');\n      } else {\n        showSnackbar('Errore: Formato PDF non riconosciuto', 'error');\n      }\n    } catch (error) {\n      console.error('Errore generazione PDF:', error);\n      showSnackbar('Errore nella generazione del PDF: ' + (error.message || 'Errore sconosciuto'), 'error');\n    } finally {\n      setOperationInProgress(false);\n    }\n  };\n\n  const handleDeleteCertificazione = async (certificazione) => {\n    if (window.confirm(`Sei sicuro di voler eliminare la certificazione ${certificazione.numero_certificato}?`)) {\n      try {\n        setOperationInProgress(true);\n        await certificazioneService.deleteCertificazione(cantiereId, certificazione.id_certificazione);\n        showSnackbar('Certificazione eliminata con successo', 'success');\n        await loadCertificazioni();\n        calculateStatistics();\n      } catch (error) {\n        showSnackbar('Errore nell\\'eliminazione della certificazione: ' + (error.message || 'Errore sconosciuto'), 'error');\n      } finally {\n        setOperationInProgress(false);\n      }\n    }\n  };\n\n  // Operazioni bulk\n  const handleBulkDelete = async () => {\n    if (bulkSelection.length === 0) {\n      showSnackbar('Seleziona almeno un elemento', 'warning');\n      return;\n    }\n\n    if (window.confirm(`Sei sicuro di voler eliminare ${bulkSelection.length} certificazioni?`)) {\n      try {\n        setOperationInProgress(true);\n        for (const id of bulkSelection) {\n          await certificazioneService.deleteCertificazione(cantiereId, id);\n        }\n        showSnackbar(`${bulkSelection.length} certificazioni eliminate con successo`, 'success');\n        setBulkSelection([]);\n        await loadCertificazioni();\n        calculateStatistics();\n      } catch (error) {\n        showSnackbar('Errore nell\\'eliminazione delle certificazioni', 'error');\n      } finally {\n        setOperationInProgress(false);\n      }\n    }\n  };\n\n  const handleBulkExport = async () => {\n    if (bulkSelection.length === 0) {\n      showSnackbar('Seleziona almeno un elemento', 'warning');\n      return;\n    }\n\n    try {\n      setOperationInProgress(true);\n      // Implementa export bulk\n      const selectedCerts = certificazioni.filter(cert =>\n        bulkSelection.includes(cert.id_certificazione)\n      );\n\n      // Crea CSV\n      const csvContent = generateCSV(selectedCerts);\n      downloadCSV(csvContent, `certificazioni_${new Date().toISOString().split('T')[0]}.csv`);\n\n      showSnackbar(`${bulkSelection.length} certificazioni esportate`, 'success');\n    } catch (error) {\n      showSnackbar('Errore nell\\'esportazione', 'error');\n    } finally {\n      setOperationInProgress(false);\n    }\n  };\n\n  // Funzioni di export\n  const generateCSV = (data) => {\n    const headers = ['ID Cavo', 'Numero Certificato', 'Data', 'Operatore', 'Strumento', 'Lunghezza', 'Isolamento', 'Risultato'];\n    const rows = data.map(cert => [\n      cert.id_cavo,\n      cert.numero_certificato,\n      new Date(cert.data_certificazione).toLocaleDateString(),\n      cert.operatore,\n      cert.strumento,\n      cert.lunghezza_misurata,\n      cert.valore_isolamento,\n      cert.risultato_finale\n    ]);\n\n    return [headers, ...rows].map(row => row.join(',')).join('\\n');\n  };\n\n  const downloadCSV = (content, filename) => {\n    const blob = new Blob([content], { type: 'text/csv;charset=utf-8;' });\n    const link = document.createElement('a');\n    if (link.download !== undefined) {\n      const url = URL.createObjectURL(blob);\n      link.setAttribute('href', url);\n      link.setAttribute('download', filename);\n      link.style.visibility = 'hidden';\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n    }\n  };\n\n  const handleExportAll = () => {\n    const csvContent = generateCSV(filteredCertificazioni);\n    downloadCSV(csvContent, `tutte_certificazioni_${new Date().toISOString().split('T')[0]}.csv`);\n    showSnackbar('Esportazione completata', 'success');\n  };\n\n  // Espone metodi tramite ref\n  useImperativeHandle(ref, () => ({\n    handleOptionSelect: (option) => {\n      if (option === 'creaCertificazione') {\n        openCreateDialog();\n      } else if (option === 'visualizzaCertificazioni') {\n        setActiveTab(1);\n      }\n    }\n  }));\n\n  // Calcola elementi per paginazione\n  const getCurrentPageItems = (items) => {\n    const startIndex = (currentPage - 1) * itemsPerPage;\n    const endIndex = startIndex + itemsPerPage;\n    return items.slice(startIndex, endIndex);\n  };\n\n  const getTotalPages = (items) => Math.ceil(items.length / itemsPerPage);\n\n  // Ottieni opzioni uniche per filtri\n  const getUniqueValues = (array, field) => {\n    return [...new Set(array.map(item => item[field]).filter(Boolean))];\n  };\n\n  // Dashboard minimal con statistiche essenziali\n  const renderDashboard = () => (\n    <Paper sx={{ p: 2, mb: 3, bgcolor: 'grey.50' }}>\n      <Stack direction=\"row\" spacing={4} alignItems=\"center\" justifyContent=\"space-between\" flexWrap=\"wrap\">\n        {/* Statistiche essenziali in formato compatto */}\n        <Stack direction=\"row\" alignItems=\"center\" spacing={1}>\n          <CableIcon color=\"primary\" fontSize=\"small\" />\n          <Box>\n            <Typography variant=\"h6\" fontWeight=\"bold\" sx={{ lineHeight: 1 }}>\n              {statistics.totaleCavi}\n            </Typography>\n            <Typography variant=\"caption\" color=\"text.secondary\">\n              Totale\n            </Typography>\n          </Box>\n        </Stack>\n\n        <Stack direction=\"row\" alignItems=\"center\" spacing={1}>\n          <CheckIcon color=\"success\" fontSize=\"small\" />\n          <Box>\n            <Typography variant=\"h6\" fontWeight=\"bold\" sx={{ lineHeight: 1 }}>\n              {statistics.caviCertificati}\n            </Typography>\n            <Typography variant=\"caption\" color=\"text.secondary\">\n              Certificati\n            </Typography>\n          </Box>\n        </Stack>\n\n        <Stack direction=\"row\" alignItems=\"center\" spacing={1}>\n          <BuildIcon color=\"info\" fontSize=\"small\" />\n          <Box>\n            <Typography variant=\"h6\" fontWeight=\"bold\" sx={{ lineHeight: 1 }}>\n              {statistics.caviCertificabili}\n            </Typography>\n            <Typography variant=\"caption\" color=\"text.secondary\">\n              Pronti\n            </Typography>\n          </Box>\n        </Stack>\n\n        <Stack direction=\"row\" alignItems=\"center\" spacing={1}>\n          <Box sx={{\n            width: 32,\n            height: 32,\n            borderRadius: '50%',\n            bgcolor: statistics.percentualeCompletamento >= 80 ? 'success.main' :\n                     statistics.percentualeCompletamento >= 50 ? 'warning.main' : 'error.main',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center'\n          }}>\n            <Typography variant=\"caption\" fontWeight=\"bold\" color=\"white\">\n              {statistics.percentualeCompletamento}%\n            </Typography>\n          </Box>\n          <Box>\n            <Typography variant=\"body2\" fontWeight=\"medium\" sx={{ lineHeight: 1 }}>\n              Completamento\n            </Typography>\n            <Typography variant=\"caption\" color=\"text.secondary\">\n              {statistics.certificazioniOggi} oggi\n            </Typography>\n          </Box>\n        </Stack>\n\n        {/* Link al report dettagliato */}\n        <Button\n          variant=\"outlined\"\n          size=\"small\"\n          startIcon={<ReportIcon />}\n          onClick={() => window.open(`/cantieri/${cantiereId}/report-avanzamento`, '_blank')}\n          sx={{ ml: 'auto' }}\n        >\n          Report Dettagliato\n        </Button>\n      </Stack>\n    </Paper>\n  );\n\n  // Componente barra di ricerca avanzata\n  const renderSearchAndFilters = () => (\n    <Paper sx={{ p: 2, mb: 3 }}>\n      <Grid container spacing={2} alignItems=\"center\">\n        <Grid item xs={12} md={4}>\n          <TextField\n            fullWidth\n            placeholder=\"Cerca cavi, certificazioni, operatori...\"\n            value={searchTerm}\n            onChange={(e) => setSearchTerm(e.target.value)}\n            InputProps={{\n              startAdornment: (\n                <InputAdornment position=\"start\">\n                  <SearchIcon />\n                </InputAdornment>\n              ),\n              endAdornment: searchTerm && (\n                <InputAdornment position=\"end\">\n                  <IconButton onClick={() => setSearchTerm('')} size=\"small\">\n                    <ClearIcon />\n                  </IconButton>\n                </InputAdornment>\n              )\n            }}\n          />\n        </Grid>\n\n        <Grid item xs={12} md={2}>\n          <Button\n            fullWidth\n            variant=\"outlined\"\n            startIcon={<FilterIcon />}\n            onClick={() => setAdvancedFiltersOpen(!advancedFiltersOpen)}\n            color={Object.values(filters).some(f => f) ? 'primary' : 'inherit'}\n          >\n            Filtri {Object.values(filters).filter(f => f).length > 0 && `(${Object.values(filters).filter(f => f).length})`}\n          </Button>\n        </Grid>\n\n        <Grid item xs={12} md={2}>\n          <Button\n            fullWidth\n            variant=\"outlined\"\n            startIcon={bulkMode ? <ClearIcon /> : <CheckIcon />}\n            onClick={toggleBulkMode}\n            color={bulkMode ? 'secondary' : 'inherit'}\n            disabled={activeTab === 0}\n          >\n            {bulkMode ? 'Esci Selezione' : 'Selezione Multipla'}\n          </Button>\n        </Grid>\n\n        <Grid item xs={12} md={2}>\n          <Button\n            fullWidth\n            variant=\"outlined\"\n            startIcon={<ExportIcon />}\n            onClick={handleExportAll}\n            disabled={activeTab === 0 || filteredCertificazioni.length === 0}\n          >\n            {activeTab === 0 ? 'Export (solo certificazioni)' : 'Esporta Certificazioni'}\n          </Button>\n        </Grid>\n\n        <Grid item xs={12} md={2}>\n          <Button\n            fullWidth\n            variant=\"contained\"\n            startIcon={<AddIcon />}\n            onClick={openCreateDialog}\n          >\n            Nuova Certificazione\n          </Button>\n        </Grid>\n      </Grid>\n\n      {/* Filtri avanzati - Diversi per ogni tab */}\n      <Collapse in={advancedFiltersOpen}>\n        <Divider sx={{ my: 2 }} />\n        <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mb: 2 }}>\n          {activeTab === 0 ? 'Filtri per Cavi' : 'Filtri per Certificazioni'}\n        </Typography>\n\n        <Grid container spacing={2}>\n          {/* Filtri per tab Cavi */}\n          {activeTab === 0 && (\n            <>\n              <Grid item xs={12} md={3}>\n                <FormControl fullWidth size=\"small\">\n                  <InputLabel>Stato Installazione</InputLabel>\n                  <Select\n                    value={filters.stato}\n                    onChange={(e) => setFilters({...filters, stato: e.target.value})}\n                  >\n                    <MenuItem value=\"\">Tutti</MenuItem>\n                    <MenuItem value=\"INSTALLATO\">Installato</MenuItem>\n                    <MenuItem value=\"NON_INSTALLATO\">Non Installato</MenuItem>\n                    <MenuItem value=\"IN_CORSO\">In Corso</MenuItem>\n                  </Select>\n                </FormControl>\n              </Grid>\n\n              <Grid item xs={12} md={3}>\n                <FormControl fullWidth size=\"small\">\n                  <InputLabel>Tipologia</InputLabel>\n                  <Select\n                    value={filters.tipologia}\n                    onChange={(e) => setFilters({...filters, tipologia: e.target.value})}\n                  >\n                    <MenuItem value=\"\">Tutte</MenuItem>\n                    {[...new Set(cavi.map(c => c.tipologia))].filter(Boolean).map(tip => (\n                      <MenuItem key={tip} value={tip}>{tip}</MenuItem>\n                    ))}\n                  </Select>\n                </FormControl>\n              </Grid>\n\n              <Grid item xs={12} md={3}>\n                <FormControl fullWidth size=\"small\">\n                  <InputLabel>Stato Certificazione</InputLabel>\n                  <Select\n                    value={filters.certificazione}\n                    onChange={(e) => setFilters({...filters, certificazione: e.target.value})}\n                  >\n                    <MenuItem value=\"\">Tutti</MenuItem>\n                    <MenuItem value=\"CERTIFICATO\">Certificato</MenuItem>\n                    <MenuItem value=\"NON_CERTIFICATO\">Non Certificato</MenuItem>\n                  </Select>\n                </FormControl>\n              </Grid>\n            </>\n          )}\n\n          {/* Filtri per tab Certificazioni */}\n          {activeTab === 1 && (\n            <>\n              <Grid item xs={12} md={3}>\n                <FormControl fullWidth size=\"small\">\n                  <InputLabel>Operatore</InputLabel>\n                  <Select\n                    value={filters.operatore}\n                    onChange={(e) => setFilters({...filters, operatore: e.target.value})}\n                  >\n                    <MenuItem value=\"\">Tutti</MenuItem>\n                    {[...new Set(certificazioni.map(c => c.operatore))].filter(Boolean).map(op => (\n                      <MenuItem key={op} value={op}>{op}</MenuItem>\n                    ))}\n                  </Select>\n                </FormControl>\n              </Grid>\n\n              <Grid item xs={12} md={3}>\n                <FormControl fullWidth size=\"small\">\n                  <InputLabel>Risultato Test</InputLabel>\n                  <Select\n                    value={filters.risultatoTest}\n                    onChange={(e) => setFilters({...filters, risultatoTest: e.target.value})}\n                  >\n                    <MenuItem value=\"\">Tutti</MenuItem>\n                    <MenuItem value=\"CONFORME\">Conforme</MenuItem>\n                    <MenuItem value=\"NON_CONFORME\">Non Conforme</MenuItem>\n                    <MenuItem value=\"DA_VERIFICARE\">Da Verificare</MenuItem>\n                  </Select>\n                </FormControl>\n              </Grid>\n\n              <Grid item xs={12} md={3}>\n                <TextField\n                  fullWidth\n                  size=\"small\"\n                  label=\"Isolamento Min (MΩ)\"\n                  type=\"number\"\n                  value={filters.valoreIsolamento}\n                  onChange={(e) => setFilters({...filters, valoreIsolamento: e.target.value})}\n                  placeholder=\"es. 500\"\n                />\n              </Grid>\n\n              <Grid item xs={12} md={3}>\n                <TextField\n                  fullWidth\n                  size=\"small\"\n                  label=\"Data Inizio\"\n                  type=\"date\"\n                  value={filters.dataInizio}\n                  onChange={(e) => setFilters({...filters, dataInizio: e.target.value})}\n                  InputLabelProps={{ shrink: true }}\n                />\n              </Grid>\n\n              <Grid item xs={12} md={3}>\n                <TextField\n                  fullWidth\n                  size=\"small\"\n                  label=\"Data Fine\"\n                  type=\"date\"\n                  value={filters.dataFine}\n                  onChange={(e) => setFilters({...filters, dataFine: e.target.value})}\n                  InputLabelProps={{ shrink: true }}\n                />\n              </Grid>\n            </>\n          )}\n\n          <Grid item xs={12}>\n            <Stack direction=\"row\" spacing={1} justifyContent=\"flex-end\">\n              <Button\n                variant=\"outlined\"\n                size=\"small\"\n                onClick={() => setFilters({\n                  stato: '', tipologia: '', operatore: '', dataInizio: '',\n                  dataFine: '', valoreIsolamento: '', risultatoTest: '', strumento: '',\n                  certificazione: ''\n                })}\n              >\n                Pulisci Tutti i Filtri\n              </Button>\n            </Stack>\n          </Grid>\n        </Grid>\n      </Collapse>\n\n      {/* Barra azioni bulk */}\n      {bulkMode && bulkSelection.length > 0 && (\n        <>\n          <Divider sx={{ my: 2 }} />\n          <Stack direction=\"row\" spacing={2} alignItems=\"center\">\n            <Typography variant=\"body2\">\n              {bulkSelection.length} elementi selezionati\n            </Typography>\n            <Button\n              size=\"small\"\n              variant=\"outlined\"\n              onClick={selectAllItems}\n            >\n              Seleziona Tutto\n            </Button>\n            <Button\n              size=\"small\"\n              variant=\"outlined\"\n              onClick={clearSelection}\n            >\n              Deseleziona\n            </Button>\n            <Button\n              size=\"small\"\n              variant=\"outlined\"\n              startIcon={<ExportIcon />}\n              onClick={handleBulkExport}\n            >\n              Esporta Selezionati\n            </Button>\n            <Button\n              size=\"small\"\n              variant=\"outlined\"\n              color=\"error\"\n              startIcon={<DeleteIcon />}\n              onClick={handleBulkDelete}\n            >\n              Elimina Selezionati\n            </Button>\n          </Stack>\n        </>\n      )}\n    </Paper>\n  );\n\n  // Renderizza la tabella dei cavi\n  const renderCaviTable = () => {\n    const currentItems = getCurrentPageItems(filteredCavi);\n\n    if (filteredCavi.length === 0) {\n      return (\n        <Alert severity=\"info\">\n          {searchTerm || filters.stato || filters.tipologia\n            ? 'Nessun cavo trovato con i filtri applicati'\n            : 'Nessun cavo disponibile'}\n        </Alert>\n      );\n    }\n\n    return (\n      <>\n        <TableContainer component={Paper}>\n          <Table size=\"small\">\n            <TableHead>\n              <TableRow>\n                <TableCell>ID Cavo</TableCell>\n                <TableCell>Tipologia</TableCell>\n                <TableCell>Sezione</TableCell>\n                <TableCell>Partenza</TableCell>\n                <TableCell>Arrivo</TableCell>\n                <TableCell>Metri</TableCell>\n                <TableCell>Stato</TableCell>\n                <TableCell>Collegamenti</TableCell>\n                <TableCell>Certificato</TableCell>\n                <TableCell>Azioni</TableCell>\n              </TableRow>\n            </TableHead>\n            <TableBody>\n              {currentItems.map((cavo) => {\n                const isCertificato = isCavoCertificato(cavo.id_cavo);\n                const puoCertificare = puoEssereCertificato(cavo);\n                const messaggioErrore = !puoCertificare ? getMessaggioErroreCertificazione(cavo) : '';\n\n                return (\n                  <TableRow key={cavo.id_cavo}>\n                    <TableCell>\n                      <Typography variant=\"body2\" fontWeight=\"medium\">\n                        {cavo.id_cavo}\n                      </Typography>\n                    </TableCell>\n                    <TableCell>{cavo.tipologia}</TableCell>\n                    <TableCell>{cavo.sezione}</TableCell>\n                    <TableCell>{cavo.ubicazione_partenza}</TableCell>\n                    <TableCell>{cavo.ubicazione_arrivo}</TableCell>\n                    <TableCell>{cavo.metratura_reale || cavo.metri_teorici} m</TableCell>\n                    <TableCell>\n                      <Chip\n                        size=\"small\"\n                        label={cavo.stato_installazione}\n                        color={cavo.stato_installazione === 'INSTALLATO' ? 'success' : 'default'}\n                      />\n                    </TableCell>\n                    <TableCell>\n                      {(() => {\n                        const collegamenti = cavo.collegamenti || 0;\n                        const statoCollegamento = collegamenti === 0 ? 'Non collegato' :\n                                                 collegamenti === 1 ? 'Solo partenza' :\n                                                 collegamenti === 2 ? 'Solo arrivo' :\n                                                 collegamenti === 3 ? 'Completo' :\n                                                 'Sconosciuto';\n                        const colore = collegamenti === 3 ? 'success' :\n                                      collegamenti === 0 ? 'error' : 'warning';\n\n                        return (\n                          <Tooltip title={`Partenza: ${cavo.responsabile_partenza || 'Non collegato'} | Arrivo: ${cavo.responsabile_arrivo || 'Non collegato'}`}>\n                            <Chip\n                              size=\"small\"\n                              label={statoCollegamento}\n                              color={colore}\n                              icon={collegamenti === 3 ? <CheckIcon /> : <WarningIcon />}\n                            />\n                          </Tooltip>\n                        );\n                      })()}\n                    </TableCell>\n                    <TableCell>\n                      {isCertificato ? (\n                        <Chip\n                          size=\"small\"\n                          icon={<CheckIcon />}\n                          label=\"Certificato\"\n                          color=\"success\"\n                        />\n                      ) : (\n                        <Chip\n                          size=\"small\"\n                          icon={<WarningIcon />}\n                          label=\"Non certificato\"\n                          color=\"warning\"\n                        />\n                      )}\n                    </TableCell>\n                    <TableCell>\n                      {isCertificato ? (\n                        <Tooltip title=\"Cavo già certificato\">\n                          <Chip\n                            icon={<CheckIcon />}\n                            label=\"Certificato\"\n                            color=\"success\"\n                            size=\"small\"\n                          />\n                        </Tooltip>\n                      ) : puoCertificare ? (\n                        <Tooltip title=\"Crea certificazione per questo cavo\">\n                          <IconButton\n                            size=\"small\"\n                            onClick={() => openCreateDialog(cavo)}\n                            color=\"primary\"\n                          >\n                            <AddIcon />\n                          </IconButton>\n                        </Tooltip>\n                      ) : (\n                        <Tooltip title={messaggioErrore}>\n                          <span>\n                            <IconButton\n                              size=\"small\"\n                              disabled\n                              onClick={() => showSnackbar(messaggioErrore, 'warning')}\n                            >\n                              <BlockIcon />\n                            </IconButton>\n                          </span>\n                        </Tooltip>\n                      )}\n                    </TableCell>\n                  </TableRow>\n                );\n              })}\n            </TableBody>\n          </Table>\n        </TableContainer>\n\n        {getTotalPages(filteredCavi) > 1 && (\n          <Box sx={{ display: 'flex', justifyContent: 'center', mt: 2 }}>\n            <Pagination\n              count={getTotalPages(filteredCavi)}\n              page={currentPage}\n              onChange={(event, value) => setCurrentPage(value)}\n              color=\"primary\"\n            />\n          </Box>\n        )}\n      </>\n    );\n  };\n\n  // Renderizza la tabella delle certificazioni\n  const renderCertificazioniTable = () => {\n    const currentItems = getCurrentPageItems(filteredCertificazioni);\n\n    if (filteredCertificazioni.length === 0) {\n      return (\n        <Alert severity=\"info\">\n          {searchTerm || filters.operatore\n            ? 'Nessuna certificazione trovata con i filtri applicati'\n            : 'Nessuna certificazione disponibile'}\n        </Alert>\n      );\n    }\n\n    return (\n      <>\n        <TableContainer component={Paper}>\n          <Table size=\"small\">\n            <TableHead>\n              <TableRow>\n                {bulkMode && (\n                  <TableCell padding=\"checkbox\">\n                    <IconButton\n                      size=\"small\"\n                      onClick={bulkSelection.length === filteredCertificazioni.length ? clearSelection : selectAllItems}\n                    >\n                      {bulkSelection.length === filteredCertificazioni.length ? <ClearIcon /> : <CheckIcon />}\n                    </IconButton>\n                  </TableCell>\n                )}\n                <TableCell>\n                  <Stack direction=\"row\" alignItems=\"center\" spacing={1}>\n                    <Typography variant=\"body2\" fontWeight=\"bold\">N° Certificato</Typography>\n                    <IconButton size=\"small\" onClick={() => {\n                      setSortBy('numero_certificato');\n                      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');\n                    }}>\n                      {sortBy === 'numero_certificato' ? (sortOrder === 'asc' ? <ExpandLessIcon /> : <ExpandMoreIcon />) : <ExpandMoreIcon />}\n                    </IconButton>\n                  </Stack>\n                </TableCell>\n                <TableCell>ID Cavo</TableCell>\n                <TableCell>\n                  <Stack direction=\"row\" alignItems=\"center\" spacing={1}>\n                    <Typography variant=\"body2\" fontWeight=\"bold\">Data</Typography>\n                    <IconButton size=\"small\" onClick={() => {\n                      setSortBy('data_certificazione');\n                      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');\n                    }}>\n                      {sortBy === 'data_certificazione' ? (sortOrder === 'asc' ? <ExpandLessIcon /> : <ExpandMoreIcon />) : <ExpandMoreIcon />}\n                    </IconButton>\n                  </Stack>\n                </TableCell>\n                <TableCell>Operatore</TableCell>\n                <TableCell>Strumento</TableCell>\n                <TableCell>Lunghezza</TableCell>\n                <TableCell>Isolamento</TableCell>\n                <TableCell>Risultato</TableCell>\n                <TableCell>Azioni</TableCell>\n              </TableRow>\n            </TableHead>\n            <TableBody>\n              {currentItems.map((cert) => (\n                <TableRow\n                  key={cert.id_certificazione}\n                  selected={bulkSelection.includes(cert.id_certificazione)}\n                  hover\n                >\n                  {bulkMode && (\n                    <TableCell padding=\"checkbox\">\n                      <IconButton\n                        size=\"small\"\n                        onClick={() => toggleItemSelection(cert.id_certificazione)}\n                        color={bulkSelection.includes(cert.id_certificazione) ? 'primary' : 'default'}\n                      >\n                        {bulkSelection.includes(cert.id_certificazione) ? <CheckIcon /> : <AddIcon />}\n                      </IconButton>\n                    </TableCell>\n                  )}\n                  <TableCell>\n                    <Typography variant=\"body2\" fontWeight=\"medium\">\n                      {cert.numero_certificato}\n                    </Typography>\n                  </TableCell>\n                  <TableCell>\n                    <Chip size=\"small\" label={cert.id_cavo} variant=\"outlined\" />\n                  </TableCell>\n                  <TableCell>{new Date(cert.data_certificazione).toLocaleDateString()}</TableCell>\n                  <TableCell>\n                    <Stack direction=\"row\" alignItems=\"center\" spacing={1}>\n                      <PersonIcon fontSize=\"small\" />\n                      <Typography variant=\"body2\">{cert.operatore || cert.id_operatore}</Typography>\n                    </Stack>\n                  </TableCell>\n                  <TableCell>\n                    <Typography variant=\"body2\">\n                      {cert.id_strumento ?\n                        (() => {\n                          const strumento = strumenti.find(s => s.id_strumento === cert.id_strumento);\n                          return strumento ? `${strumento.nome} - ${strumento.marca}` : 'Strumento non trovato';\n                        })()\n                        : (cert.strumento_utilizzato || 'N/A')\n                      }\n                    </Typography>\n                  </TableCell>\n                  <TableCell>\n                    <Typography variant=\"body2\">{cert.lunghezza_misurata} m</Typography>\n                  </TableCell>\n                  <TableCell>\n                    <Chip\n                      size=\"small\"\n                      label={`${cert.valore_isolamento} MΩ`}\n                      color={parseFloat(cert.valore_isolamento) >= 500 ? 'success' : 'warning'}\n                      icon={parseFloat(cert.valore_isolamento) >= 500 ? <CheckIcon /> : <WarningIcon />}\n                    />\n                  </TableCell>\n                  <TableCell>\n                    <Chip\n                      size=\"small\"\n                      label={cert.risultato_finale || 'CONFORME'}\n                      color={cert.risultato_finale === 'CONFORME' ? 'success' : cert.risultato_finale === 'NON_CONFORME' ? 'error' : 'warning'}\n                    />\n                  </TableCell>\n                  <TableCell>\n                    <Stack direction=\"row\" spacing={0.5}>\n                      <Tooltip title=\"Visualizza dettagli\">\n                        <IconButton\n                          size=\"small\"\n                          onClick={() => {\n                            setSelectedItem(cert);\n                            setDialogType('view');\n                            setOpenDialog(true);\n                          }}\n                        >\n                          <ViewIcon />\n                        </IconButton>\n                      </Tooltip>\n                      <Tooltip title=\"Genera PDF\">\n                        <IconButton\n                          size=\"small\"\n                          onClick={() => handleGeneratePdf(cert)}\n                          disabled={operationInProgress}\n                        >\n                          <PdfIcon />\n                        </IconButton>\n                      </Tooltip>\n                      <Tooltip title=\"Elimina\">\n                        <IconButton\n                          size=\"small\"\n                          color=\"error\"\n                          onClick={() => handleDeleteCertificazione(cert)}\n                          disabled={operationInProgress}\n                        >\n                          <DeleteIcon />\n                        </IconButton>\n                      </Tooltip>\n                    </Stack>\n                  </TableCell>\n                </TableRow>\n              ))}\n            </TableBody>\n          </Table>\n        </TableContainer>\n\n        {getTotalPages(filteredCertificazioni) > 1 && (\n          <Box sx={{ display: 'flex', justifyContent: 'center', mt: 2 }}>\n            <Pagination\n              count={getTotalPages(filteredCertificazioni)}\n              page={currentPage}\n              onChange={(event, value) => setCurrentPage(value)}\n              color=\"primary\"\n            />\n          </Box>\n        )}\n      </>\n    );\n  };\n\n  // Renderizza il dialog per creare/modificare certificazione\n  const renderCertificazioneDialog = () => {\n    if (dialogType !== 'create' && dialogType !== 'edit') return null;\n\n    return (\n      <Dialog open={openDialog} onClose={closeDialog} maxWidth=\"md\" fullWidth>\n        <DialogTitle>\n          {dialogType === 'create' ? 'Nuova Certificazione' : 'Modifica Certificazione'}\n        </DialogTitle>\n        <DialogContent>\n          <Grid container spacing={2} sx={{ mt: 1 }}>\n            <Grid item xs={12} md={6}>\n              <Autocomplete\n                options={cavi.filter(cavo => {\n                  // Mostra solo cavi che possono essere certificati o quello già selezionato\n                  const isSelected = cavo.id_cavo === formData.id_cavo;\n                  const isNotCertified = !certificazioni.some(cert => cert.id_cavo === cavo.id_cavo);\n                  const canBeCertified = puoEssereCertificato(cavo);\n\n                  return isSelected || (isNotCertified && canBeCertified);\n                })}\n                getOptionLabel={(option) => `${option.id_cavo} - ${option.tipologia}`}\n                value={cavi.find(c => c.id_cavo === formData.id_cavo) || null}\n                onChange={(event, newValue) => {\n                  if (newValue) {\n                    handleCavoSelect(newValue);\n                  } else {\n                    setFormData(prev => ({ ...prev, id_cavo: '', lunghezza_misurata: '' }));\n                  }\n                }}\n                renderInput={(params) => (\n                  <TextField\n                    {...params}\n                    label=\"Cavo *\"\n                    placeholder=\"Seleziona un cavo posato\"\n                    required\n                    helperText=\"Solo cavi posati/installati (il collegamento può essere gestito al momento)\"\n                  />\n                )}\n                renderOption={(props, option) => {\n                  const collegamenti = option.collegamenti || 0;\n                  const isCollegato = collegamenti === 3;\n\n                  return (\n                    <Box component=\"li\" {...props}>\n                      <Box sx={{ width: '100%' }}>\n                        <Stack direction=\"row\" justifyContent=\"space-between\" alignItems=\"center\">\n                          <Box>\n                            <Typography variant=\"body2\" fontWeight=\"medium\">\n                              {option.id_cavo}\n                            </Typography>\n                            <Typography variant=\"caption\" color=\"text.secondary\">\n                              {option.tipologia} - {option.ubicazione_partenza} → {option.ubicazione_arrivo}\n                            </Typography>\n                          </Box>\n                          <Stack direction=\"row\" spacing={1}>\n                            <Chip\n                              size=\"small\"\n                              label={option.stato_installazione}\n                              color={option.stato_installazione === 'INSTALLATO' ? 'success' : 'default'}\n                            />\n                            <Chip\n                              size=\"small\"\n                              label={isCollegato ? 'Collegato' : 'Da collegare'}\n                              color={isCollegato ? 'success' : 'warning'}\n                              icon={isCollegato ? <CheckIcon /> : <WarningIcon />}\n                            />\n                          </Stack>\n                        </Stack>\n                      </Box>\n                    </Box>\n                  );\n                }}\n              />\n            </Grid>\n\n            <Grid item xs={12} md={6}>\n              <TextField\n                fullWidth\n                label=\"Operatore *\"\n                value={formData.id_operatore}\n                onChange={(e) => handleFormChange('id_operatore', e.target.value)}\n                required\n              />\n            </Grid>\n\n            <Grid item xs={12} md={6}>\n              <FormControl fullWidth required>\n                <InputLabel>Strumento *</InputLabel>\n                <Select\n                  value={formData.id_strumento}\n                  onChange={(e) => handleFormChange('id_strumento', e.target.value)}\n                  label=\"Strumento *\"\n                >\n                  {strumenti.map((strumento) => (\n                    <MenuItem key={strumento.id_strumento} value={strumento.id_strumento}>\n                      {strumento.nome} - {strumento.marca} {strumento.modello} (S/N: {strumento.numero_serie})\n                    </MenuItem>\n                  ))}\n                </Select>\n              </FormControl>\n            </Grid>\n\n            <Grid item xs={12} md={6}>\n              <TextField\n                fullWidth\n                label=\"Lunghezza Misurata (m) *\"\n                type=\"number\"\n                value={formData.lunghezza_misurata}\n                onChange={(e) => handleFormChange('lunghezza_misurata', e.target.value)}\n                required\n              />\n            </Grid>\n\n            <Grid item xs={12} md={4}>\n              <FormControl fullWidth>\n                <InputLabel>Continuità</InputLabel>\n                <Select\n                  value={formData.valore_continuita}\n                  onChange={(e) => handleFormChange('valore_continuita', e.target.value)}\n                  label=\"Continuità\"\n                >\n                  <MenuItem value=\"OK\">OK</MenuItem>\n                  <MenuItem value=\"NOK\">NOK</MenuItem>\n                </Select>\n              </FormControl>\n            </Grid>\n\n            <Grid item xs={12} md={4}>\n              <TextField\n                fullWidth\n                label=\"Isolamento (MΩ) *\"\n                type=\"number\"\n                value={formData.valore_isolamento}\n                onChange={(e) => handleFormChange('valore_isolamento', e.target.value)}\n                required\n                helperText=\"Valore minimo consigliato: 500 MΩ\"\n              />\n            </Grid>\n\n            <Grid item xs={12} md={4}>\n              <FormControl fullWidth>\n                <InputLabel>Resistenza</InputLabel>\n                <Select\n                  value={formData.valore_resistenza}\n                  onChange={(e) => handleFormChange('valore_resistenza', e.target.value)}\n                  label=\"Resistenza\"\n                >\n                  <MenuItem value=\"OK\">OK</MenuItem>\n                  <MenuItem value=\"NOK\">NOK</MenuItem>\n                </Select>\n              </FormControl>\n            </Grid>\n\n            {/* Sezione Collegamenti */}\n            <Grid item xs={12}>\n              <Divider sx={{ my: 2 }}>\n                <Typography variant=\"body2\" color=\"text.secondary\">\n                  Stato Collegamenti Cavo\n                </Typography>\n              </Divider>\n            </Grid>\n\n            {formData.id_cavo && (() => {\n              const cavo = cavi.find(c => c.id_cavo === formData.id_cavo);\n              if (!cavo) return null;\n\n              const collegamenti = cavo.collegamenti || 0;\n              const isCollegato = collegamenti === 3;\n\n              return (\n                <Grid item xs={12}>\n                  <Paper sx={{ p: 2, bgcolor: isCollegato ? 'success.light' : 'warning.light' }}>\n                    <Stack direction=\"row\" alignItems=\"center\" spacing={2}>\n                      {isCollegato ? <CheckIcon color=\"success\" /> : <WarningIcon color=\"warning\" />}\n                      <Box>\n                        <Typography variant=\"body2\" fontWeight=\"bold\">\n                          {isCollegato ? 'Cavo Completamente Collegato' : 'Cavo Non Completamente Collegato'}\n                        </Typography>\n                        <Typography variant=\"caption\">\n                          Stato: {collegamenti === 0 ? 'Non collegato' :\n                                  collegamenti === 1 ? 'Solo partenza collegata' :\n                                  collegamenti === 2 ? 'Solo arrivo collegato' :\n                                  collegamenti === 3 ? 'Completamente collegato' :\n                                  'Stato sconosciuto'}\n                        </Typography>\n                        {!isCollegato && (\n                          <Typography variant=\"caption\" display=\"block\" sx={{ mt: 1 }}>\n                            ⚠️ Il cavo può essere certificato ma ricorda di completare i collegamenti prima della messa in servizio\n                          </Typography>\n                        )}\n                      </Box>\n                    </Stack>\n                  </Paper>\n                </Grid>\n              );\n            })()}\n\n            {/* Campi avanzati */}\n            <Grid item xs={12}>\n              <Divider sx={{ my: 2 }}>\n                <Typography variant=\"body2\" color=\"text.secondary\">\n                  Parametri Ambientali e Test Avanzati\n                </Typography>\n              </Divider>\n            </Grid>\n\n            <Grid item xs={12} md={3}>\n              <TextField\n                fullWidth\n                label=\"Temperatura Ambiente (°C)\"\n                type=\"number\"\n                value={formData.temperatura_ambiente}\n                onChange={(e) => handleFormChange('temperatura_ambiente', e.target.value)}\n                helperText=\"Temperatura durante il test\"\n              />\n            </Grid>\n\n            <Grid item xs={12} md={3}>\n              <TextField\n                fullWidth\n                label=\"Umidità (%)\"\n                type=\"number\"\n                value={formData.umidita}\n                onChange={(e) => handleFormChange('umidita', e.target.value)}\n                helperText=\"Umidità relativa\"\n              />\n            </Grid>\n\n            <Grid item xs={12} md={3}>\n              <TextField\n                fullWidth\n                label=\"Tensione di Prova (V)\"\n                type=\"number\"\n                value={formData.tensione_prova}\n                onChange={(e) => handleFormChange('tensione_prova', e.target.value)}\n                helperText=\"Tensione applicata per il test\"\n              />\n            </Grid>\n\n            <Grid item xs={12} md={3}>\n              <TextField\n                fullWidth\n                label=\"Durata Prova (min)\"\n                type=\"number\"\n                value={formData.durata_prova}\n                onChange={(e) => handleFormChange('durata_prova', e.target.value)}\n                helperText=\"Durata del test in minuti\"\n              />\n            </Grid>\n\n            <Grid item xs={12} md={6}>\n              <FormControl fullWidth>\n                <InputLabel>Risultato Finale</InputLabel>\n                <Select\n                  value={formData.risultato_finale}\n                  onChange={(e) => handleFormChange('risultato_finale', e.target.value)}\n                  label=\"Risultato Finale\"\n                >\n                  <MenuItem value=\"CONFORME\">\n                    <Stack direction=\"row\" alignItems=\"center\" spacing={1}>\n                      <CheckIcon color=\"success\" />\n                      <Typography>Conforme</Typography>\n                    </Stack>\n                  </MenuItem>\n                  <MenuItem value=\"NON_CONFORME\">\n                    <Stack direction=\"row\" alignItems=\"center\" spacing={1}>\n                      <ErrorIcon color=\"error\" />\n                      <Typography>Non Conforme</Typography>\n                    </Stack>\n                  </MenuItem>\n                  <MenuItem value=\"DA_VERIFICARE\">\n                    <Stack direction=\"row\" alignItems=\"center\" spacing={1}>\n                      <WarningIcon color=\"warning\" />\n                      <Typography>Da Verificare</Typography>\n                    </Stack>\n                  </MenuItem>\n                </Select>\n              </FormControl>\n            </Grid>\n\n            <Grid item xs={12}>\n              <TextField\n                fullWidth\n                label=\"Note\"\n                multiline\n                rows={3}\n                value={formData.note}\n                onChange={(e) => handleFormChange('note', e.target.value)}\n                placeholder=\"Inserisci eventuali note, osservazioni o anomalie riscontrate durante il test...\"\n              />\n            </Grid>\n          </Grid>\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={closeDialog}>Annulla</Button>\n          <Button\n            onClick={handleCreateCertificazione}\n            variant=\"contained\"\n            disabled={loading || !formData.id_cavo || !formData.id_operatore || !formData.id_strumento || !formData.valore_isolamento}\n            startIcon={loading ? <CircularProgress size={20} /> : <SaveIcon />}\n          >\n            {dialogType === 'create' ? 'Crea Certificazione' : 'Salva Modifiche'}\n          </Button>\n        </DialogActions>\n      </Dialog>\n    );\n  };\n\n  // Renderizza il dialog di visualizzazione dettagli\n  const renderViewDialog = () => {\n    if (dialogType !== 'view' || !selectedItem) return null;\n\n    return (\n      <Dialog open={openDialog} onClose={closeDialog} maxWidth=\"md\" fullWidth>\n        <DialogTitle>\n          Dettagli Certificazione - {selectedItem.numero_certificato}\n        </DialogTitle>\n        <DialogContent>\n          <Grid container spacing={2} sx={{ mt: 1 }}>\n            <Grid item xs={12} md={6}>\n              <Card variant=\"outlined\">\n                <CardContent>\n                  <Typography variant=\"h6\" gutterBottom>\n                    Informazioni Cavo\n                  </Typography>\n                  <Typography variant=\"body2\" color=\"text.secondary\">\n                    ID Cavo: <strong>{selectedItem.id_cavo}</strong>\n                  </Typography>\n                  <Typography variant=\"body2\" color=\"text.secondary\">\n                    Lunghezza Misurata: <strong>{selectedItem.lunghezza_misurata} m</strong>\n                  </Typography>\n                </CardContent>\n              </Card>\n            </Grid>\n\n            <Grid item xs={12} md={6}>\n              <Card variant=\"outlined\">\n                <CardContent>\n                  <Typography variant=\"h6\" gutterBottom>\n                    Informazioni Certificazione\n                  </Typography>\n                  <Typography variant=\"body2\" color=\"text.secondary\">\n                    Numero: <strong>{selectedItem.numero_certificato}</strong>\n                  </Typography>\n                  <Typography variant=\"body2\" color=\"text.secondary\">\n                    Data: <strong>{new Date(selectedItem.data_certificazione).toLocaleDateString()}</strong>\n                  </Typography>\n                  <Typography variant=\"body2\" color=\"text.secondary\">\n                    Operatore: <strong>{selectedItem.operatore || selectedItem.id_operatore}</strong>\n                  </Typography>\n                </CardContent>\n              </Card>\n            </Grid>\n\n            <Grid item xs={12}>\n              <Card variant=\"outlined\">\n                <CardContent>\n                  <Typography variant=\"h6\" gutterBottom>\n                    Risultati Test\n                  </Typography>\n                  <Grid container spacing={2}>\n                    <Grid item xs={4}>\n                      <Typography variant=\"body2\" color=\"text.secondary\">\n                        Continuità\n                      </Typography>\n                      <Chip\n                        size=\"small\"\n                        label={selectedItem.valore_continuita}\n                        color={selectedItem.valore_continuita === 'OK' ? 'success' : 'error'}\n                      />\n                    </Grid>\n                    <Grid item xs={4}>\n                      <Typography variant=\"body2\" color=\"text.secondary\">\n                        Isolamento\n                      </Typography>\n                      <Chip\n                        size=\"small\"\n                        label={`${selectedItem.valore_isolamento} MΩ`}\n                        color={parseFloat(selectedItem.valore_isolamento) >= 500 ? 'success' : 'warning'}\n                      />\n                    </Grid>\n                    <Grid item xs={4}>\n                      <Typography variant=\"body2\" color=\"text.secondary\">\n                        Resistenza\n                      </Typography>\n                      <Chip\n                        size=\"small\"\n                        label={selectedItem.valore_resistenza}\n                        color={selectedItem.valore_resistenza === 'OK' ? 'success' : 'error'}\n                      />\n                    </Grid>\n                  </Grid>\n                </CardContent>\n              </Card>\n            </Grid>\n\n            {selectedItem.note && (\n              <Grid item xs={12}>\n                <Card variant=\"outlined\">\n                  <CardContent>\n                    <Typography variant=\"h6\" gutterBottom>\n                      Note\n                    </Typography>\n                    <Typography variant=\"body2\">\n                      {selectedItem.note}\n                    </Typography>\n                  </CardContent>\n                </Card>\n              </Grid>\n            )}\n          </Grid>\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={closeDialog}>Chiudi</Button>\n          <Button\n            onClick={() => handleGeneratePdf(selectedItem)}\n            variant=\"contained\"\n            startIcon={<PdfIcon />}\n            disabled={loading}\n          >\n            Genera PDF\n          </Button>\n        </DialogActions>\n      </Dialog>\n    );\n  };\n\n  // Renderizza le statistiche\n  const renderStats = () => {\n    const totalCavi = cavi.length;\n    const caviInstallati = cavi.filter(c => c.stato_installazione === 'INSTALLATO').length;\n    const caviCertificati = certificazioni.length;\n    const percentualeCertificazione = totalCavi > 0 ? Math.round((caviCertificati / caviInstallati) * 100) : 0;\n\n    return (\n      <Grid container spacing={2} sx={{ mb: 3 }}>\n        <Grid item xs={12} sm={6} md={3}>\n          <Card>\n            <CardContent>\n              <Typography color=\"text.secondary\" gutterBottom>\n                Cavi Totali\n              </Typography>\n              <Typography variant=\"h4\">\n                {totalCavi}\n              </Typography>\n            </CardContent>\n          </Card>\n        </Grid>\n        <Grid item xs={12} sm={6} md={3}>\n          <Card>\n            <CardContent>\n              <Typography color=\"text.secondary\" gutterBottom>\n                Cavi Installati\n              </Typography>\n              <Typography variant=\"h4\">\n                {caviInstallati}\n              </Typography>\n            </CardContent>\n          </Card>\n        </Grid>\n        <Grid item xs={12} sm={6} md={3}>\n          <Card>\n            <CardContent>\n              <Typography color=\"text.secondary\" gutterBottom>\n                Certificazioni\n              </Typography>\n              <Typography variant=\"h4\">\n                {caviCertificati}\n              </Typography>\n            </CardContent>\n          </Card>\n        </Grid>\n        <Grid item xs={12} sm={6} md={3}>\n          <Card>\n            <CardContent>\n              <Typography color=\"text.secondary\" gutterBottom>\n                % Certificazione\n              </Typography>\n              <Typography variant=\"h4\" color={percentualeCertificazione >= 80 ? 'success.main' : 'warning.main'}>\n                {percentualeCertificazione}%\n              </Typography>\n            </CardContent>\n          </Card>\n        </Grid>\n      </Grid>\n    );\n  };\n\n  return (\n    <Container maxWidth=\"xl\" sx={{ py: 3 }}>\n      {/* Header con titolo e azioni rapide */}\n      <Box sx={{ mb: 4 }}>\n        <Typography variant=\"h4\" component=\"h1\" gutterBottom fontWeight=\"bold\">\n          🔌 Sistema di Certificazione Cavi\n        </Typography>\n        <Typography variant=\"body1\" color=\"text.secondary\" sx={{ mb: 2 }}>\n          Gestione completa delle certificazioni elettriche secondo standard CEI 64-8\n        </Typography>\n\n        {/* Messaggio di aiuto */}\n        <Paper sx={{ p: 2, bgcolor: 'info.light', color: 'info.contrastText', mb: 2 }}>\n          <Stack direction=\"row\" alignItems=\"center\" spacing={2}>\n            <InfoIcon />\n            <Box>\n              <Typography variant=\"body2\" fontWeight=\"bold\">\n                Come utilizzare il sistema:\n              </Typography>\n              <Typography variant=\"caption\">\n                • <strong>Tab \"Cavi da Certificare\":</strong> Visualizza tutti i cavi posati e clicca sul \"+\" per certificare\n                • <strong>Certificazione:</strong> Richiede solo che il cavo sia posato, il collegamento può essere gestito al momento\n                • <strong>Tab \"Certificazioni Completate\":</strong> Gestisci le certificazioni esistenti, attiva la \"Selezione\" per operazioni multiple\n                • <strong>Filtri:</strong> Usa i filtri specifici per ogni tab per trovare rapidamente quello che cerchi\n              </Typography>\n            </Box>\n          </Stack>\n        </Paper>\n      </Box>\n\n      {/* Dashboard con statistiche */}\n      {renderDashboard()}\n\n      {/* Progress bar per operazioni in corso */}\n      {(loading || operationInProgress) && (\n        <Box sx={{ mb: 2 }}>\n          <LinearProgress />\n          {progress > 0 && (\n            <Typography variant=\"caption\" color=\"text.secondary\" sx={{ mt: 1 }}>\n              Caricamento... {progress}%\n            </Typography>\n          )}\n        </Box>\n      )}\n\n      {/* Tabs per navigazione */}\n      <Paper sx={{ mb: 3 }}>\n        <Tabs\n          value={activeTab}\n          onChange={handleTabChange}\n          indicatorColor=\"primary\"\n          textColor=\"primary\"\n          variant=\"fullWidth\"\n        >\n          <Tab\n            label={\n              <Stack direction=\"row\" alignItems=\"center\" spacing={1}>\n                <CableIcon />\n                <Box>\n                  <Typography variant=\"body2\" fontWeight=\"bold\">\n                    Cavi da Certificare\n                  </Typography>\n                  <Typography variant=\"caption\" color=\"text.secondary\">\n                    {filteredCavi.length} cavi totali\n                  </Typography>\n                </Box>\n                {statistics.caviNonCertificati > 0 && (\n                  <Badge badgeContent={statistics.caviNonCertificati} color=\"warning\">\n                    <WarningIcon />\n                  </Badge>\n                )}\n              </Stack>\n            }\n          />\n          <Tab\n            label={\n              <Stack direction=\"row\" alignItems=\"center\" spacing={1}>\n                <ScienceIcon />\n                <Box>\n                  <Typography variant=\"body2\" fontWeight=\"bold\">\n                    Certificazioni Completate\n                  </Typography>\n                  <Typography variant=\"caption\" color=\"text.secondary\">\n                    {filteredCertificazioni.length} certificazioni\n                  </Typography>\n                </Box>\n                {statistics.certificazioniOggi > 0 && (\n                  <Badge badgeContent={statistics.certificazioniOggi} color=\"success\">\n                    <CheckIcon />\n                  </Badge>\n                )}\n              </Stack>\n            }\n          />\n        </Tabs>\n      </Paper>\n\n      {/* Barra di ricerca e filtri avanzati */}\n      {renderSearchAndFilters()}\n\n      {/* Contenuto delle tabs */}\n      {!loading && activeTab === 0 && renderCaviTable()}\n      {!loading && activeTab === 1 && renderCertificazioniTable()}\n\n      {/* Dialogs */}\n      {renderCertificazioneDialog()}\n      {renderViewDialog()}\n\n      {/* Snackbar per notifiche */}\n      <Snackbar\n        open={snackbar.open}\n        autoHideDuration={6000}\n        onClose={closeSnackbar}\n        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}\n      >\n        <Alert onClose={closeSnackbar} severity={snackbar.severity} sx={{ width: '100%' }}>\n          {snackbar.message}\n        </Alert>\n      </Snackbar>\n\n      {/* Speed Dial per azioni rapide */}\n      <SpeedDial\n        ariaLabel=\"Azioni rapide\"\n        sx={{ position: 'fixed', bottom: 16, right: 16 }}\n        icon={<SpeedDialIcon />}\n      >\n        <SpeedDialAction\n          icon={<AddIcon />}\n          tooltipTitle=\"Nuova Certificazione\"\n          onClick={openCreateDialog}\n        />\n        <SpeedDialAction\n          icon={<ExportIcon />}\n          tooltipTitle=\"Esporta Tutto\"\n          onClick={handleExportAll}\n        />\n        <SpeedDialAction\n          icon={<RefreshIcon />}\n          tooltipTitle=\"Aggiorna Dati\"\n          onClick={loadInitialData}\n        />\n        <SpeedDialAction\n          icon={<ReportIcon />}\n          tooltipTitle=\"Report Avanzato\"\n          onClick={() => showSnackbar('Funzionalità in sviluppo', 'info')}\n        />\n      </SpeedDial>\n    </Container>\n  );\n});\n\nexport default CertificazioneCaviImproved;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,UAAU,EAAEC,mBAAmB,QAAQ,OAAO;AACnF,SACEC,GAAG,EACHC,UAAU,EACVC,MAAM,EACNC,KAAK,EACLC,IAAI,EACJC,IAAI,EACJC,WAAW,EACXC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,SAAS,EACTC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,YAAY,EACZC,gBAAgB,EAChBC,KAAK,EACLC,KAAK,EACLC,SAAS,EACTC,SAAS,EACTC,cAAc,EACdC,SAAS,EACTC,QAAQ,EACRC,UAAU,EACVC,IAAI,EACJC,GAAG,EACHC,UAAU,EACVC,cAAc,EACdC,OAAO,EACPC,KAAK,EACLC,IAAI,EACJC,OAAO,EACPC,KAAK,EACLC,cAAc,EACdC,QAAQ,EACRC,IAAI,EACJC,QAAQ,EACRC,YAAY,EACZC,YAAY,EACZC,QAAQ,EACRC,MAAM,EACNC,OAAO,EACPC,SAAS,EACTC,GAAG,EACHC,SAAS,EACTC,eAAe,EACfC,aAAa,QACR,eAAe;AACtB,SACEC,GAAG,IAAIC,OAAO,EACdC,MAAM,IAAIC,UAAU,EACpBC,UAAU,IAAIC,UAAU,EACxBC,YAAY,IAAIC,OAAO,EACvBC,QAAQ,IAAIC,YAAY,EACxBC,UAAU,IAAIC,QAAQ,EACtBC,MAAM,IAAIC,UAAU,EACpBC,IAAI,IAAIC,QAAQ,EAChBC,IAAI,IAAIC,QAAQ,EAChBC,KAAK,IAAIC,SAAS,EAClBC,KAAK,IAAIC,SAAS,EAClBC,WAAW,IAAIC,SAAS,EACxBC,OAAO,IAAIC,WAAW,EACtBC,MAAM,IAAIC,UAAU,EACpBC,KAAK,IAAIC,SAAS,EAClBC,KAAK,IAAIC,SAAS,EAClBC,WAAW,IAAIC,UAAU,EACzBC,UAAU,IAAIC,UAAU,EACxBC,QAAQ,IAAIC,YAAY,EACxBC,OAAO,IAAIC,WAAW,EACtBC,UAAU,IAAIC,cAAc,EAC5BC,UAAU,IAAIC,cAAc,EAC5BC,IAAI,IAAIC,QAAQ,EAChBC,KAAK,IAAIC,SAAS,EAClBC,QAAQ,IAAIC,YAAY,EACxBC,MAAM,IAAIC,UAAU,EACpBC,KAAK,IAAIC,SAAS,EAClBC,OAAO,IAAIC,WAAW,EACtBC,KAAK,IAAIC,SAAS,QACb,qBAAqB;AAE5B,OAAOC,qBAAqB,MAAM,sCAAsC;AACxE,OAAOC,WAAW,MAAM,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAErD,MAAMC,0BAA0B,gBAAAC,EAAA,cAAGpH,UAAU,CAAAqH,EAAA,GAAAD,EAAA,CAAC,CAAC;EAAEE,UAAU;EAAEC,SAAS;EAAEC;AAAQ,CAAC,EAAEC,GAAG,KAAK;EAAAL,EAAA;EACzF;EACA,MAAM,CAACM,OAAO,EAAEC,UAAU,CAAC,GAAG7H,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC8H,SAAS,EAAEC,YAAY,CAAC,GAAG/H,QAAQ,CAAC,CAAC,CAAC;EAC7C,MAAM,CAACgI,cAAc,EAAEC,iBAAiB,CAAC,GAAGjI,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAACkI,IAAI,EAAEC,OAAO,CAAC,GAAGnI,QAAQ,CAAC,EAAE,CAAC;EACpC,MAAM,CAACoI,SAAS,EAAEC,YAAY,CAAC,GAAGrI,QAAQ,CAAC,EAAE,CAAC;;EAE9C;EACA,MAAM,CAACsI,UAAU,EAAEC,aAAa,CAAC,GAAGvI,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACwI,YAAY,EAAEC,eAAe,CAAC,GAAGzI,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAAC0I,sBAAsB,EAAEC,yBAAyB,CAAC,GAAG3I,QAAQ,CAAC,EAAE,CAAC;EACxE,MAAM,CAAC4I,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG7I,QAAQ,CAAC,KAAK,CAAC;EACrE,MAAM,CAAC8I,OAAO,EAAEC,UAAU,CAAC,GAAG/I,QAAQ,CAAC;IACrCgJ,KAAK,EAAE,EAAE;IACTC,SAAS,EAAE,EAAE;IACbC,SAAS,EAAE,EAAE;IACbC,UAAU,EAAE,EAAE;IACdC,QAAQ,EAAE,EAAE;IACZC,gBAAgB,EAAE,EAAE;IACpBC,aAAa,EAAE,EAAE;IACjBC,SAAS,EAAE,EAAE;IACbC,cAAc,EAAE;EAClB,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAG1J,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAAC2J,YAAY,EAAEC,eAAe,CAAC,GAAG5J,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAAC6J,MAAM,EAAEC,SAAS,CAAC,GAAG9J,QAAQ,CAAC,qBAAqB,CAAC;EAC3D,MAAM,CAAC+J,SAAS,EAAEC,YAAY,CAAC,GAAGhK,QAAQ,CAAC,MAAM,CAAC;;EAElD;EACA,MAAM,CAACiK,UAAU,EAAEC,aAAa,CAAC,GAAGlK,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACmK,UAAU,EAAEC,aAAa,CAAC,GAAGpK,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACqK,YAAY,EAAEC,eAAe,CAAC,GAAGtK,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACuK,aAAa,EAAEC,gBAAgB,CAAC,GAAGxK,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACyK,QAAQ,EAAEC,WAAW,CAAC,GAAG1K,QAAQ,CAAC,KAAK,CAAC;;EAE/C;EACA,MAAM,CAAC2K,QAAQ,EAAEC,WAAW,CAAC,GAAG5K,QAAQ,CAAC;IAAE6K,IAAI,EAAE,KAAK;IAAEC,OAAO,EAAE,EAAE;IAAEC,QAAQ,EAAE;EAAO,CAAC,CAAC;EACxF,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGjL,QAAQ,CAAC,CAAC,CAAC;EAC3C,MAAM,CAACkL,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGnL,QAAQ,CAAC,KAAK,CAAC;;EAErE;EACA,MAAM,CAACoL,QAAQ,EAAEC,WAAW,CAAC,GAAGrL,QAAQ,CAAC;IACvCsL,OAAO,EAAE,EAAE;IACXC,YAAY,EAAE,EAAE;IAChBC,YAAY,EAAE,EAAE;IAChBC,kBAAkB,EAAE,EAAE;IACtBC,iBAAiB,EAAE,IAAI;IACvBC,iBAAiB,EAAE,EAAE;IACrBC,iBAAiB,EAAE,IAAI;IACvBC,IAAI,EAAE,EAAE;IACRC,oBAAoB,EAAE,EAAE;IACxBC,OAAO,EAAE,EAAE;IACXC,cAAc,EAAE,EAAE;IAClBC,YAAY,EAAE,EAAE;IAChBC,gBAAgB,EAAE;EACpB,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGpM,QAAQ,CAAC;IAC3CqM,UAAU,EAAE,CAAC;IACbC,eAAe,EAAE,CAAC;IAClBC,kBAAkB,EAAE,CAAC;IACrBC,wBAAwB,EAAE,CAAC;IAC3BC,kBAAkB,EAAE,CAAC;IACrBC,uBAAuB,EAAE;EAC3B,CAAC,CAAC;;EAEF;EACAzM,SAAS,CAAC,MAAM;IACd0M,eAAe,CAAC,CAAC;EACnB,CAAC,EAAE,CAACnF,UAAU,CAAC,CAAC;;EAEhB;EACAvH,SAAS,CAAC,MAAM;IACd2M,UAAU,CAAC,CAAC;EACd,CAAC,EAAE,CAAC1E,IAAI,EAAEI,UAAU,EAAEQ,OAAO,EAAEe,MAAM,EAAEE,SAAS,CAAC,CAAC;;EAElD;EACA9J,SAAS,CAAC,MAAM;IACd4M,oBAAoB,CAAC,CAAC;EACxB,CAAC,EAAE,CAAC7E,cAAc,EAAEM,UAAU,EAAEQ,OAAO,EAAEe,MAAM,EAAEE,SAAS,CAAC,CAAC;;EAE5D;EACA9J,SAAS,CAAC,MAAM;IACd6M,mBAAmB,CAAC,CAAC;EACvB,CAAC,EAAE,CAAC5E,IAAI,EAAEF,cAAc,CAAC,CAAC;EAE1B,MAAM2E,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACF9E,UAAU,CAAC,IAAI,CAAC;MAChBoD,WAAW,CAAC,CAAC,CAAC;;MAEd;MACAA,WAAW,CAAC,EAAE,CAAC;MACf,MAAM8B,QAAQ,CAAC,CAAC;MAEhB9B,WAAW,CAAC,EAAE,CAAC;MACf,MAAM+B,kBAAkB,CAAC,CAAC;MAE1B/B,WAAW,CAAC,EAAE,CAAC;MACf,MAAMgC,aAAa,CAAC,CAAC;MAErBhC,WAAW,CAAC,GAAG,CAAC;MAChB6B,mBAAmB,CAAC,CAAC;IAEvB,CAAC,CAAC,OAAOI,KAAK,EAAE;MACdC,YAAY,CAAC,0CAA0C,EAAE,OAAO,CAAC;MACjEzF,OAAO,CAAC,0CAA0C,CAAC;IACrD,CAAC,SAAS;MACRG,UAAU,CAAC,KAAK,CAAC;MACjBoD,WAAW,CAAC,CAAC,CAAC;IAChB;EACF,CAAC;EAED,MAAM+B,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrC,IAAI;MACF,MAAMI,IAAI,GAAG,MAAMrG,qBAAqB,CAACsG,iBAAiB,CAAC7F,UAAU,CAAC;MACtES,iBAAiB,CAACmF,IAAI,CAAC;MACvB,OAAOA,IAAI;IACb,CAAC,CAAC,OAAOF,KAAK,EAAE;MACdI,OAAO,CAACJ,KAAK,CAAC,8CAA8C,EAAEA,KAAK,CAAC;MACpE,MAAMA,KAAK;IACb;EACF,CAAC;EAED,MAAMH,QAAQ,GAAG,MAAAA,CAAA,KAAY;IAC3B,IAAI;MACF,MAAMK,IAAI,GAAG,MAAMpG,WAAW,CAACuG,OAAO,CAAC/F,UAAU,CAAC;MAClDW,OAAO,CAACiF,IAAI,CAAC;MACb,OAAOA,IAAI;IACb,CAAC,CAAC,OAAOF,KAAK,EAAE;MACdI,OAAO,CAACJ,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;MACxD,MAAMA,KAAK;IACb;EACF,CAAC;EAED,MAAMD,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACF,MAAMG,IAAI,GAAG,MAAMrG,qBAAqB,CAACyG,YAAY,CAAChG,UAAU,CAAC;MACjEa,YAAY,CAAC+E,IAAI,CAAC;MAClB,OAAOA,IAAI;IACb,CAAC,CAAC,OAAOF,KAAK,EAAE;MACdI,OAAO,CAACJ,KAAK,CAAC,yCAAyC,EAAEA,KAAK,CAAC;MAC/D,MAAMA,KAAK;IACb;EACF,CAAC;;EAED;EACA,MAAMJ,mBAAmB,GAAGA,CAAA,KAAM;IAChC,MAAMT,UAAU,GAAGnE,IAAI,CAACuF,MAAM;IAC9B,MAAMnB,eAAe,GAAGtE,cAAc,CAACyF,MAAM;IAC7C,MAAMlB,kBAAkB,GAAGF,UAAU,GAAGC,eAAe;IACvD,MAAME,wBAAwB,GAAGH,UAAU,GAAG,CAAC,GAAGqB,IAAI,CAACC,KAAK,CAAErB,eAAe,GAAGD,UAAU,GAAI,GAAG,CAAC,GAAG,CAAC;;IAEtG;IACA,MAAMuB,IAAI,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACC,YAAY,CAAC,CAAC;IACtC,MAAMrB,kBAAkB,GAAGzE,cAAc,CAAC+F,MAAM,CAACC,IAAI,IACnD,IAAIH,IAAI,CAACG,IAAI,CAACC,mBAAmB,CAAC,CAACH,YAAY,CAAC,CAAC,KAAKF,IACxD,CAAC,CAACH,MAAM;;IAER;IACA,MAAMS,cAAc,GAAG,IAAIL,IAAI,CAAC,CAAC;IACjCK,cAAc,CAACC,OAAO,CAACD,cAAc,CAACE,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC;IACpD,MAAM1B,uBAAuB,GAAG1E,cAAc,CAAC+F,MAAM,CAACC,IAAI,IACxD,IAAIH,IAAI,CAACG,IAAI,CAACC,mBAAmB,CAAC,IAAIC,cACxC,CAAC,CAACT,MAAM;;IAER;IACA,MAAMY,iBAAiB,GAAGnG,IAAI,CAAC6F,MAAM,CAACO,IAAI,IAAIC,oBAAoB,CAACD,IAAI,CAAC,CAAC,CAACb,MAAM;IAChF,MAAMe,oBAAoB,GAAGnC,UAAU,GAAGgC,iBAAiB;;IAE3D;IACA,MAAMI,aAAa,GAAGvG,IAAI,CAAC6F,MAAM,CAACO,IAAI,IAAII,eAAe,CAACJ,IAAI,CAAC,CAAC,CAACb,MAAM;IAEvErB,aAAa,CAAC;MACZC,UAAU;MACVC,eAAe;MACfC,kBAAkB;MAClB8B,iBAAiB;MACjBG,oBAAoB;MACpBC,aAAa;MACbjC,wBAAwB;MACxBC,kBAAkB;MAClBC;IACF,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMS,YAAY,GAAGA,CAACrC,OAAO,EAAEC,QAAQ,GAAG,MAAM,KAAK;IACnDH,WAAW,CAAC;MAAEC,IAAI,EAAE,IAAI;MAAEC,OAAO;MAAEC;IAAS,CAAC,CAAC;EAChD,CAAC;EAED,MAAM4D,aAAa,GAAGA,CAAA,KAAM;IAC1B/D,WAAW,CAAC;MAAE,GAAGD,QAAQ;MAAEE,IAAI,EAAE;IAAM,CAAC,CAAC;EAC3C,CAAC;EAED,MAAM+B,UAAU,GAAGA,CAAA,KAAM;IACvB,IAAIgC,QAAQ,GAAG1G,IAAI;;IAEnB;IACA,IAAII,UAAU,EAAE;MACd,MAAMuG,WAAW,GAAGvG,UAAU,CAACwG,WAAW,CAAC,CAAC;MAC5CF,QAAQ,GAAGA,QAAQ,CAACb,MAAM,CAACO,IAAI;QAAA,IAAAS,eAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,aAAA,EAAAC,aAAA;QAAA,OAC7Bb,IAAI,CAAChD,OAAO,CAACwD,WAAW,CAAC,CAAC,CAACM,QAAQ,CAACP,WAAW,CAAC,MAAAE,eAAA,GAChDT,IAAI,CAACrF,SAAS,cAAA8F,eAAA,uBAAdA,eAAA,CAAgBD,WAAW,CAAC,CAAC,CAACM,QAAQ,CAACP,WAAW,CAAC,OAAAG,qBAAA,GACnDV,IAAI,CAACe,mBAAmB,cAAAL,qBAAA,uBAAxBA,qBAAA,CAA0BF,WAAW,CAAC,CAAC,CAACM,QAAQ,CAACP,WAAW,CAAC,OAAAI,qBAAA,GAC7DX,IAAI,CAACgB,iBAAiB,cAAAL,qBAAA,uBAAtBA,qBAAA,CAAwBH,WAAW,CAAC,CAAC,CAACM,QAAQ,CAACP,WAAW,CAAC,OAAAK,aAAA,GAC3DZ,IAAI,CAACiB,OAAO,cAAAL,aAAA,uBAAZA,aAAA,CAAcJ,WAAW,CAAC,CAAC,CAACM,QAAQ,CAACP,WAAW,CAAC,OAAAM,aAAA,GACjDb,IAAI,CAACkB,OAAO,cAAAL,aAAA,uBAAZA,aAAA,CAAcL,WAAW,CAAC,CAAC,CAACM,QAAQ,CAACP,WAAW,CAAC;MAAA,CACnD,CAAC;IACH;;IAEA;IACA,IAAI/F,OAAO,CAACE,KAAK,EAAE;MACjB4F,QAAQ,GAAGA,QAAQ,CAACb,MAAM,CAACO,IAAI,IAAIA,IAAI,CAACmB,mBAAmB,KAAK3G,OAAO,CAACE,KAAK,CAAC;IAChF;IACA,IAAIF,OAAO,CAACG,SAAS,EAAE;MACrB2F,QAAQ,GAAGA,QAAQ,CAACb,MAAM,CAACO,IAAI,IAAIA,IAAI,CAACrF,SAAS,KAAKH,OAAO,CAACG,SAAS,CAAC;IAC1E;;IAEA;IACA,IAAIH,OAAO,CAACU,cAAc,EAAE;MAC1B,IAAIV,OAAO,CAACU,cAAc,KAAK,aAAa,EAAE;QAC5CoF,QAAQ,GAAGA,QAAQ,CAACb,MAAM,CAACO,IAAI,IAC7BtG,cAAc,CAAC0H,IAAI,CAAC1B,IAAI,IAAIA,IAAI,CAAC1C,OAAO,KAAKgD,IAAI,CAAChD,OAAO,CAC3D,CAAC;MACH,CAAC,MAAM,IAAIxC,OAAO,CAACU,cAAc,KAAK,iBAAiB,EAAE;QACvDoF,QAAQ,GAAGA,QAAQ,CAACb,MAAM,CAACO,IAAI,IAC7B,CAACtG,cAAc,CAAC0H,IAAI,CAAC1B,IAAI,IAAIA,IAAI,CAAC1C,OAAO,KAAKgD,IAAI,CAAChD,OAAO,CAC5D,CAAC;MACH;IACF;;IAEA;IACAsD,QAAQ,CAACe,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;MACtB,IAAIC,MAAM,GAAGF,CAAC,CAAC/F,MAAM,CAAC;MACtB,IAAIkG,MAAM,GAAGF,CAAC,CAAChG,MAAM,CAAC;MAEtB,IAAI,OAAOiG,MAAM,KAAK,QAAQ,EAAE;QAC9BA,MAAM,GAAGA,MAAM,CAAChB,WAAW,CAAC,CAAC;QAC7BiB,MAAM,GAAGA,MAAM,CAACjB,WAAW,CAAC,CAAC;MAC/B;MAEA,IAAI/E,SAAS,KAAK,KAAK,EAAE;QACvB,OAAO+F,MAAM,GAAGC,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC;MACjC,CAAC,MAAM;QACL,OAAOD,MAAM,GAAGC,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC;MACjC;IACF,CAAC,CAAC;IAEFtH,eAAe,CAACmG,QAAQ,CAAC;EAC3B,CAAC;EAED,MAAM/B,oBAAoB,GAAGA,CAAA,KAAM;IACjC,IAAI+B,QAAQ,GAAG5G,cAAc;;IAE7B;IACA,IAAIM,UAAU,EAAE;MACd,MAAMuG,WAAW,GAAGvG,UAAU,CAACwG,WAAW,CAAC,CAAC;MAC5CF,QAAQ,GAAGA,QAAQ,CAACb,MAAM,CAACC,IAAI;QAAA,IAAAgC,eAAA,EAAAC,qBAAA,EAAAC,UAAA;QAAA,OAC7BlC,IAAI,CAAC1C,OAAO,CAACwD,WAAW,CAAC,CAAC,CAACM,QAAQ,CAACP,WAAW,CAAC,MAAAmB,eAAA,GAChDhC,IAAI,CAAC9E,SAAS,cAAA8G,eAAA,uBAAdA,eAAA,CAAgBlB,WAAW,CAAC,CAAC,CAACM,QAAQ,CAACP,WAAW,CAAC,OAAAoB,qBAAA,GACnDjC,IAAI,CAACmC,kBAAkB,cAAAF,qBAAA,uBAAvBA,qBAAA,CAAyBnB,WAAW,CAAC,CAAC,CAACM,QAAQ,CAACP,WAAW,CAAC,OAAAqB,UAAA,GAC5DlC,IAAI,CAACnC,IAAI,cAAAqE,UAAA,uBAATA,UAAA,CAAWpB,WAAW,CAAC,CAAC,CAACM,QAAQ,CAACP,WAAW,CAAC;MAAA,CAChD,CAAC;IACH;;IAEA;IACA,IAAI/F,OAAO,CAACI,SAAS,EAAE;MACrB0F,QAAQ,GAAGA,QAAQ,CAACb,MAAM,CAACC,IAAI,IAAIA,IAAI,CAAC9E,SAAS,KAAKJ,OAAO,CAACI,SAAS,CAAC;IAC1E;IACA,IAAIJ,OAAO,CAACS,SAAS,EAAE;MACrBqF,QAAQ,GAAGA,QAAQ,CAACb,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACzE,SAAS,KAAKT,OAAO,CAACS,SAAS,CAAC;IAC1E;IACA,IAAIT,OAAO,CAACQ,aAAa,EAAE;MACzBsF,QAAQ,GAAGA,QAAQ,CAACb,MAAM,CAACC,IAAI,IAAIA,IAAI,CAAC9B,gBAAgB,KAAKpD,OAAO,CAACQ,aAAa,CAAC;IACrF;IACA,IAAIR,OAAO,CAACK,UAAU,EAAE;MACtByF,QAAQ,GAAGA,QAAQ,CAACb,MAAM,CAACC,IAAI,IAC7B,IAAIH,IAAI,CAACG,IAAI,CAACC,mBAAmB,CAAC,IAAI,IAAIJ,IAAI,CAAC/E,OAAO,CAACK,UAAU,CACnE,CAAC;IACH;IACA,IAAIL,OAAO,CAACM,QAAQ,EAAE;MACpBwF,QAAQ,GAAGA,QAAQ,CAACb,MAAM,CAACC,IAAI,IAC7B,IAAIH,IAAI,CAACG,IAAI,CAACC,mBAAmB,CAAC,IAAI,IAAIJ,IAAI,CAAC/E,OAAO,CAACM,QAAQ,CACjE,CAAC;IACH;IACA,IAAIN,OAAO,CAACO,gBAAgB,EAAE;MAC5B,MAAM+G,MAAM,GAAGC,UAAU,CAACvH,OAAO,CAACO,gBAAgB,CAAC;MACnDuF,QAAQ,GAAGA,QAAQ,CAACb,MAAM,CAACC,IAAI,IAC7BqC,UAAU,CAACrC,IAAI,CAACrC,iBAAiB,CAAC,IAAIyE,MACxC,CAAC;IACH;;IAEA;IACAxB,QAAQ,CAACe,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;MACtB,IAAIC,MAAM,GAAGF,CAAC,CAAC/F,MAAM,CAAC;MACtB,IAAIkG,MAAM,GAAGF,CAAC,CAAChG,MAAM,CAAC;MAEtB,IAAIA,MAAM,KAAK,qBAAqB,EAAE;QACpCiG,MAAM,GAAG,IAAIjC,IAAI,CAACiC,MAAM,CAAC;QACzBC,MAAM,GAAG,IAAIlC,IAAI,CAACkC,MAAM,CAAC;MAC3B,CAAC,MAAM,IAAI,OAAOD,MAAM,KAAK,QAAQ,EAAE;QACrCA,MAAM,GAAGA,MAAM,CAAChB,WAAW,CAAC,CAAC;QAC7BiB,MAAM,GAAGA,MAAM,CAACjB,WAAW,CAAC,CAAC;MAC/B;MAEA,IAAI/E,SAAS,KAAK,KAAK,EAAE;QACvB,OAAO+F,MAAM,GAAGC,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC;MACjC,CAAC,MAAM;QACL,OAAOD,MAAM,GAAGC,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC;MACjC;IACF,CAAC,CAAC;IAEFpH,yBAAyB,CAACiG,QAAQ,CAAC;EACrC,CAAC;;EAED;EACA,MAAM0B,cAAc,GAAGA,CAAA,KAAM;IAC3B,IAAIxI,SAAS,KAAK,CAAC,EAAE;MACnBqF,YAAY,CAAC,gEAAgE,EAAE,SAAS,CAAC;MACzF;IACF;IACAzC,WAAW,CAAC,CAACD,QAAQ,CAAC;IACtBD,gBAAgB,CAAC,EAAE,CAAC;IACpB2C,YAAY,CACV,CAAC1C,QAAQ,GACL,4EAA4E,GAC5E,gCAAgC,EACpC,MACF,CAAC;EACH,CAAC;EAED,MAAM8F,mBAAmB,GAAIC,MAAM,IAAK;IACtChG,gBAAgB,CAACiG,IAAI,IAAI;MACvB,MAAMC,YAAY,GAAGD,IAAI,CAACrB,QAAQ,CAACoB,MAAM,CAAC,GACtCC,IAAI,CAAC1C,MAAM,CAAC4C,EAAE,IAAIA,EAAE,KAAKH,MAAM,CAAC,GAChC,CAAC,GAAGC,IAAI,EAAED,MAAM,CAAC;MAErBrD,YAAY,CACV,GAAGuD,YAAY,CAACjD,MAAM,6BAA6B,EACnD,MACF,CAAC;MACD,OAAOiD,YAAY;IACrB,CAAC,CAAC;EACJ,CAAC;EAED,MAAME,cAAc,GAAGA,CAAA,KAAM;IAC3B,IAAI9I,SAAS,KAAK,CAAC,EAAE;IAErB,MAAM+I,MAAM,GAAGnI,sBAAsB,CAACoI,GAAG,CAAC9C,IAAI,IAAIA,IAAI,CAAC+C,iBAAiB,CAAC;IACzEvG,gBAAgB,CAACqG,MAAM,CAAC;IACxB1D,YAAY,CAAC,YAAY0D,MAAM,CAACpD,MAAM,6BAA6B,EAAE,SAAS,CAAC;EACjF,CAAC;EAED,MAAMuD,cAAc,GAAGA,CAAA,KAAM;IAC3BxG,gBAAgB,CAAC,EAAE,CAAC;IACpB2C,YAAY,CAAC,sBAAsB,EAAE,MAAM,CAAC;EAC9C,CAAC;;EAED;EACA,MAAM8D,iBAAiB,GAAIC,MAAM,IAAK;IACpC,OAAOlJ,cAAc,CAAC0H,IAAI,CAAC1B,IAAI,IAAIA,IAAI,CAAC1C,OAAO,KAAK4F,MAAM,CAAC;EAC7D,CAAC;;EAED;EACA,MAAM3C,oBAAoB,GAAID,IAAI,IAAK;IACrC;IACA,MAAM6C,YAAY,GAAG7C,IAAI,CAACmB,mBAAmB,KAAK,YAAY,IAC1CnB,IAAI,CAACmB,mBAAmB,KAAK,YAAY,IACzCnB,IAAI,CAACmB,mBAAmB,KAAK,QAAQ;;IAEzD;IACA;IACA,OAAO0B,YAAY;EACrB,CAAC;;EAED;EACA,MAAMzC,eAAe,GAAIJ,IAAI,IAAK;IAChC,MAAM8C,WAAW,GAAG9C,IAAI,CAAC+C,YAAY,KAAK,CAAC;IAC3C,MAAMC,eAAe,GAAGhD,IAAI,CAACiD,qBAAqB,IAAIjD,IAAI,CAACkD,mBAAmB;IAC9E,OAAOJ,WAAW,IAAIE,eAAe;EACvC,CAAC;;EAED;EACA,MAAMG,gCAAgC,GAAInD,IAAI,IAAK;IACjD,MAAM6C,YAAY,GAAG7C,IAAI,CAACmB,mBAAmB,KAAK,YAAY,IAC1CnB,IAAI,CAACmB,mBAAmB,KAAK,YAAY,IACzCnB,IAAI,CAACmB,mBAAmB,KAAK,QAAQ;IAEzD,IAAI,CAAC0B,YAAY,EAAE;MACjB,OAAO,yEAAyE;IAClF;IAEA,OAAO,+CAA+C;EACxD,CAAC;;EAED;EACA,MAAMO,eAAe,GAAGA,CAACC,KAAK,EAAEC,QAAQ,KAAK;IAC3C7J,YAAY,CAAC6J,QAAQ,CAAC;IACtBlI,cAAc,CAAC,CAAC,CAAC;IACjBnB,aAAa,CAAC,EAAE,CAAC;IACjBQ,UAAU,CAAC;MAAEC,KAAK,EAAE,EAAE;MAAEC,SAAS,EAAE,EAAE;MAAEC,SAAS,EAAE;IAAG,CAAC,CAAC;EACzD,CAAC;;EAED;EACA,MAAM2I,gBAAgB,GAAGA,CAACC,kBAAkB,GAAG,IAAI,KAAK;IACtD1H,aAAa,CAAC,QAAQ,CAAC;IACvBE,eAAe,CAAC,IAAI,CAAC;;IAErB;IACA,IAAIwH,kBAAkB,EAAE;MACtBzG,WAAW,CAAC;QACVC,OAAO,EAAEwG,kBAAkB,CAACxG,OAAO;QACnCC,YAAY,EAAE,EAAE;QAChBC,YAAY,EAAE,EAAE;QAChBC,kBAAkB,EAAEqG,kBAAkB,CAACC,eAAe,IAAID,kBAAkB,CAACE,aAAa,IAAI,EAAE;QAChGtG,iBAAiB,EAAE,IAAI;QACvBC,iBAAiB,EAAE,EAAE;QACrBC,iBAAiB,EAAE,IAAI;QACvBC,IAAI,EAAE,EAAE;QACRC,oBAAoB,EAAE,EAAE;QACxBC,OAAO,EAAE,EAAE;QACXC,cAAc,EAAE,EAAE;QAClBC,YAAY,EAAE,EAAE;QAChBC,gBAAgB,EAAE;MACpB,CAAC,CAAC;MACFiB,YAAY,CAAC,QAAQ2E,kBAAkB,CAACxG,OAAO,8BAA8B,EAAE,SAAS,CAAC;IAC3F,CAAC,MAAM;MACL;MACAD,WAAW,CAAC;QACVC,OAAO,EAAE,EAAE;QACXC,YAAY,EAAE,EAAE;QAChBC,YAAY,EAAE,EAAE;QAChBC,kBAAkB,EAAE,EAAE;QACtBC,iBAAiB,EAAE,IAAI;QACvBC,iBAAiB,EAAE,EAAE;QACrBC,iBAAiB,EAAE,IAAI;QACvBC,IAAI,EAAE,EAAE;QACRC,oBAAoB,EAAE,EAAE;QACxBC,OAAO,EAAE,EAAE;QACXC,cAAc,EAAE,EAAE;QAClBC,YAAY,EAAE,EAAE;QAChBC,gBAAgB,EAAE;MACpB,CAAC,CAAC;IACJ;IAEAhC,aAAa,CAAC,IAAI,CAAC;EACrB,CAAC;EAED,MAAM+H,WAAW,GAAGA,CAAA,KAAM;IACxB/H,aAAa,CAAC,KAAK,CAAC;IACpBI,eAAe,CAAC,IAAI,CAAC;IACrBF,aAAa,CAAC,EAAE,CAAC;EACnB,CAAC;;EAED;EACA,MAAM8H,gBAAgB,GAAGA,CAACC,KAAK,EAAEC,KAAK,KAAK;IACzC/G,WAAW,CAACoF,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAAC0B,KAAK,GAAGC;IACX,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMC,gBAAgB,GAAI/D,IAAI,IAAK;IACjCjD,WAAW,CAACoF,IAAI,KAAK;MACnB,GAAGA,IAAI;MACPnF,OAAO,EAAEgD,IAAI,CAAChD,OAAO;MACrBG,kBAAkB,EAAE6C,IAAI,CAACyD,eAAe,IAAIzD,IAAI,CAAC0D,aAAa,IAAI;IACpE,CAAC,CAAC,CAAC;EACL,CAAC;;EAED;EACA,MAAMM,0BAA0B,GAAG,MAAAA,CAAA,KAAY;IAC7C,IAAI;MACF,IAAI,CAAClH,QAAQ,CAACE,OAAO,IAAI,CAACF,QAAQ,CAACG,YAAY,IAAI,CAACH,QAAQ,CAACI,YAAY,IAAI,CAACJ,QAAQ,CAACO,iBAAiB,EAAE;QACxGwB,YAAY,CAAC,mCAAmC,EAAE,SAAS,CAAC;QAC5D;MACF;;MAEA;MACA,MAAMmB,IAAI,GAAGpG,IAAI,CAACqK,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAClH,OAAO,KAAKF,QAAQ,CAACE,OAAO,CAAC;MAC3D,IAAI,CAACgD,IAAI,EAAE;QACTnB,YAAY,CAAC,kBAAkB,EAAE,OAAO,CAAC;QACzC;MACF;MAEA,IAAI,CAACoB,oBAAoB,CAACD,IAAI,CAAC,EAAE;QAC/B,MAAMmE,SAAS,GAAGhB,gCAAgC,CAACnD,IAAI,CAAC;QACxDnB,YAAY,CAAC,oCAAoCsF,SAAS,EAAE,EAAE,OAAO,CAAC;QACtE;MACF;;MAEA;MACA,IAAIxB,iBAAiB,CAAC7F,QAAQ,CAACE,OAAO,CAAC,EAAE;QACvC6B,YAAY,CAAC,iCAAiC,EAAE,SAAS,CAAC;QAC1D;MACF;;MAEA;MACA,IAAI,CAACuB,eAAe,CAACJ,IAAI,CAAC,EAAE;QAC1B,MAAMoE,QAAQ,GAAGC,MAAM,CAACC,OAAO,CAC7B,uBAAuBtE,IAAI,CAAChD,OAAO,2CAA2C,GAC9E,uBAAuBgD,IAAI,CAAC+C,YAAY,KAAK,CAAC,GAAG,eAAe,GAC1C/C,IAAI,CAAC+C,YAAY,KAAK,CAAC,GAAG,yBAAyB,GACnD/C,IAAI,CAAC+C,YAAY,KAAK,CAAC,GAAG,uBAAuB,GACjD,mBAAmB,MAAM,GAC/C,kDAAkD,GAClD,sEACF,CAAC;QAED,IAAI,CAACqB,QAAQ,EAAE;UACb;QACF;MACF;MAEAvH,sBAAsB,CAAC,IAAI,CAAC;MAC5B,MAAMpE,qBAAqB,CAAC8L,oBAAoB,CAACrL,UAAU,EAAE4D,QAAQ,CAAC;MACtE+B,YAAY,CAAC,oCAAoC,EAAE,SAAS,CAAC;MAC7D8E,WAAW,CAAC,CAAC;MACb,MAAMjF,kBAAkB,CAAC,CAAC;MAC1BF,mBAAmB,CAAC,CAAC;IACvB,CAAC,CAAC,OAAOI,KAAK,EAAE;MACdC,YAAY,CAAC,+CAA+C,IAAID,KAAK,CAACpC,OAAO,IAAI,oBAAoB,CAAC,EAAE,OAAO,CAAC;IAClH,CAAC,SAAS;MACRK,sBAAsB,CAAC,KAAK,CAAC;IAC/B;EACF,CAAC;EAED,MAAM2H,iBAAiB,GAAG,MAAOtJ,cAAc,IAAK;IAClD,IAAI;MACF2B,sBAAsB,CAAC,IAAI,CAAC;MAC5BgC,YAAY,CAAC,6BAA6B,EAAE,MAAM,CAAC;MAEnD,MAAM4F,QAAQ,GAAG,MAAMhM,qBAAqB,CAACiM,WAAW,CAACxL,UAAU,EAAEgC,cAAc,CAACuH,iBAAiB,CAAC;MAEtG,IAAIgC,QAAQ,CAACE,QAAQ,EAAE;QACrB;QACA,MAAMC,SAAS,GAAGP,MAAM,CAAC9H,IAAI,CAACkI,QAAQ,CAACE,QAAQ,EAAE,QAAQ,CAAC;QAC1D,IAAIC,SAAS,EAAE;UACb/F,YAAY,CAAC,6CAA6C,EAAE,SAAS,CAAC;QACxE,CAAC,MAAM;UACL;UACA,MAAMgG,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;UACxCF,IAAI,CAACG,IAAI,GAAGP,QAAQ,CAACE,QAAQ;UAC7BE,IAAI,CAACI,QAAQ,GAAG,kBAAkB/J,cAAc,CAAC2G,kBAAkB,MAAM;UACzEiD,QAAQ,CAACI,IAAI,CAACC,WAAW,CAACN,IAAI,CAAC;UAC/BA,IAAI,CAACO,KAAK,CAAC,CAAC;UACZN,QAAQ,CAACI,IAAI,CAACG,WAAW,CAACR,IAAI,CAAC;UAC/BhG,YAAY,CAAC,uCAAuC,EAAE,SAAS,CAAC;QAClE;MACF,CAAC,MAAM,IAAI4F,QAAQ,CAACa,WAAW,EAAE;QAC/B;QACA,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACC,IAAI,CAAChB,QAAQ,CAACa,WAAW,CAAC,CAAC,EAAE;UAAEI,IAAI,EAAE;QAAkB,CAAC,CAAC;QAChF,MAAMC,GAAG,GAAGC,GAAG,CAACC,eAAe,CAACN,IAAI,CAAC;QACrC,MAAMV,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;QACxCF,IAAI,CAACG,IAAI,GAAGW,GAAG;QACfd,IAAI,CAACI,QAAQ,GAAG,kBAAkB/J,cAAc,CAAC2G,kBAAkB,MAAM;QACzEiD,QAAQ,CAACI,IAAI,CAACC,WAAW,CAACN,IAAI,CAAC;QAC/BA,IAAI,CAACO,KAAK,CAAC,CAAC;QACZN,QAAQ,CAACI,IAAI,CAACG,WAAW,CAACR,IAAI,CAAC;QAC/Be,GAAG,CAACE,eAAe,CAACH,GAAG,CAAC;QACxB9G,YAAY,CAAC,4BAA4B,EAAE,SAAS,CAAC;MACvD,CAAC,MAAM;QACLA,YAAY,CAAC,sCAAsC,EAAE,OAAO,CAAC;MAC/D;IACF,CAAC,CAAC,OAAOD,KAAK,EAAE;MACdI,OAAO,CAACJ,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/CC,YAAY,CAAC,oCAAoC,IAAID,KAAK,CAACpC,OAAO,IAAI,oBAAoB,CAAC,EAAE,OAAO,CAAC;IACvG,CAAC,SAAS;MACRK,sBAAsB,CAAC,KAAK,CAAC;IAC/B;EACF,CAAC;EAED,MAAMkJ,0BAA0B,GAAG,MAAO7K,cAAc,IAAK;IAC3D,IAAImJ,MAAM,CAACC,OAAO,CAAC,mDAAmDpJ,cAAc,CAAC2G,kBAAkB,GAAG,CAAC,EAAE;MAC3G,IAAI;QACFhF,sBAAsB,CAAC,IAAI,CAAC;QAC5B,MAAMpE,qBAAqB,CAACuN,oBAAoB,CAAC9M,UAAU,EAAEgC,cAAc,CAACuH,iBAAiB,CAAC;QAC9F5D,YAAY,CAAC,uCAAuC,EAAE,SAAS,CAAC;QAChE,MAAMH,kBAAkB,CAAC,CAAC;QAC1BF,mBAAmB,CAAC,CAAC;MACvB,CAAC,CAAC,OAAOI,KAAK,EAAE;QACdC,YAAY,CAAC,kDAAkD,IAAID,KAAK,CAACpC,OAAO,IAAI,oBAAoB,CAAC,EAAE,OAAO,CAAC;MACrH,CAAC,SAAS;QACRK,sBAAsB,CAAC,KAAK,CAAC;MAC/B;IACF;EACF,CAAC;;EAED;EACA,MAAMoJ,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAIhK,aAAa,CAACkD,MAAM,KAAK,CAAC,EAAE;MAC9BN,YAAY,CAAC,8BAA8B,EAAE,SAAS,CAAC;MACvD;IACF;IAEA,IAAIwF,MAAM,CAACC,OAAO,CAAC,iCAAiCrI,aAAa,CAACkD,MAAM,kBAAkB,CAAC,EAAE;MAC3F,IAAI;QACFtC,sBAAsB,CAAC,IAAI,CAAC;QAC5B,KAAK,MAAMwF,EAAE,IAAIpG,aAAa,EAAE;UAC9B,MAAMxD,qBAAqB,CAACuN,oBAAoB,CAAC9M,UAAU,EAAEmJ,EAAE,CAAC;QAClE;QACAxD,YAAY,CAAC,GAAG5C,aAAa,CAACkD,MAAM,wCAAwC,EAAE,SAAS,CAAC;QACxFjD,gBAAgB,CAAC,EAAE,CAAC;QACpB,MAAMwC,kBAAkB,CAAC,CAAC;QAC1BF,mBAAmB,CAAC,CAAC;MACvB,CAAC,CAAC,OAAOI,KAAK,EAAE;QACdC,YAAY,CAAC,gDAAgD,EAAE,OAAO,CAAC;MACzE,CAAC,SAAS;QACRhC,sBAAsB,CAAC,KAAK,CAAC;MAC/B;IACF;EACF,CAAC;EAED,MAAMqJ,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAIjK,aAAa,CAACkD,MAAM,KAAK,CAAC,EAAE;MAC9BN,YAAY,CAAC,8BAA8B,EAAE,SAAS,CAAC;MACvD;IACF;IAEA,IAAI;MACFhC,sBAAsB,CAAC,IAAI,CAAC;MAC5B;MACA,MAAMsJ,aAAa,GAAGzM,cAAc,CAAC+F,MAAM,CAACC,IAAI,IAC9CzD,aAAa,CAAC6E,QAAQ,CAACpB,IAAI,CAAC+C,iBAAiB,CAC/C,CAAC;;MAED;MACA,MAAM2D,UAAU,GAAGC,WAAW,CAACF,aAAa,CAAC;MAC7CG,WAAW,CAACF,UAAU,EAAE,kBAAkB,IAAI7G,IAAI,CAAC,CAAC,CAACgH,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;MAEvF3H,YAAY,CAAC,GAAG5C,aAAa,CAACkD,MAAM,2BAA2B,EAAE,SAAS,CAAC;IAC7E,CAAC,CAAC,OAAOP,KAAK,EAAE;MACdC,YAAY,CAAC,2BAA2B,EAAE,OAAO,CAAC;IACpD,CAAC,SAAS;MACRhC,sBAAsB,CAAC,KAAK,CAAC;IAC/B;EACF,CAAC;;EAED;EACA,MAAMwJ,WAAW,GAAIvH,IAAI,IAAK;IAC5B,MAAM2H,OAAO,GAAG,CAAC,SAAS,EAAE,oBAAoB,EAAE,MAAM,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,YAAY,EAAE,WAAW,CAAC;IAC3H,MAAMC,IAAI,GAAG5H,IAAI,CAAC0D,GAAG,CAAC9C,IAAI,IAAI,CAC5BA,IAAI,CAAC1C,OAAO,EACZ0C,IAAI,CAACmC,kBAAkB,EACvB,IAAItC,IAAI,CAACG,IAAI,CAACC,mBAAmB,CAAC,CAACgH,kBAAkB,CAAC,CAAC,EACvDjH,IAAI,CAAC9E,SAAS,EACd8E,IAAI,CAACzE,SAAS,EACdyE,IAAI,CAACvC,kBAAkB,EACvBuC,IAAI,CAACrC,iBAAiB,EACtBqC,IAAI,CAAC9B,gBAAgB,CACtB,CAAC;IAEF,OAAO,CAAC6I,OAAO,EAAE,GAAGC,IAAI,CAAC,CAAClE,GAAG,CAACoE,GAAG,IAAIA,GAAG,CAACC,IAAI,CAAC,GAAG,CAAC,CAAC,CAACA,IAAI,CAAC,IAAI,CAAC;EAChE,CAAC;EAED,MAAMP,WAAW,GAAGA,CAACQ,OAAO,EAAEC,QAAQ,KAAK;IACzC,MAAMxB,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACsB,OAAO,CAAC,EAAE;MAAEpB,IAAI,EAAE;IAA0B,CAAC,CAAC;IACrE,MAAMb,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;IACxC,IAAIF,IAAI,CAACI,QAAQ,KAAK+B,SAAS,EAAE;MAC/B,MAAMrB,GAAG,GAAGC,GAAG,CAACC,eAAe,CAACN,IAAI,CAAC;MACrCV,IAAI,CAACoC,YAAY,CAAC,MAAM,EAAEtB,GAAG,CAAC;MAC9Bd,IAAI,CAACoC,YAAY,CAAC,UAAU,EAAEF,QAAQ,CAAC;MACvClC,IAAI,CAACqC,KAAK,CAACC,UAAU,GAAG,QAAQ;MAChCrC,QAAQ,CAACI,IAAI,CAACC,WAAW,CAACN,IAAI,CAAC;MAC/BA,IAAI,CAACO,KAAK,CAAC,CAAC;MACZN,QAAQ,CAACI,IAAI,CAACG,WAAW,CAACR,IAAI,CAAC;IACjC;EACF,CAAC;EAED,MAAMuC,eAAe,GAAGA,CAAA,KAAM;IAC5B,MAAMhB,UAAU,GAAGC,WAAW,CAACjM,sBAAsB,CAAC;IACtDkM,WAAW,CAACF,UAAU,EAAE,wBAAwB,IAAI7G,IAAI,CAAC,CAAC,CAACgH,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;IAC7F3H,YAAY,CAAC,yBAAyB,EAAE,SAAS,CAAC;EACpD,CAAC;;EAED;EACAhN,mBAAmB,CAACwH,GAAG,EAAE,OAAO;IAC9BgO,kBAAkB,EAAGC,MAAM,IAAK;MAC9B,IAAIA,MAAM,KAAK,oBAAoB,EAAE;QACnC/D,gBAAgB,CAAC,CAAC;MACpB,CAAC,MAAM,IAAI+D,MAAM,KAAK,0BAA0B,EAAE;QAChD7N,YAAY,CAAC,CAAC,CAAC;MACjB;IACF;EACF,CAAC,CAAC,CAAC;;EAEH;EACA,MAAM8N,mBAAmB,GAAIC,KAAK,IAAK;IACrC,MAAMC,UAAU,GAAG,CAACtM,WAAW,GAAG,CAAC,IAAIE,YAAY;IACnD,MAAMqM,QAAQ,GAAGD,UAAU,GAAGpM,YAAY;IAC1C,OAAOmM,KAAK,CAACG,KAAK,CAACF,UAAU,EAAEC,QAAQ,CAAC;EAC1C,CAAC;EAED,MAAME,aAAa,GAAIJ,KAAK,IAAKpI,IAAI,CAACyI,IAAI,CAACL,KAAK,CAACrI,MAAM,GAAG9D,YAAY,CAAC;;EAEvE;EACA,MAAMyM,eAAe,GAAGA,CAACC,KAAK,EAAElE,KAAK,KAAK;IACxC,OAAO,CAAC,GAAG,IAAImE,GAAG,CAACD,KAAK,CAACvF,GAAG,CAACyF,IAAI,IAAIA,IAAI,CAACpE,KAAK,CAAC,CAAC,CAACpE,MAAM,CAACyI,OAAO,CAAC,CAAC,CAAC;EACrE,CAAC;;EAED;EACA,MAAMC,eAAe,GAAGA,CAAA,kBACtBvP,OAAA,CAAC3G,KAAK;IAACmW,EAAE,EAAE;MAAEC,CAAC,EAAE,CAAC;MAAEC,EAAE,EAAE,CAAC;MAAEC,OAAO,EAAE;IAAU,CAAE;IAAAC,QAAA,eAC7C5P,OAAA,CAAC/E,KAAK;MAAC4U,SAAS,EAAC,KAAK;MAACC,OAAO,EAAE,CAAE;MAACC,UAAU,EAAC,QAAQ;MAACC,cAAc,EAAC,eAAe;MAACC,QAAQ,EAAC,MAAM;MAAAL,QAAA,gBAEnG5P,OAAA,CAAC/E,KAAK;QAAC4U,SAAS,EAAC,KAAK;QAACE,UAAU,EAAC,QAAQ;QAACD,OAAO,EAAE,CAAE;QAAAF,QAAA,gBACpD5P,OAAA,CAACR,SAAS;UAAC0Q,KAAK,EAAC,SAAS;UAACC,QAAQ,EAAC;QAAO;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC9CvQ,OAAA,CAAC9G,GAAG;UAAA0W,QAAA,gBACF5P,OAAA,CAAC7G,UAAU;YAACqX,OAAO,EAAC,IAAI;YAACC,UAAU,EAAC,MAAM;YAACjB,EAAE,EAAE;cAAEkB,UAAU,EAAE;YAAE,CAAE;YAAAd,QAAA,EAC9D3K,UAAU,CAACE;UAAU;YAAAiL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACZ,CAAC,eACbvQ,OAAA,CAAC7G,UAAU;YAACqX,OAAO,EAAC,SAAS;YAACN,KAAK,EAAC,gBAAgB;YAAAN,QAAA,EAAC;UAErD;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAERvQ,OAAA,CAAC/E,KAAK;QAAC4U,SAAS,EAAC,KAAK;QAACE,UAAU,EAAC,QAAQ;QAACD,OAAO,EAAE,CAAE;QAAAF,QAAA,gBACpD5P,OAAA,CAACtC,SAAS;UAACwS,KAAK,EAAC,SAAS;UAACC,QAAQ,EAAC;QAAO;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC9CvQ,OAAA,CAAC9G,GAAG;UAAA0W,QAAA,gBACF5P,OAAA,CAAC7G,UAAU;YAACqX,OAAO,EAAC,IAAI;YAACC,UAAU,EAAC,MAAM;YAACjB,EAAE,EAAE;cAAEkB,UAAU,EAAE;YAAE,CAAE;YAAAd,QAAA,EAC9D3K,UAAU,CAACG;UAAe;YAAAgL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjB,CAAC,eACbvQ,OAAA,CAAC7G,UAAU;YAACqX,OAAO,EAAC,SAAS;YAACN,KAAK,EAAC,gBAAgB;YAAAN,QAAA,EAAC;UAErD;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAERvQ,OAAA,CAAC/E,KAAK;QAAC4U,SAAS,EAAC,KAAK;QAACE,UAAU,EAAC,QAAQ;QAACD,OAAO,EAAE,CAAE;QAAAF,QAAA,gBACpD5P,OAAA,CAACxC,SAAS;UAAC0S,KAAK,EAAC,MAAM;UAACC,QAAQ,EAAC;QAAO;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC3CvQ,OAAA,CAAC9G,GAAG;UAAA0W,QAAA,gBACF5P,OAAA,CAAC7G,UAAU;YAACqX,OAAO,EAAC,IAAI;YAACC,UAAU,EAAC,MAAM;YAACjB,EAAE,EAAE;cAAEkB,UAAU,EAAE;YAAE,CAAE;YAAAd,QAAA,EAC9D3K,UAAU,CAACkC;UAAiB;YAAAiJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC,eACbvQ,OAAA,CAAC7G,UAAU;YAACqX,OAAO,EAAC,SAAS;YAACN,KAAK,EAAC,gBAAgB;YAAAN,QAAA,EAAC;UAErD;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAERvQ,OAAA,CAAC/E,KAAK;QAAC4U,SAAS,EAAC,KAAK;QAACE,UAAU,EAAC,QAAQ;QAACD,OAAO,EAAE,CAAE;QAAAF,QAAA,gBACpD5P,OAAA,CAAC9G,GAAG;UAACsW,EAAE,EAAE;YACPmB,KAAK,EAAE,EAAE;YACTC,MAAM,EAAE,EAAE;YACVC,YAAY,EAAE,KAAK;YACnBlB,OAAO,EAAE1K,UAAU,CAACK,wBAAwB,IAAI,EAAE,GAAG,cAAc,GAC1DL,UAAU,CAACK,wBAAwB,IAAI,EAAE,GAAG,cAAc,GAAG,YAAY;YAClFwL,OAAO,EAAE,MAAM;YACff,UAAU,EAAE,QAAQ;YACpBC,cAAc,EAAE;UAClB,CAAE;UAAAJ,QAAA,eACA5P,OAAA,CAAC7G,UAAU;YAACqX,OAAO,EAAC,SAAS;YAACC,UAAU,EAAC,MAAM;YAACP,KAAK,EAAC,OAAO;YAAAN,QAAA,GAC1D3K,UAAU,CAACK,wBAAwB,EAAC,GACvC;UAAA;YAAA8K,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eACNvQ,OAAA,CAAC9G,GAAG;UAAA0W,QAAA,gBACF5P,OAAA,CAAC7G,UAAU;YAACqX,OAAO,EAAC,OAAO;YAACC,UAAU,EAAC,QAAQ;YAACjB,EAAE,EAAE;cAAEkB,UAAU,EAAE;YAAE,CAAE;YAAAd,QAAA,EAAC;UAEvE;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbvQ,OAAA,CAAC7G,UAAU;YAACqX,OAAO,EAAC,SAAS;YAACN,KAAK,EAAC,gBAAgB;YAAAN,QAAA,GACjD3K,UAAU,CAACM,kBAAkB,EAAC,OACjC;UAAA;YAAA6K,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAGRvQ,OAAA,CAAC5G,MAAM;QACLoX,OAAO,EAAC,UAAU;QAClBO,IAAI,EAAC,OAAO;QACZC,SAAS,eAAEhR,OAAA,CAAC1B,UAAU;UAAA8R,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAC1BU,OAAO,EAAEA,CAAA,KAAMxF,MAAM,CAAC9H,IAAI,CAAC,aAAarD,UAAU,qBAAqB,EAAE,QAAQ,CAAE;QACnFkP,EAAE,EAAE;UAAE0B,EAAE,EAAE;QAAO,CAAE;QAAAtB,QAAA,EACpB;MAED;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACR;;EAED;EACA,MAAMY,sBAAsB,GAAGA,CAAA,kBAC7BnR,OAAA,CAAC3G,KAAK;IAACmW,EAAE,EAAE;MAAEC,CAAC,EAAE,CAAC;MAAEC,EAAE,EAAE;IAAE,CAAE;IAAAE,QAAA,gBACzB5P,OAAA,CAAC1G,IAAI;MAAC8X,SAAS;MAACtB,OAAO,EAAE,CAAE;MAACC,UAAU,EAAC,QAAQ;MAAAH,QAAA,gBAC7C5P,OAAA,CAAC1G,IAAI;QAAC+V,IAAI;QAACgC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAA1B,QAAA,eACvB5P,OAAA,CAACnG,SAAS;UACR0X,SAAS;UACTC,WAAW,EAAC,0CAA0C;UACtDtG,KAAK,EAAE9J,UAAW;UAClBqQ,QAAQ,EAAGC,CAAC,IAAKrQ,aAAa,CAACqQ,CAAC,CAACC,MAAM,CAACzG,KAAK,CAAE;UAC/C0G,UAAU,EAAE;YACVC,cAAc,eACZ7R,OAAA,CAACjF,cAAc;cAAC+W,QAAQ,EAAC,OAAO;cAAAlC,QAAA,eAC9B5P,OAAA,CAAC1D,UAAU;gBAAA8T,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CACjB;YACDwB,YAAY,EAAE3Q,UAAU,iBACtBpB,OAAA,CAACjF,cAAc;cAAC+W,QAAQ,EAAC,KAAK;cAAAlC,QAAA,eAC5B5P,OAAA,CAACrF,UAAU;gBAACsW,OAAO,EAAEA,CAAA,KAAM5P,aAAa,CAAC,EAAE,CAAE;gBAAC0P,IAAI,EAAC,OAAO;gBAAAnB,QAAA,eACxD5P,OAAA,CAAC1C,SAAS;kBAAA8S,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UAEpB;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAEPvQ,OAAA,CAAC1G,IAAI;QAAC+V,IAAI;QAACgC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAA1B,QAAA,eACvB5P,OAAA,CAAC5G,MAAM;UACLmY,SAAS;UACTf,OAAO,EAAC,UAAU;UAClBQ,SAAS,eAAEhR,OAAA,CAACxD,UAAU;YAAA4T,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC1BU,OAAO,EAAEA,CAAA,KAAMtP,sBAAsB,CAAC,CAACD,mBAAmB,CAAE;UAC5DwO,KAAK,EAAE8B,MAAM,CAACC,MAAM,CAACrQ,OAAO,CAAC,CAAC4G,IAAI,CAAC0J,CAAC,IAAIA,CAAC,CAAC,GAAG,SAAS,GAAG,SAAU;UAAAtC,QAAA,GACpE,SACQ,EAACoC,MAAM,CAACC,MAAM,CAACrQ,OAAO,CAAC,CAACiF,MAAM,CAACqL,CAAC,IAAIA,CAAC,CAAC,CAAC3L,MAAM,GAAG,CAAC,IAAI,IAAIyL,MAAM,CAACC,MAAM,CAACrQ,OAAO,CAAC,CAACiF,MAAM,CAACqL,CAAC,IAAIA,CAAC,CAAC,CAAC3L,MAAM,GAAG;QAAA;UAAA6J,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAEPvQ,OAAA,CAAC1G,IAAI;QAAC+V,IAAI;QAACgC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAA1B,QAAA,eACvB5P,OAAA,CAAC5G,MAAM;UACLmY,SAAS;UACTf,OAAO,EAAC,UAAU;UAClBQ,SAAS,EAAEzN,QAAQ,gBAAGvD,OAAA,CAAC1C,SAAS;YAAA8S,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAAGvQ,OAAA,CAACtC,SAAS;YAAA0S,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACpDU,OAAO,EAAE7H,cAAe;UACxB8G,KAAK,EAAE3M,QAAQ,GAAG,WAAW,GAAG,SAAU;UAC1C4O,QAAQ,EAAEvR,SAAS,KAAK,CAAE;UAAAgP,QAAA,EAEzBrM,QAAQ,GAAG,gBAAgB,GAAG;QAAoB;UAAA6M,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7C;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAEPvQ,OAAA,CAAC1G,IAAI;QAAC+V,IAAI;QAACgC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAA1B,QAAA,eACvB5P,OAAA,CAAC5G,MAAM;UACLmY,SAAS;UACTf,OAAO,EAAC,UAAU;UAClBQ,SAAS,eAAEhR,OAAA,CAAClC,UAAU;YAAAsS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC1BU,OAAO,EAAEzC,eAAgB;UACzB2D,QAAQ,EAAEvR,SAAS,KAAK,CAAC,IAAIY,sBAAsB,CAAC+E,MAAM,KAAK,CAAE;UAAAqJ,QAAA,EAEhEhP,SAAS,KAAK,CAAC,GAAG,8BAA8B,GAAG;QAAwB;UAAAwP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAEPvQ,OAAA,CAAC1G,IAAI;QAAC+V,IAAI;QAACgC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAA1B,QAAA,eACvB5P,OAAA,CAAC5G,MAAM;UACLmY,SAAS;UACTf,OAAO,EAAC,WAAW;UACnBQ,SAAS,eAAEhR,OAAA,CAAC5D,OAAO;YAAAgU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACvBU,OAAO,EAAEtG,gBAAiB;UAAAiF,QAAA,EAC3B;QAED;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGPvQ,OAAA,CAAC1E,QAAQ;MAAC8W,EAAE,EAAE1Q,mBAAoB;MAAAkO,QAAA,gBAChC5P,OAAA,CAAChF,OAAO;QAACwU,EAAE,EAAE;UAAE6C,EAAE,EAAE;QAAE;MAAE;QAAAjC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC1BvQ,OAAA,CAAC7G,UAAU;QAACqX,OAAO,EAAC,OAAO;QAACN,KAAK,EAAC,gBAAgB;QAACV,EAAE,EAAE;UAAEE,EAAE,EAAE;QAAE,CAAE;QAAAE,QAAA,EAC9DhP,SAAS,KAAK,CAAC,GAAG,iBAAiB,GAAG;MAA2B;QAAAwP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxD,CAAC,eAEbvQ,OAAA,CAAC1G,IAAI;QAAC8X,SAAS;QAACtB,OAAO,EAAE,CAAE;QAAAF,QAAA,GAExBhP,SAAS,KAAK,CAAC,iBACdZ,OAAA,CAAAE,SAAA;UAAA0P,QAAA,gBACE5P,OAAA,CAAC1G,IAAI;YAAC+V,IAAI;YAACgC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAA1B,QAAA,eACvB5P,OAAA,CAAClG,WAAW;cAACyX,SAAS;cAACR,IAAI,EAAC,OAAO;cAAAnB,QAAA,gBACjC5P,OAAA,CAACjG,UAAU;gBAAA6V,QAAA,EAAC;cAAmB;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAC5CvQ,OAAA,CAAChG,MAAM;gBACLkR,KAAK,EAAEtJ,OAAO,CAACE,KAAM;gBACrB2P,QAAQ,EAAGC,CAAC,IAAK7P,UAAU,CAAC;kBAAC,GAAGD,OAAO;kBAAEE,KAAK,EAAE4P,CAAC,CAACC,MAAM,CAACzG;gBAAK,CAAC,CAAE;gBAAA0E,QAAA,gBAEjE5P,OAAA,CAAC/F,QAAQ;kBAACiR,KAAK,EAAC,EAAE;kBAAA0E,QAAA,EAAC;gBAAK;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,eACnCvQ,OAAA,CAAC/F,QAAQ;kBAACiR,KAAK,EAAC,YAAY;kBAAA0E,QAAA,EAAC;gBAAU;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,eAClDvQ,OAAA,CAAC/F,QAAQ;kBAACiR,KAAK,EAAC,gBAAgB;kBAAA0E,QAAA,EAAC;gBAAc;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,eAC1DvQ,OAAA,CAAC/F,QAAQ;kBAACiR,KAAK,EAAC,UAAU;kBAAA0E,QAAA,EAAC;gBAAQ;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eAEPvQ,OAAA,CAAC1G,IAAI;YAAC+V,IAAI;YAACgC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAA1B,QAAA,eACvB5P,OAAA,CAAClG,WAAW;cAACyX,SAAS;cAACR,IAAI,EAAC,OAAO;cAAAnB,QAAA,gBACjC5P,OAAA,CAACjG,UAAU;gBAAA6V,QAAA,EAAC;cAAS;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAClCvQ,OAAA,CAAChG,MAAM;gBACLkR,KAAK,EAAEtJ,OAAO,CAACG,SAAU;gBACzB0P,QAAQ,EAAGC,CAAC,IAAK7P,UAAU,CAAC;kBAAC,GAAGD,OAAO;kBAAEG,SAAS,EAAE2P,CAAC,CAACC,MAAM,CAACzG;gBAAK,CAAC,CAAE;gBAAA0E,QAAA,gBAErE5P,OAAA,CAAC/F,QAAQ;kBAACiR,KAAK,EAAC,EAAE;kBAAA0E,QAAA,EAAC;gBAAK;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,EAClC,CAAC,GAAG,IAAInB,GAAG,CAACpO,IAAI,CAAC4I,GAAG,CAAC0B,CAAC,IAAIA,CAAC,CAACvJ,SAAS,CAAC,CAAC,CAAC,CAAC8E,MAAM,CAACyI,OAAO,CAAC,CAAC1F,GAAG,CAAC0I,GAAG,iBAC/DtS,OAAA,CAAC/F,QAAQ;kBAAWiR,KAAK,EAAEoH,GAAI;kBAAA1C,QAAA,EAAE0C;gBAAG,GAArBA,GAAG;kBAAAlC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAA6B,CAChD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eAEPvQ,OAAA,CAAC1G,IAAI;YAAC+V,IAAI;YAACgC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAA1B,QAAA,eACvB5P,OAAA,CAAClG,WAAW;cAACyX,SAAS;cAACR,IAAI,EAAC,OAAO;cAAAnB,QAAA,gBACjC5P,OAAA,CAACjG,UAAU;gBAAA6V,QAAA,EAAC;cAAoB;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAC7CvQ,OAAA,CAAChG,MAAM;gBACLkR,KAAK,EAAEtJ,OAAO,CAACU,cAAe;gBAC9BmP,QAAQ,EAAGC,CAAC,IAAK7P,UAAU,CAAC;kBAAC,GAAGD,OAAO;kBAAEU,cAAc,EAAEoP,CAAC,CAACC,MAAM,CAACzG;gBAAK,CAAC,CAAE;gBAAA0E,QAAA,gBAE1E5P,OAAA,CAAC/F,QAAQ;kBAACiR,KAAK,EAAC,EAAE;kBAAA0E,QAAA,EAAC;gBAAK;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,eACnCvQ,OAAA,CAAC/F,QAAQ;kBAACiR,KAAK,EAAC,aAAa;kBAAA0E,QAAA,EAAC;gBAAW;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,eACpDvQ,OAAA,CAAC/F,QAAQ;kBAACiR,KAAK,EAAC,iBAAiB;kBAAA0E,QAAA,EAAC;gBAAe;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA,eACP,CACH,EAGA3P,SAAS,KAAK,CAAC,iBACdZ,OAAA,CAAAE,SAAA;UAAA0P,QAAA,gBACE5P,OAAA,CAAC1G,IAAI;YAAC+V,IAAI;YAACgC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAA1B,QAAA,eACvB5P,OAAA,CAAClG,WAAW;cAACyX,SAAS;cAACR,IAAI,EAAC,OAAO;cAAAnB,QAAA,gBACjC5P,OAAA,CAACjG,UAAU;gBAAA6V,QAAA,EAAC;cAAS;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAClCvQ,OAAA,CAAChG,MAAM;gBACLkR,KAAK,EAAEtJ,OAAO,CAACI,SAAU;gBACzByP,QAAQ,EAAGC,CAAC,IAAK7P,UAAU,CAAC;kBAAC,GAAGD,OAAO;kBAAEI,SAAS,EAAE0P,CAAC,CAACC,MAAM,CAACzG;gBAAK,CAAC,CAAE;gBAAA0E,QAAA,gBAErE5P,OAAA,CAAC/F,QAAQ;kBAACiR,KAAK,EAAC,EAAE;kBAAA0E,QAAA,EAAC;gBAAK;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,EAClC,CAAC,GAAG,IAAInB,GAAG,CAACtO,cAAc,CAAC8I,GAAG,CAAC0B,CAAC,IAAIA,CAAC,CAACtJ,SAAS,CAAC,CAAC,CAAC,CAAC6E,MAAM,CAACyI,OAAO,CAAC,CAAC1F,GAAG,CAAC2I,EAAE,iBACxEvS,OAAA,CAAC/F,QAAQ;kBAAUiR,KAAK,EAAEqH,EAAG;kBAAA3C,QAAA,EAAE2C;gBAAE,GAAlBA,EAAE;kBAAAnC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAA2B,CAC7C,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eAEPvQ,OAAA,CAAC1G,IAAI;YAAC+V,IAAI;YAACgC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAA1B,QAAA,eACvB5P,OAAA,CAAClG,WAAW;cAACyX,SAAS;cAACR,IAAI,EAAC,OAAO;cAAAnB,QAAA,gBACjC5P,OAAA,CAACjG,UAAU;gBAAA6V,QAAA,EAAC;cAAc;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACvCvQ,OAAA,CAAChG,MAAM;gBACLkR,KAAK,EAAEtJ,OAAO,CAACQ,aAAc;gBAC7BqP,QAAQ,EAAGC,CAAC,IAAK7P,UAAU,CAAC;kBAAC,GAAGD,OAAO;kBAAEQ,aAAa,EAAEsP,CAAC,CAACC,MAAM,CAACzG;gBAAK,CAAC,CAAE;gBAAA0E,QAAA,gBAEzE5P,OAAA,CAAC/F,QAAQ;kBAACiR,KAAK,EAAC,EAAE;kBAAA0E,QAAA,EAAC;gBAAK;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,eACnCvQ,OAAA,CAAC/F,QAAQ;kBAACiR,KAAK,EAAC,UAAU;kBAAA0E,QAAA,EAAC;gBAAQ;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,eAC9CvQ,OAAA,CAAC/F,QAAQ;kBAACiR,KAAK,EAAC,cAAc;kBAAA0E,QAAA,EAAC;gBAAY;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,eACtDvQ,OAAA,CAAC/F,QAAQ;kBAACiR,KAAK,EAAC,eAAe;kBAAA0E,QAAA,EAAC;gBAAa;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eAEPvQ,OAAA,CAAC1G,IAAI;YAAC+V,IAAI;YAACgC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAA1B,QAAA,eACvB5P,OAAA,CAACnG,SAAS;cACR0X,SAAS;cACTR,IAAI,EAAC,OAAO;cACZyB,KAAK,EAAC,0BAAqB;cAC3B1F,IAAI,EAAC,QAAQ;cACb5B,KAAK,EAAEtJ,OAAO,CAACO,gBAAiB;cAChCsP,QAAQ,EAAGC,CAAC,IAAK7P,UAAU,CAAC;gBAAC,GAAGD,OAAO;gBAAEO,gBAAgB,EAAEuP,CAAC,CAACC,MAAM,CAACzG;cAAK,CAAC,CAAE;cAC5EsG,WAAW,EAAC;YAAS;cAAApB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAEPvQ,OAAA,CAAC1G,IAAI;YAAC+V,IAAI;YAACgC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAA1B,QAAA,eACvB5P,OAAA,CAACnG,SAAS;cACR0X,SAAS;cACTR,IAAI,EAAC,OAAO;cACZyB,KAAK,EAAC,aAAa;cACnB1F,IAAI,EAAC,MAAM;cACX5B,KAAK,EAAEtJ,OAAO,CAACK,UAAW;cAC1BwP,QAAQ,EAAGC,CAAC,IAAK7P,UAAU,CAAC;gBAAC,GAAGD,OAAO;gBAAEK,UAAU,EAAEyP,CAAC,CAACC,MAAM,CAACzG;cAAK,CAAC,CAAE;cACtEuH,eAAe,EAAE;gBAAEC,MAAM,EAAE;cAAK;YAAE;cAAAtC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAEPvQ,OAAA,CAAC1G,IAAI;YAAC+V,IAAI;YAACgC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAA1B,QAAA,eACvB5P,OAAA,CAACnG,SAAS;cACR0X,SAAS;cACTR,IAAI,EAAC,OAAO;cACZyB,KAAK,EAAC,WAAW;cACjB1F,IAAI,EAAC,MAAM;cACX5B,KAAK,EAAEtJ,OAAO,CAACM,QAAS;cACxBuP,QAAQ,EAAGC,CAAC,IAAK7P,UAAU,CAAC;gBAAC,GAAGD,OAAO;gBAAEM,QAAQ,EAAEwP,CAAC,CAACC,MAAM,CAACzG;cAAK,CAAC,CAAE;cACpEuH,eAAe,EAAE;gBAAEC,MAAM,EAAE;cAAK;YAAE;cAAAtC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA,eACP,CACH,eAEDvQ,OAAA,CAAC1G,IAAI;UAAC+V,IAAI;UAACgC,EAAE,EAAE,EAAG;UAAAzB,QAAA,eAChB5P,OAAA,CAAC/E,KAAK;YAAC4U,SAAS,EAAC,KAAK;YAACC,OAAO,EAAE,CAAE;YAACE,cAAc,EAAC,UAAU;YAAAJ,QAAA,eAC1D5P,OAAA,CAAC5G,MAAM;cACLoX,OAAO,EAAC,UAAU;cAClBO,IAAI,EAAC,OAAO;cACZE,OAAO,EAAEA,CAAA,KAAMpP,UAAU,CAAC;gBACxBC,KAAK,EAAE,EAAE;gBAAEC,SAAS,EAAE,EAAE;gBAAEC,SAAS,EAAE,EAAE;gBAAEC,UAAU,EAAE,EAAE;gBACvDC,QAAQ,EAAE,EAAE;gBAAEC,gBAAgB,EAAE,EAAE;gBAAEC,aAAa,EAAE,EAAE;gBAAEC,SAAS,EAAE,EAAE;gBACpEC,cAAc,EAAE;cAClB,CAAC,CAAE;cAAAsN,QAAA,EACJ;YAED;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,EAGVhN,QAAQ,IAAIF,aAAa,CAACkD,MAAM,GAAG,CAAC,iBACnCvG,OAAA,CAAAE,SAAA;MAAA0P,QAAA,gBACE5P,OAAA,CAAChF,OAAO;QAACwU,EAAE,EAAE;UAAE6C,EAAE,EAAE;QAAE;MAAE;QAAAjC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC1BvQ,OAAA,CAAC/E,KAAK;QAAC4U,SAAS,EAAC,KAAK;QAACC,OAAO,EAAE,CAAE;QAACC,UAAU,EAAC,QAAQ;QAAAH,QAAA,gBACpD5P,OAAA,CAAC7G,UAAU;UAACqX,OAAO,EAAC,OAAO;UAAAZ,QAAA,GACxBvM,aAAa,CAACkD,MAAM,EAAC,uBACxB;QAAA;UAAA6J,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbvQ,OAAA,CAAC5G,MAAM;UACL2X,IAAI,EAAC,OAAO;UACZP,OAAO,EAAC,UAAU;UAClBS,OAAO,EAAEvH,cAAe;UAAAkG,QAAA,EACzB;QAED;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTvQ,OAAA,CAAC5G,MAAM;UACL2X,IAAI,EAAC,OAAO;UACZP,OAAO,EAAC,UAAU;UAClBS,OAAO,EAAEnH,cAAe;UAAA8F,QAAA,EACzB;QAED;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTvQ,OAAA,CAAC5G,MAAM;UACL2X,IAAI,EAAC,OAAO;UACZP,OAAO,EAAC,UAAU;UAClBQ,SAAS,eAAEhR,OAAA,CAAClC,UAAU;YAAAsS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC1BU,OAAO,EAAE3D,gBAAiB;UAAAsC,QAAA,EAC3B;QAED;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTvQ,OAAA,CAAC5G,MAAM;UACL2X,IAAI,EAAC,OAAO;UACZP,OAAO,EAAC,UAAU;UAClBN,KAAK,EAAC,OAAO;UACbc,SAAS,eAAEhR,OAAA,CAAChD,UAAU;YAAAoT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC1BU,OAAO,EAAE5D,gBAAiB;UAAAuC,QAAA,EAC3B;QAED;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA,eACR,CACH;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACI,CACR;;EAED;EACA,MAAMoC,eAAe,GAAGA,CAAA,KAAM;IAC5B,MAAMC,YAAY,GAAGjE,mBAAmB,CAACrN,YAAY,CAAC;IAEtD,IAAIA,YAAY,CAACiF,MAAM,KAAK,CAAC,EAAE;MAC7B,oBACEvG,OAAA,CAAC5F,KAAK;QAACyJ,QAAQ,EAAC,MAAM;QAAA+L,QAAA,EACnBxO,UAAU,IAAIQ,OAAO,CAACE,KAAK,IAAIF,OAAO,CAACG,SAAS,GAC7C,4CAA4C,GAC5C;MAAyB;QAAAqO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxB,CAAC;IAEZ;IAEA,oBACEvQ,OAAA,CAAAE,SAAA;MAAA0P,QAAA,gBACE5P,OAAA,CAACxF,cAAc;QAACqY,SAAS,EAAExZ,KAAM;QAAAuW,QAAA,eAC/B5P,OAAA,CAAC3F,KAAK;UAAC0W,IAAI,EAAC,OAAO;UAAAnB,QAAA,gBACjB5P,OAAA,CAACvF,SAAS;YAAAmV,QAAA,eACR5P,OAAA,CAACtF,QAAQ;cAAAkV,QAAA,gBACP5P,OAAA,CAACzF,SAAS;gBAAAqV,QAAA,EAAC;cAAO;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC9BvQ,OAAA,CAACzF,SAAS;gBAAAqV,QAAA,EAAC;cAAS;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAChCvQ,OAAA,CAACzF,SAAS;gBAAAqV,QAAA,EAAC;cAAO;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC9BvQ,OAAA,CAACzF,SAAS;gBAAAqV,QAAA,EAAC;cAAQ;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC/BvQ,OAAA,CAACzF,SAAS;gBAAAqV,QAAA,EAAC;cAAM;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC7BvQ,OAAA,CAACzF,SAAS;gBAAAqV,QAAA,EAAC;cAAK;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC5BvQ,OAAA,CAACzF,SAAS;gBAAAqV,QAAA,EAAC;cAAK;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC5BvQ,OAAA,CAACzF,SAAS;gBAAAqV,QAAA,EAAC;cAAY;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eACnCvQ,OAAA,CAACzF,SAAS;gBAAAqV,QAAA,EAAC;cAAW;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAClCvQ,OAAA,CAACzF,SAAS;gBAAAqV,QAAA,EAAC;cAAM;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACZvQ,OAAA,CAAC1F,SAAS;YAAAsV,QAAA,EACPgD,YAAY,CAAChJ,GAAG,CAAExC,IAAI,IAAK;cAC1B,MAAM0L,aAAa,GAAG/I,iBAAiB,CAAC3C,IAAI,CAAChD,OAAO,CAAC;cACrD,MAAM2O,cAAc,GAAG1L,oBAAoB,CAACD,IAAI,CAAC;cACjD,MAAM4L,eAAe,GAAG,CAACD,cAAc,GAAGxI,gCAAgC,CAACnD,IAAI,CAAC,GAAG,EAAE;cAErF,oBACEpH,OAAA,CAACtF,QAAQ;gBAAAkV,QAAA,gBACP5P,OAAA,CAACzF,SAAS;kBAAAqV,QAAA,eACR5P,OAAA,CAAC7G,UAAU;oBAACqX,OAAO,EAAC,OAAO;oBAACC,UAAU,EAAC,QAAQ;oBAAAb,QAAA,EAC5CxI,IAAI,CAAChD;kBAAO;oBAAAgM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eACZvQ,OAAA,CAACzF,SAAS;kBAAAqV,QAAA,EAAExI,IAAI,CAACrF;gBAAS;kBAAAqO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACvCvQ,OAAA,CAACzF,SAAS;kBAAAqV,QAAA,EAAExI,IAAI,CAACiB;gBAAO;kBAAA+H,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACrCvQ,OAAA,CAACzF,SAAS;kBAAAqV,QAAA,EAAExI,IAAI,CAACe;gBAAmB;kBAAAiI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACjDvQ,OAAA,CAACzF,SAAS;kBAAAqV,QAAA,EAAExI,IAAI,CAACgB;gBAAiB;kBAAAgI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC/CvQ,OAAA,CAACzF,SAAS;kBAAAqV,QAAA,GAAExI,IAAI,CAACyD,eAAe,IAAIzD,IAAI,CAAC0D,aAAa,EAAC,IAAE;gBAAA;kBAAAsF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eACrEvQ,OAAA,CAACzF,SAAS;kBAAAqV,QAAA,eACR5P,OAAA,CAAC9E,IAAI;oBACH6V,IAAI,EAAC,OAAO;oBACZyB,KAAK,EAAEpL,IAAI,CAACmB,mBAAoB;oBAChC2H,KAAK,EAAE9I,IAAI,CAACmB,mBAAmB,KAAK,YAAY,GAAG,SAAS,GAAG;kBAAU;oBAAA6H,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1E;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACO,CAAC,eACZvQ,OAAA,CAACzF,SAAS;kBAAAqV,QAAA,EACP,CAAC,MAAM;oBACN,MAAMzF,YAAY,GAAG/C,IAAI,CAAC+C,YAAY,IAAI,CAAC;oBAC3C,MAAM8I,iBAAiB,GAAG9I,YAAY,KAAK,CAAC,GAAG,eAAe,GACrCA,YAAY,KAAK,CAAC,GAAG,eAAe,GACpCA,YAAY,KAAK,CAAC,GAAG,aAAa,GAClCA,YAAY,KAAK,CAAC,GAAG,UAAU,GAC/B,aAAa;oBACtC,MAAM+I,MAAM,GAAG/I,YAAY,KAAK,CAAC,GAAG,SAAS,GAC/BA,YAAY,KAAK,CAAC,GAAG,OAAO,GAAG,SAAS;oBAEtD,oBACEnK,OAAA,CAAC7E,OAAO;sBAACgY,KAAK,EAAE,aAAa/L,IAAI,CAACiD,qBAAqB,IAAI,eAAe,cAAcjD,IAAI,CAACkD,mBAAmB,IAAI,eAAe,EAAG;sBAAAsF,QAAA,eACpI5P,OAAA,CAAC9E,IAAI;wBACH6V,IAAI,EAAC,OAAO;wBACZyB,KAAK,EAAES,iBAAkB;wBACzB/C,KAAK,EAAEgD,MAAO;wBACdE,IAAI,EAAEjJ,YAAY,KAAK,CAAC,gBAAGnK,OAAA,CAACtC,SAAS;0BAAA0S,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,gBAAGvQ,OAAA,CAACpC,WAAW;0BAAAwS,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAE;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC5D;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACK,CAAC;kBAEd,CAAC,EAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACK,CAAC,eACZvQ,OAAA,CAACzF,SAAS;kBAAAqV,QAAA,EACPkD,aAAa,gBACZ9S,OAAA,CAAC9E,IAAI;oBACH6V,IAAI,EAAC,OAAO;oBACZqC,IAAI,eAAEpT,OAAA,CAACtC,SAAS;sBAAA0S,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAE;oBACpBiC,KAAK,EAAC,aAAa;oBACnBtC,KAAK,EAAC;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChB,CAAC,gBAEFvQ,OAAA,CAAC9E,IAAI;oBACH6V,IAAI,EAAC,OAAO;oBACZqC,IAAI,eAAEpT,OAAA,CAACpC,WAAW;sBAAAwS,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAE;oBACtBiC,KAAK,EAAC,iBAAiB;oBACvBtC,KAAK,EAAC;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChB;gBACF;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACQ,CAAC,eACZvQ,OAAA,CAACzF,SAAS;kBAAAqV,QAAA,EACPkD,aAAa,gBACZ9S,OAAA,CAAC7E,OAAO;oBAACgY,KAAK,EAAC,yBAAsB;oBAAAvD,QAAA,eACnC5P,OAAA,CAAC9E,IAAI;sBACHkY,IAAI,eAAEpT,OAAA,CAACtC,SAAS;wBAAA0S,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAE;sBACpBiC,KAAK,EAAC,aAAa;sBACnBtC,KAAK,EAAC,SAAS;sBACfa,IAAI,EAAC;oBAAO;sBAAAX,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACb;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACK,CAAC,GACRwC,cAAc,gBAChB/S,OAAA,CAAC7E,OAAO;oBAACgY,KAAK,EAAC,qCAAqC;oBAAAvD,QAAA,eAClD5P,OAAA,CAACrF,UAAU;sBACToW,IAAI,EAAC,OAAO;sBACZE,OAAO,EAAEA,CAAA,KAAMtG,gBAAgB,CAACvD,IAAI,CAAE;sBACtC8I,KAAK,EAAC,SAAS;sBAAAN,QAAA,eAEf5P,OAAA,CAAC5D,OAAO;wBAAAgU,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACD;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC,gBAEVvQ,OAAA,CAAC7E,OAAO;oBAACgY,KAAK,EAAEH,eAAgB;oBAAApD,QAAA,eAC9B5P,OAAA;sBAAA4P,QAAA,eACE5P,OAAA,CAACrF,UAAU;wBACToW,IAAI,EAAC,OAAO;wBACZoB,QAAQ;wBACRlB,OAAO,EAAEA,CAAA,KAAMhL,YAAY,CAAC+M,eAAe,EAAE,SAAS,CAAE;wBAAApD,QAAA,eAExD5P,OAAA,CAACJ,SAAS;0BAAAwQ,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACT;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACA;gBACV;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACQ,CAAC;cAAA,GA3FCnJ,IAAI,CAAChD,OAAO;gBAAAgM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OA4FjB,CAAC;YAEf,CAAC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC,EAEhBvB,aAAa,CAAC1N,YAAY,CAAC,GAAG,CAAC,iBAC9BtB,OAAA,CAAC9G,GAAG;QAACsW,EAAE,EAAE;UAAEsB,OAAO,EAAE,MAAM;UAAEd,cAAc,EAAE,QAAQ;UAAEqD,EAAE,EAAE;QAAE,CAAE;QAAAzD,QAAA,eAC5D5P,OAAA,CAAClF,UAAU;UACTwY,KAAK,EAAEtE,aAAa,CAAC1N,YAAY,CAAE;UACnCiS,IAAI,EAAEhR,WAAY;UAClBkP,QAAQ,EAAEA,CAAChH,KAAK,EAAES,KAAK,KAAK1I,cAAc,CAAC0I,KAAK,CAAE;UAClDgF,KAAK,EAAC;QAAS;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CACN;IAAA,eACD,CAAC;EAEP,CAAC;;EAED;EACA,MAAMiD,yBAAyB,GAAGA,CAAA,KAAM;IACtC,MAAMZ,YAAY,GAAGjE,mBAAmB,CAACnN,sBAAsB,CAAC;IAEhE,IAAIA,sBAAsB,CAAC+E,MAAM,KAAK,CAAC,EAAE;MACvC,oBACEvG,OAAA,CAAC5F,KAAK;QAACyJ,QAAQ,EAAC,MAAM;QAAA+L,QAAA,EACnBxO,UAAU,IAAIQ,OAAO,CAACI,SAAS,GAC5B,uDAAuD,GACvD;MAAoC;QAAAoO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnC,CAAC;IAEZ;IAEA,oBACEvQ,OAAA,CAAAE,SAAA;MAAA0P,QAAA,gBACE5P,OAAA,CAACxF,cAAc;QAACqY,SAAS,EAAExZ,KAAM;QAAAuW,QAAA,eAC/B5P,OAAA,CAAC3F,KAAK;UAAC0W,IAAI,EAAC,OAAO;UAAAnB,QAAA,gBACjB5P,OAAA,CAACvF,SAAS;YAAAmV,QAAA,eACR5P,OAAA,CAACtF,QAAQ;cAAAkV,QAAA,GACNrM,QAAQ,iBACPvD,OAAA,CAACzF,SAAS;gBAACkZ,OAAO,EAAC,UAAU;gBAAA7D,QAAA,eAC3B5P,OAAA,CAACrF,UAAU;kBACToW,IAAI,EAAC,OAAO;kBACZE,OAAO,EAAE5N,aAAa,CAACkD,MAAM,KAAK/E,sBAAsB,CAAC+E,MAAM,GAAGuD,cAAc,GAAGJ,cAAe;kBAAAkG,QAAA,EAEjGvM,aAAa,CAACkD,MAAM,KAAK/E,sBAAsB,CAAC+E,MAAM,gBAAGvG,OAAA,CAAC1C,SAAS;oBAAA8S,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,gBAAGvQ,OAAA,CAACtC,SAAS;oBAAA0S,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7E;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CACZ,eACDvQ,OAAA,CAACzF,SAAS;gBAAAqV,QAAA,eACR5P,OAAA,CAAC/E,KAAK;kBAAC4U,SAAS,EAAC,KAAK;kBAACE,UAAU,EAAC,QAAQ;kBAACD,OAAO,EAAE,CAAE;kBAAAF,QAAA,gBACpD5P,OAAA,CAAC7G,UAAU;oBAACqX,OAAO,EAAC,OAAO;oBAACC,UAAU,EAAC,MAAM;oBAAAb,QAAA,EAAC;kBAAc;oBAAAQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACzEvQ,OAAA,CAACrF,UAAU;oBAACoW,IAAI,EAAC,OAAO;oBAACE,OAAO,EAAEA,CAAA,KAAM;sBACtCrO,SAAS,CAAC,oBAAoB,CAAC;sBAC/BE,YAAY,CAACD,SAAS,KAAK,KAAK,GAAG,MAAM,GAAG,KAAK,CAAC;oBACpD,CAAE;oBAAA+M,QAAA,EACCjN,MAAM,KAAK,oBAAoB,GAAIE,SAAS,KAAK,KAAK,gBAAG7C,OAAA,CAAClB,cAAc;sBAAAsR,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,gBAAGvQ,OAAA,CAACpB,cAAc;sBAAAwR,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,gBAAIvQ,OAAA,CAACpB,cAAc;sBAAAwR,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7G,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACZvQ,OAAA,CAACzF,SAAS;gBAAAqV,QAAA,EAAC;cAAO;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC9BvQ,OAAA,CAACzF,SAAS;gBAAAqV,QAAA,eACR5P,OAAA,CAAC/E,KAAK;kBAAC4U,SAAS,EAAC,KAAK;kBAACE,UAAU,EAAC,QAAQ;kBAACD,OAAO,EAAE,CAAE;kBAAAF,QAAA,gBACpD5P,OAAA,CAAC7G,UAAU;oBAACqX,OAAO,EAAC,OAAO;oBAACC,UAAU,EAAC,MAAM;oBAAAb,QAAA,EAAC;kBAAI;oBAAAQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eAC/DvQ,OAAA,CAACrF,UAAU;oBAACoW,IAAI,EAAC,OAAO;oBAACE,OAAO,EAAEA,CAAA,KAAM;sBACtCrO,SAAS,CAAC,qBAAqB,CAAC;sBAChCE,YAAY,CAACD,SAAS,KAAK,KAAK,GAAG,MAAM,GAAG,KAAK,CAAC;oBACpD,CAAE;oBAAA+M,QAAA,EACCjN,MAAM,KAAK,qBAAqB,GAAIE,SAAS,KAAK,KAAK,gBAAG7C,OAAA,CAAClB,cAAc;sBAAAsR,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,gBAAGvQ,OAAA,CAACpB,cAAc;sBAAAwR,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,gBAAIvQ,OAAA,CAACpB,cAAc;sBAAAwR,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9G,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACZvQ,OAAA,CAACzF,SAAS;gBAAAqV,QAAA,EAAC;cAAS;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAChCvQ,OAAA,CAACzF,SAAS;gBAAAqV,QAAA,EAAC;cAAS;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAChCvQ,OAAA,CAACzF,SAAS;gBAAAqV,QAAA,EAAC;cAAS;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAChCvQ,OAAA,CAACzF,SAAS;gBAAAqV,QAAA,EAAC;cAAU;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eACjCvQ,OAAA,CAACzF,SAAS;gBAAAqV,QAAA,EAAC;cAAS;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAChCvQ,OAAA,CAACzF,SAAS;gBAAAqV,QAAA,EAAC;cAAM;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACZvQ,OAAA,CAAC1F,SAAS;YAAAsV,QAAA,EACPgD,YAAY,CAAChJ,GAAG,CAAE9C,IAAI,iBACrB9G,OAAA,CAACtF,QAAQ;cAEPgZ,QAAQ,EAAErQ,aAAa,CAAC6E,QAAQ,CAACpB,IAAI,CAAC+C,iBAAiB,CAAE;cACzD8J,KAAK;cAAA/D,QAAA,GAEJrM,QAAQ,iBACPvD,OAAA,CAACzF,SAAS;gBAACkZ,OAAO,EAAC,UAAU;gBAAA7D,QAAA,eAC3B5P,OAAA,CAACrF,UAAU;kBACToW,IAAI,EAAC,OAAO;kBACZE,OAAO,EAAEA,CAAA,KAAM5H,mBAAmB,CAACvC,IAAI,CAAC+C,iBAAiB,CAAE;kBAC3DqG,KAAK,EAAE7M,aAAa,CAAC6E,QAAQ,CAACpB,IAAI,CAAC+C,iBAAiB,CAAC,GAAG,SAAS,GAAG,SAAU;kBAAA+F,QAAA,EAE7EvM,aAAa,CAAC6E,QAAQ,CAACpB,IAAI,CAAC+C,iBAAiB,CAAC,gBAAG7J,OAAA,CAACtC,SAAS;oBAAA0S,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,gBAAGvQ,OAAA,CAAC5D,OAAO;oBAAAgU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CACZ,eACDvQ,OAAA,CAACzF,SAAS;gBAAAqV,QAAA,eACR5P,OAAA,CAAC7G,UAAU;kBAACqX,OAAO,EAAC,OAAO;kBAACC,UAAU,EAAC,QAAQ;kBAAAb,QAAA,EAC5C9I,IAAI,CAACmC;gBAAkB;kBAAAmH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACd;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACZvQ,OAAA,CAACzF,SAAS;gBAAAqV,QAAA,eACR5P,OAAA,CAAC9E,IAAI;kBAAC6V,IAAI,EAAC,OAAO;kBAACyB,KAAK,EAAE1L,IAAI,CAAC1C,OAAQ;kBAACoM,OAAO,EAAC;gBAAU;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpD,CAAC,eACZvQ,OAAA,CAACzF,SAAS;gBAAAqV,QAAA,EAAE,IAAIjJ,IAAI,CAACG,IAAI,CAACC,mBAAmB,CAAC,CAACgH,kBAAkB,CAAC;cAAC;gBAAAqC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAChFvQ,OAAA,CAACzF,SAAS;gBAAAqV,QAAA,eACR5P,OAAA,CAAC/E,KAAK;kBAAC4U,SAAS,EAAC,KAAK;kBAACE,UAAU,EAAC,QAAQ;kBAACD,OAAO,EAAE,CAAE;kBAAAF,QAAA,gBACpD5P,OAAA,CAACV,UAAU;oBAAC6Q,QAAQ,EAAC;kBAAO;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC/BvQ,OAAA,CAAC7G,UAAU;oBAACqX,OAAO,EAAC,OAAO;oBAAAZ,QAAA,EAAE9I,IAAI,CAAC9E,SAAS,IAAI8E,IAAI,CAACzC;kBAAY;oBAAA+L,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAa,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACZvQ,OAAA,CAACzF,SAAS;gBAAAqV,QAAA,eACR5P,OAAA,CAAC7G,UAAU;kBAACqX,OAAO,EAAC,OAAO;kBAAAZ,QAAA,EACxB9I,IAAI,CAACxC,YAAY,GAChB,CAAC,MAAM;oBACL,MAAMjC,SAAS,GAAGnB,SAAS,CAACmK,IAAI,CAACuI,CAAC,IAAIA,CAAC,CAACtP,YAAY,KAAKwC,IAAI,CAACxC,YAAY,CAAC;oBAC3E,OAAOjC,SAAS,GAAG,GAAGA,SAAS,CAACwR,IAAI,MAAMxR,SAAS,CAACyR,KAAK,EAAE,GAAG,uBAAuB;kBACvF,CAAC,EAAE,CAAC,GACDhN,IAAI,CAACiN,oBAAoB,IAAI;gBAAM;kBAAA3D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAE9B;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACZvQ,OAAA,CAACzF,SAAS;gBAAAqV,QAAA,eACR5P,OAAA,CAAC7G,UAAU;kBAACqX,OAAO,EAAC,OAAO;kBAAAZ,QAAA,GAAE9I,IAAI,CAACvC,kBAAkB,EAAC,IAAE;gBAAA;kBAAA6L,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3D,CAAC,eACZvQ,OAAA,CAACzF,SAAS;gBAAAqV,QAAA,eACR5P,OAAA,CAAC9E,IAAI;kBACH6V,IAAI,EAAC,OAAO;kBACZyB,KAAK,EAAE,GAAG1L,IAAI,CAACrC,iBAAiB,KAAM;kBACtCyL,KAAK,EAAE/G,UAAU,CAACrC,IAAI,CAACrC,iBAAiB,CAAC,IAAI,GAAG,GAAG,SAAS,GAAG,SAAU;kBACzE2O,IAAI,EAAEjK,UAAU,CAACrC,IAAI,CAACrC,iBAAiB,CAAC,IAAI,GAAG,gBAAGzE,OAAA,CAACtC,SAAS;oBAAA0S,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,gBAAGvQ,OAAA,CAACpC,WAAW;oBAAAwS,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAE;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnF;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO,CAAC,eACZvQ,OAAA,CAACzF,SAAS;gBAAAqV,QAAA,eACR5P,OAAA,CAAC9E,IAAI;kBACH6V,IAAI,EAAC,OAAO;kBACZyB,KAAK,EAAE1L,IAAI,CAAC9B,gBAAgB,IAAI,UAAW;kBAC3CkL,KAAK,EAAEpJ,IAAI,CAAC9B,gBAAgB,KAAK,UAAU,GAAG,SAAS,GAAG8B,IAAI,CAAC9B,gBAAgB,KAAK,cAAc,GAAG,OAAO,GAAG;gBAAU;kBAAAoL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1H;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO,CAAC,eACZvQ,OAAA,CAACzF,SAAS;gBAAAqV,QAAA,eACR5P,OAAA,CAAC/E,KAAK;kBAAC4U,SAAS,EAAC,KAAK;kBAACC,OAAO,EAAE,GAAI;kBAAAF,QAAA,gBAClC5P,OAAA,CAAC7E,OAAO;oBAACgY,KAAK,EAAC,qBAAqB;oBAAAvD,QAAA,eAClC5P,OAAA,CAACrF,UAAU;sBACToW,IAAI,EAAC,OAAO;sBACZE,OAAO,EAAEA,CAAA,KAAM;wBACb7N,eAAe,CAAC0D,IAAI,CAAC;wBACrB5D,aAAa,CAAC,MAAM,CAAC;wBACrBF,aAAa,CAAC,IAAI,CAAC;sBACrB,CAAE;sBAAA4M,QAAA,eAEF5P,OAAA,CAAClD,QAAQ;wBAAAsT,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACF;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC,eACVvQ,OAAA,CAAC7E,OAAO;oBAACgY,KAAK,EAAC,YAAY;oBAAAvD,QAAA,eACzB5P,OAAA,CAACrF,UAAU;sBACToW,IAAI,EAAC,OAAO;sBACZE,OAAO,EAAEA,CAAA,KAAMrF,iBAAiB,CAAC9E,IAAI,CAAE;sBACvCqL,QAAQ,EAAEnO,mBAAoB;sBAAA4L,QAAA,eAE9B5P,OAAA,CAACtD,OAAO;wBAAA0T,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACD;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC,eACVvQ,OAAA,CAAC7E,OAAO;oBAACgY,KAAK,EAAC,SAAS;oBAAAvD,QAAA,eACtB5P,OAAA,CAACrF,UAAU;sBACToW,IAAI,EAAC,OAAO;sBACZb,KAAK,EAAC,OAAO;sBACbe,OAAO,EAAEA,CAAA,KAAM9D,0BAA0B,CAACrG,IAAI,CAAE;sBAChDqL,QAAQ,EAAEnO,mBAAoB;sBAAA4L,QAAA,eAE9B5P,OAAA,CAAChD,UAAU;wBAAAoT,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA,GA7FPzJ,IAAI,CAAC+C,iBAAiB;cAAAuG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA8FnB,CACX;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC,EAEhBvB,aAAa,CAACxN,sBAAsB,CAAC,GAAG,CAAC,iBACxCxB,OAAA,CAAC9G,GAAG;QAACsW,EAAE,EAAE;UAAEsB,OAAO,EAAE,MAAM;UAAEd,cAAc,EAAE,QAAQ;UAAEqD,EAAE,EAAE;QAAE,CAAE;QAAAzD,QAAA,eAC5D5P,OAAA,CAAClF,UAAU;UACTwY,KAAK,EAAEtE,aAAa,CAACxN,sBAAsB,CAAE;UAC7C+R,IAAI,EAAEhR,WAAY;UAClBkP,QAAQ,EAAEA,CAAChH,KAAK,EAAES,KAAK,KAAK1I,cAAc,CAAC0I,KAAK,CAAE;UAClDgF,KAAK,EAAC;QAAS;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CACN;IAAA,eACD,CAAC;EAEP,CAAC;;EAED;EACA,MAAMyD,0BAA0B,GAAGA,CAAA,KAAM;IACvC,IAAI/Q,UAAU,KAAK,QAAQ,IAAIA,UAAU,KAAK,MAAM,EAAE,OAAO,IAAI;IAEjE,oBACEjD,OAAA,CAACvG,MAAM;MAACkK,IAAI,EAAEZ,UAAW;MAACkR,OAAO,EAAElJ,WAAY;MAACmJ,QAAQ,EAAC,IAAI;MAAC3C,SAAS;MAAA3B,QAAA,gBACrE5P,OAAA,CAACtG,WAAW;QAAAkW,QAAA,EACT3M,UAAU,KAAK,QAAQ,GAAG,sBAAsB,GAAG;MAAyB;QAAAmN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClE,CAAC,eACdvQ,OAAA,CAACrG,aAAa;QAAAiW,QAAA,eACZ5P,OAAA,CAAC1G,IAAI;UAAC8X,SAAS;UAACtB,OAAO,EAAE,CAAE;UAACN,EAAE,EAAE;YAAE6D,EAAE,EAAE;UAAE,CAAE;UAAAzD,QAAA,gBACxC5P,OAAA,CAAC1G,IAAI;YAAC+V,IAAI;YAACgC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAA1B,QAAA,eACvB5P,OAAA,CAAC9F,YAAY;cACXia,OAAO,EAAEnT,IAAI,CAAC6F,MAAM,CAACO,IAAI,IAAI;gBAC3B;gBACA,MAAMgN,UAAU,GAAGhN,IAAI,CAAChD,OAAO,KAAKF,QAAQ,CAACE,OAAO;gBACpD,MAAMiQ,cAAc,GAAG,CAACvT,cAAc,CAAC0H,IAAI,CAAC1B,IAAI,IAAIA,IAAI,CAAC1C,OAAO,KAAKgD,IAAI,CAAChD,OAAO,CAAC;gBAClF,MAAMkQ,cAAc,GAAGjN,oBAAoB,CAACD,IAAI,CAAC;gBAEjD,OAAOgN,UAAU,IAAKC,cAAc,IAAIC,cAAe;cACzD,CAAC,CAAE;cACHC,cAAc,EAAG7F,MAAM,IAAK,GAAGA,MAAM,CAACtK,OAAO,MAAMsK,MAAM,CAAC3M,SAAS,EAAG;cACtEmJ,KAAK,EAAElK,IAAI,CAACqK,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAClH,OAAO,KAAKF,QAAQ,CAACE,OAAO,CAAC,IAAI,IAAK;cAC9DqN,QAAQ,EAAEA,CAAChH,KAAK,EAAEC,QAAQ,KAAK;gBAC7B,IAAIA,QAAQ,EAAE;kBACZS,gBAAgB,CAACT,QAAQ,CAAC;gBAC5B,CAAC,MAAM;kBACLvG,WAAW,CAACoF,IAAI,KAAK;oBAAE,GAAGA,IAAI;oBAAEnF,OAAO,EAAE,EAAE;oBAAEG,kBAAkB,EAAE;kBAAG,CAAC,CAAC,CAAC;gBACzE;cACF,CAAE;cACFiQ,WAAW,EAAGC,MAAM,iBAClBzU,OAAA,CAACnG,SAAS;gBAAA,GACJ4a,MAAM;gBACVjC,KAAK,EAAC,QAAQ;gBACdhB,WAAW,EAAC,0BAA0B;gBACtCkD,QAAQ;gBACRC,UAAU,EAAC;cAA6E;gBAAAvE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzF,CACD;cACFqE,YAAY,EAAEA,CAACC,KAAK,EAAEnG,MAAM,KAAK;gBAC/B,MAAMvE,YAAY,GAAGuE,MAAM,CAACvE,YAAY,IAAI,CAAC;gBAC7C,MAAMD,WAAW,GAAGC,YAAY,KAAK,CAAC;gBAEtC,oBACEnK,OAAA,CAAC9G,GAAG;kBAAC2Z,SAAS,EAAC,IAAI;kBAAA,GAAKgC,KAAK;kBAAAjF,QAAA,eAC3B5P,OAAA,CAAC9G,GAAG;oBAACsW,EAAE,EAAE;sBAAEmB,KAAK,EAAE;oBAAO,CAAE;oBAAAf,QAAA,eACzB5P,OAAA,CAAC/E,KAAK;sBAAC4U,SAAS,EAAC,KAAK;sBAACG,cAAc,EAAC,eAAe;sBAACD,UAAU,EAAC,QAAQ;sBAAAH,QAAA,gBACvE5P,OAAA,CAAC9G,GAAG;wBAAA0W,QAAA,gBACF5P,OAAA,CAAC7G,UAAU;0BAACqX,OAAO,EAAC,OAAO;0BAACC,UAAU,EAAC,QAAQ;0BAAAb,QAAA,EAC5ClB,MAAM,CAACtK;wBAAO;0BAAAgM,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACL,CAAC,eACbvQ,OAAA,CAAC7G,UAAU;0BAACqX,OAAO,EAAC,SAAS;0BAACN,KAAK,EAAC,gBAAgB;0BAAAN,QAAA,GACjDlB,MAAM,CAAC3M,SAAS,EAAC,KAAG,EAAC2M,MAAM,CAACvG,mBAAmB,EAAC,UAAG,EAACuG,MAAM,CAACtG,iBAAiB;wBAAA;0BAAAgI,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACnE,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACV,CAAC,eACNvQ,OAAA,CAAC/E,KAAK;wBAAC4U,SAAS,EAAC,KAAK;wBAACC,OAAO,EAAE,CAAE;wBAAAF,QAAA,gBAChC5P,OAAA,CAAC9E,IAAI;0BACH6V,IAAI,EAAC,OAAO;0BACZyB,KAAK,EAAE9D,MAAM,CAACnG,mBAAoB;0BAClC2H,KAAK,EAAExB,MAAM,CAACnG,mBAAmB,KAAK,YAAY,GAAG,SAAS,GAAG;wBAAU;0BAAA6H,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC5E,CAAC,eACFvQ,OAAA,CAAC9E,IAAI;0BACH6V,IAAI,EAAC,OAAO;0BACZyB,KAAK,EAAEtI,WAAW,GAAG,WAAW,GAAG,cAAe;0BAClDgG,KAAK,EAAEhG,WAAW,GAAG,SAAS,GAAG,SAAU;0BAC3CkJ,IAAI,EAAElJ,WAAW,gBAAGlK,OAAA,CAACtC,SAAS;4BAAA0S,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC,gBAAGvQ,OAAA,CAACpC,WAAW;4BAAAwS,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAE;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACrD,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACG,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAEV;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAEPvQ,OAAA,CAAC1G,IAAI;YAAC+V,IAAI;YAACgC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAA1B,QAAA,eACvB5P,OAAA,CAACnG,SAAS;cACR0X,SAAS;cACTiB,KAAK,EAAC,aAAa;cACnBtH,KAAK,EAAEhH,QAAQ,CAACG,YAAa;cAC7BoN,QAAQ,EAAGC,CAAC,IAAK1G,gBAAgB,CAAC,cAAc,EAAE0G,CAAC,CAACC,MAAM,CAACzG,KAAK,CAAE;cAClEwJ,QAAQ;YAAA;cAAAtE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAEPvQ,OAAA,CAAC1G,IAAI;YAAC+V,IAAI;YAACgC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAA1B,QAAA,eACvB5P,OAAA,CAAClG,WAAW;cAACyX,SAAS;cAACmD,QAAQ;cAAA9E,QAAA,gBAC7B5P,OAAA,CAACjG,UAAU;gBAAA6V,QAAA,EAAC;cAAW;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACpCvQ,OAAA,CAAChG,MAAM;gBACLkR,KAAK,EAAEhH,QAAQ,CAACI,YAAa;gBAC7BmN,QAAQ,EAAGC,CAAC,IAAK1G,gBAAgB,CAAC,cAAc,EAAE0G,CAAC,CAACC,MAAM,CAACzG,KAAK,CAAE;gBAClEsH,KAAK,EAAC,aAAa;gBAAA5C,QAAA,EAElB1O,SAAS,CAAC0I,GAAG,CAAEvH,SAAS,iBACvBrC,OAAA,CAAC/F,QAAQ;kBAA8BiR,KAAK,EAAE7I,SAAS,CAACiC,YAAa;kBAAAsL,QAAA,GAClEvN,SAAS,CAACwR,IAAI,EAAC,KAAG,EAACxR,SAAS,CAACyR,KAAK,EAAC,GAAC,EAACzR,SAAS,CAACyS,OAAO,EAAC,SAAO,EAACzS,SAAS,CAAC0S,YAAY,EAAC,GACzF;gBAAA,GAFe1S,SAAS,CAACiC,YAAY;kBAAA8L,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAE3B,CACX;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eAEPvQ,OAAA,CAAC1G,IAAI;YAAC+V,IAAI;YAACgC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAA1B,QAAA,eACvB5P,OAAA,CAACnG,SAAS;cACR0X,SAAS;cACTiB,KAAK,EAAC,0BAA0B;cAChC1F,IAAI,EAAC,QAAQ;cACb5B,KAAK,EAAEhH,QAAQ,CAACK,kBAAmB;cACnCkN,QAAQ,EAAGC,CAAC,IAAK1G,gBAAgB,CAAC,oBAAoB,EAAE0G,CAAC,CAACC,MAAM,CAACzG,KAAK,CAAE;cACxEwJ,QAAQ;YAAA;cAAAtE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAEPvQ,OAAA,CAAC1G,IAAI;YAAC+V,IAAI;YAACgC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAA1B,QAAA,eACvB5P,OAAA,CAAClG,WAAW;cAACyX,SAAS;cAAA3B,QAAA,gBACpB5P,OAAA,CAACjG,UAAU;gBAAA6V,QAAA,EAAC;cAAU;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACnCvQ,OAAA,CAAChG,MAAM;gBACLkR,KAAK,EAAEhH,QAAQ,CAACM,iBAAkB;gBAClCiN,QAAQ,EAAGC,CAAC,IAAK1G,gBAAgB,CAAC,mBAAmB,EAAE0G,CAAC,CAACC,MAAM,CAACzG,KAAK,CAAE;gBACvEsH,KAAK,EAAC,eAAY;gBAAA5C,QAAA,gBAElB5P,OAAA,CAAC/F,QAAQ;kBAACiR,KAAK,EAAC,IAAI;kBAAA0E,QAAA,EAAC;gBAAE;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,eAClCvQ,OAAA,CAAC/F,QAAQ;kBAACiR,KAAK,EAAC,KAAK;kBAAA0E,QAAA,EAAC;gBAAG;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eAEPvQ,OAAA,CAAC1G,IAAI;YAAC+V,IAAI;YAACgC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAA1B,QAAA,eACvB5P,OAAA,CAACnG,SAAS;cACR0X,SAAS;cACTiB,KAAK,EAAC,wBAAmB;cACzB1F,IAAI,EAAC,QAAQ;cACb5B,KAAK,EAAEhH,QAAQ,CAACO,iBAAkB;cAClCgN,QAAQ,EAAGC,CAAC,IAAK1G,gBAAgB,CAAC,mBAAmB,EAAE0G,CAAC,CAACC,MAAM,CAACzG,KAAK,CAAE;cACvEwJ,QAAQ;cACRC,UAAU,EAAC;YAAmC;cAAAvE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/C;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAEPvQ,OAAA,CAAC1G,IAAI;YAAC+V,IAAI;YAACgC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAA1B,QAAA,eACvB5P,OAAA,CAAClG,WAAW;cAACyX,SAAS;cAAA3B,QAAA,gBACpB5P,OAAA,CAACjG,UAAU;gBAAA6V,QAAA,EAAC;cAAU;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACnCvQ,OAAA,CAAChG,MAAM;gBACLkR,KAAK,EAAEhH,QAAQ,CAACQ,iBAAkB;gBAClC+M,QAAQ,EAAGC,CAAC,IAAK1G,gBAAgB,CAAC,mBAAmB,EAAE0G,CAAC,CAACC,MAAM,CAACzG,KAAK,CAAE;gBACvEsH,KAAK,EAAC,YAAY;gBAAA5C,QAAA,gBAElB5P,OAAA,CAAC/F,QAAQ;kBAACiR,KAAK,EAAC,IAAI;kBAAA0E,QAAA,EAAC;gBAAE;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,eAClCvQ,OAAA,CAAC/F,QAAQ;kBAACiR,KAAK,EAAC,KAAK;kBAAA0E,QAAA,EAAC;gBAAG;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eAGPvQ,OAAA,CAAC1G,IAAI;YAAC+V,IAAI;YAACgC,EAAE,EAAE,EAAG;YAAAzB,QAAA,eAChB5P,OAAA,CAAChF,OAAO;cAACwU,EAAE,EAAE;gBAAE6C,EAAE,EAAE;cAAE,CAAE;cAAAzC,QAAA,eACrB5P,OAAA,CAAC7G,UAAU;gBAACqX,OAAO,EAAC,OAAO;gBAACN,KAAK,EAAC,gBAAgB;gBAAAN,QAAA,EAAC;cAEnD;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,EAENrM,QAAQ,CAACE,OAAO,IAAI,CAAC,MAAM;YAC1B,MAAMgD,IAAI,GAAGpG,IAAI,CAACqK,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAClH,OAAO,KAAKF,QAAQ,CAACE,OAAO,CAAC;YAC3D,IAAI,CAACgD,IAAI,EAAE,OAAO,IAAI;YAEtB,MAAM+C,YAAY,GAAG/C,IAAI,CAAC+C,YAAY,IAAI,CAAC;YAC3C,MAAMD,WAAW,GAAGC,YAAY,KAAK,CAAC;YAEtC,oBACEnK,OAAA,CAAC1G,IAAI;cAAC+V,IAAI;cAACgC,EAAE,EAAE,EAAG;cAAAzB,QAAA,eAChB5P,OAAA,CAAC3G,KAAK;gBAACmW,EAAE,EAAE;kBAAEC,CAAC,EAAE,CAAC;kBAAEE,OAAO,EAAEzF,WAAW,GAAG,eAAe,GAAG;gBAAgB,CAAE;gBAAA0F,QAAA,eAC5E5P,OAAA,CAAC/E,KAAK;kBAAC4U,SAAS,EAAC,KAAK;kBAACE,UAAU,EAAC,QAAQ;kBAACD,OAAO,EAAE,CAAE;kBAAAF,QAAA,GACnD1F,WAAW,gBAAGlK,OAAA,CAACtC,SAAS;oBAACwS,KAAK,EAAC;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,gBAAGvQ,OAAA,CAACpC,WAAW;oBAACsS,KAAK,EAAC;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC9EvQ,OAAA,CAAC9G,GAAG;oBAAA0W,QAAA,gBACF5P,OAAA,CAAC7G,UAAU;sBAACqX,OAAO,EAAC,OAAO;sBAACC,UAAU,EAAC,MAAM;sBAAAb,QAAA,EAC1C1F,WAAW,GAAG,8BAA8B,GAAG;oBAAkC;sBAAAkG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxE,CAAC,eACbvQ,OAAA,CAAC7G,UAAU;sBAACqX,OAAO,EAAC,SAAS;sBAAAZ,QAAA,GAAC,SACrB,EAACzF,YAAY,KAAK,CAAC,GAAG,eAAe,GACpCA,YAAY,KAAK,CAAC,GAAG,yBAAyB,GAC9CA,YAAY,KAAK,CAAC,GAAG,uBAAuB,GAC5CA,YAAY,KAAK,CAAC,GAAG,yBAAyB,GAC9C,mBAAmB;oBAAA;sBAAAiG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjB,CAAC,EACZ,CAACrG,WAAW,iBACXlK,OAAA,CAAC7G,UAAU;sBAACqX,OAAO,EAAC,SAAS;sBAACM,OAAO,EAAC,OAAO;sBAACtB,EAAE,EAAE;wBAAE6D,EAAE,EAAE;sBAAE,CAAE;sBAAAzD,QAAA,EAAC;oBAE7D;sBAAAQ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CACb;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAEX,CAAC,EAAE,CAAC,eAGJvQ,OAAA,CAAC1G,IAAI;YAAC+V,IAAI;YAACgC,EAAE,EAAE,EAAG;YAAAzB,QAAA,eAChB5P,OAAA,CAAChF,OAAO;cAACwU,EAAE,EAAE;gBAAE6C,EAAE,EAAE;cAAE,CAAE;cAAAzC,QAAA,eACrB5P,OAAA,CAAC7G,UAAU;gBAACqX,OAAO,EAAC,OAAO;gBAACN,KAAK,EAAC,gBAAgB;gBAAAN,QAAA,EAAC;cAEnD;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAEPvQ,OAAA,CAAC1G,IAAI;YAAC+V,IAAI;YAACgC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAA1B,QAAA,eACvB5P,OAAA,CAACnG,SAAS;cACR0X,SAAS;cACTiB,KAAK,EAAC,8BAA2B;cACjC1F,IAAI,EAAC,QAAQ;cACb5B,KAAK,EAAEhH,QAAQ,CAACU,oBAAqB;cACrC6M,QAAQ,EAAGC,CAAC,IAAK1G,gBAAgB,CAAC,sBAAsB,EAAE0G,CAAC,CAACC,MAAM,CAACzG,KAAK,CAAE;cAC1EyJ,UAAU,EAAC;YAA6B;cAAAvE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAEPvQ,OAAA,CAAC1G,IAAI;YAAC+V,IAAI;YAACgC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAA1B,QAAA,eACvB5P,OAAA,CAACnG,SAAS;cACR0X,SAAS;cACTiB,KAAK,EAAC,gBAAa;cACnB1F,IAAI,EAAC,QAAQ;cACb5B,KAAK,EAAEhH,QAAQ,CAACW,OAAQ;cACxB4M,QAAQ,EAAGC,CAAC,IAAK1G,gBAAgB,CAAC,SAAS,EAAE0G,CAAC,CAACC,MAAM,CAACzG,KAAK,CAAE;cAC7DyJ,UAAU,EAAC;YAAkB;cAAAvE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAEPvQ,OAAA,CAAC1G,IAAI;YAAC+V,IAAI;YAACgC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAA1B,QAAA,eACvB5P,OAAA,CAACnG,SAAS;cACR0X,SAAS;cACTiB,KAAK,EAAC,uBAAuB;cAC7B1F,IAAI,EAAC,QAAQ;cACb5B,KAAK,EAAEhH,QAAQ,CAACY,cAAe;cAC/B2M,QAAQ,EAAGC,CAAC,IAAK1G,gBAAgB,CAAC,gBAAgB,EAAE0G,CAAC,CAACC,MAAM,CAACzG,KAAK,CAAE;cACpEyJ,UAAU,EAAC;YAAgC;cAAAvE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5C;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAEPvQ,OAAA,CAAC1G,IAAI;YAAC+V,IAAI;YAACgC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAA1B,QAAA,eACvB5P,OAAA,CAACnG,SAAS;cACR0X,SAAS;cACTiB,KAAK,EAAC,oBAAoB;cAC1B1F,IAAI,EAAC,QAAQ;cACb5B,KAAK,EAAEhH,QAAQ,CAACa,YAAa;cAC7B0M,QAAQ,EAAGC,CAAC,IAAK1G,gBAAgB,CAAC,cAAc,EAAE0G,CAAC,CAACC,MAAM,CAACzG,KAAK,CAAE;cAClEyJ,UAAU,EAAC;YAA2B;cAAAvE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAEPvQ,OAAA,CAAC1G,IAAI;YAAC+V,IAAI;YAACgC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAA1B,QAAA,eACvB5P,OAAA,CAAClG,WAAW;cAACyX,SAAS;cAAA3B,QAAA,gBACpB5P,OAAA,CAACjG,UAAU;gBAAA6V,QAAA,EAAC;cAAgB;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACzCvQ,OAAA,CAAChG,MAAM;gBACLkR,KAAK,EAAEhH,QAAQ,CAACc,gBAAiB;gBACjCyM,QAAQ,EAAGC,CAAC,IAAK1G,gBAAgB,CAAC,kBAAkB,EAAE0G,CAAC,CAACC,MAAM,CAACzG,KAAK,CAAE;gBACtEsH,KAAK,EAAC,kBAAkB;gBAAA5C,QAAA,gBAExB5P,OAAA,CAAC/F,QAAQ;kBAACiR,KAAK,EAAC,UAAU;kBAAA0E,QAAA,eACxB5P,OAAA,CAAC/E,KAAK;oBAAC4U,SAAS,EAAC,KAAK;oBAACE,UAAU,EAAC,QAAQ;oBAACD,OAAO,EAAE,CAAE;oBAAAF,QAAA,gBACpD5P,OAAA,CAACtC,SAAS;sBAACwS,KAAK,EAAC;oBAAS;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAC7BvQ,OAAA,CAAC7G,UAAU;sBAAAyW,QAAA,EAAC;oBAAQ;sBAAAQ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5B;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA,CAAC,eACXvQ,OAAA,CAAC/F,QAAQ;kBAACiR,KAAK,EAAC,cAAc;kBAAA0E,QAAA,eAC5B5P,OAAA,CAAC/E,KAAK;oBAAC4U,SAAS,EAAC,KAAK;oBAACE,UAAU,EAAC,QAAQ;oBAACD,OAAO,EAAE,CAAE;oBAAAF,QAAA,gBACpD5P,OAAA,CAACd,SAAS;sBAACgR,KAAK,EAAC;oBAAO;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAC3BvQ,OAAA,CAAC7G,UAAU;sBAAAyW,QAAA,EAAC;oBAAY;sBAAAQ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA,CAAC,eACXvQ,OAAA,CAAC/F,QAAQ;kBAACiR,KAAK,EAAC,eAAe;kBAAA0E,QAAA,eAC7B5P,OAAA,CAAC/E,KAAK;oBAAC4U,SAAS,EAAC,KAAK;oBAACE,UAAU,EAAC,QAAQ;oBAACD,OAAO,EAAE,CAAE;oBAAAF,QAAA,gBACpD5P,OAAA,CAACpC,WAAW;sBAACsS,KAAK,EAAC;oBAAS;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAC/BvQ,OAAA,CAAC7G,UAAU;sBAAAyW,QAAA,EAAC;oBAAa;sBAAAQ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eAEPvQ,OAAA,CAAC1G,IAAI;YAAC+V,IAAI;YAACgC,EAAE,EAAE,EAAG;YAAAzB,QAAA,eAChB5P,OAAA,CAACnG,SAAS;cACR0X,SAAS;cACTiB,KAAK,EAAC,MAAM;cACZwC,SAAS;cACTlH,IAAI,EAAE,CAAE;cACR5C,KAAK,EAAEhH,QAAQ,CAACS,IAAK;cACrB8M,QAAQ,EAAGC,CAAC,IAAK1G,gBAAgB,CAAC,MAAM,EAAE0G,CAAC,CAACC,MAAM,CAACzG,KAAK,CAAE;cAC1DsG,WAAW,EAAC;YAAkF;cAAApB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/F;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC,eAChBvQ,OAAA,CAACpG,aAAa;QAAAgW,QAAA,gBACZ5P,OAAA,CAAC5G,MAAM;UAAC6X,OAAO,EAAElG,WAAY;UAAA6E,QAAA,EAAC;QAAO;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAC9CvQ,OAAA,CAAC5G,MAAM;UACL6X,OAAO,EAAE7F,0BAA2B;UACpCoF,OAAO,EAAC,WAAW;UACnB2B,QAAQ,EAAEzR,OAAO,IAAI,CAACwD,QAAQ,CAACE,OAAO,IAAI,CAACF,QAAQ,CAACG,YAAY,IAAI,CAACH,QAAQ,CAACI,YAAY,IAAI,CAACJ,QAAQ,CAACO,iBAAkB;UAC1HuM,SAAS,EAAEtQ,OAAO,gBAAGV,OAAA,CAAC7F,gBAAgB;YAAC4W,IAAI,EAAE;UAAG;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAAGvQ,OAAA,CAAC5C,QAAQ;YAAAgT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAAX,QAAA,EAElE3M,UAAU,KAAK,QAAQ,GAAG,qBAAqB,GAAG;QAAiB;UAAAmN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9D,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAEb,CAAC;;EAED;EACA,MAAM0E,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAIhS,UAAU,KAAK,MAAM,IAAI,CAACE,YAAY,EAAE,OAAO,IAAI;IAEvD,oBACEnD,OAAA,CAACvG,MAAM;MAACkK,IAAI,EAAEZ,UAAW;MAACkR,OAAO,EAAElJ,WAAY;MAACmJ,QAAQ,EAAC,IAAI;MAAC3C,SAAS;MAAA3B,QAAA,gBACrE5P,OAAA,CAACtG,WAAW;QAAAkW,QAAA,GAAC,4BACe,EAACzM,YAAY,CAAC8F,kBAAkB;MAAA;QAAAmH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/C,CAAC,eACdvQ,OAAA,CAACrG,aAAa;QAAAiW,QAAA,eACZ5P,OAAA,CAAC1G,IAAI;UAAC8X,SAAS;UAACtB,OAAO,EAAE,CAAE;UAACN,EAAE,EAAE;YAAE6D,EAAE,EAAE;UAAE,CAAE;UAAAzD,QAAA,gBACxC5P,OAAA,CAAC1G,IAAI;YAAC+V,IAAI;YAACgC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAA1B,QAAA,eACvB5P,OAAA,CAACzG,IAAI;cAACiX,OAAO,EAAC,UAAU;cAAAZ,QAAA,eACtB5P,OAAA,CAACxG,WAAW;gBAAAoW,QAAA,gBACV5P,OAAA,CAAC7G,UAAU;kBAACqX,OAAO,EAAC,IAAI;kBAAC0E,YAAY;kBAAAtF,QAAA,EAAC;gBAEtC;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACbvQ,OAAA,CAAC7G,UAAU;kBAACqX,OAAO,EAAC,OAAO;kBAACN,KAAK,EAAC,gBAAgB;kBAAAN,QAAA,GAAC,WACxC,eAAA5P,OAAA;oBAAA4P,QAAA,EAASzM,YAAY,CAACiB;kBAAO;oBAAAgM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAS,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtC,CAAC,eACbvQ,OAAA,CAAC7G,UAAU;kBAACqX,OAAO,EAAC,OAAO;kBAACN,KAAK,EAAC,gBAAgB;kBAAAN,QAAA,GAAC,sBAC7B,eAAA5P,OAAA;oBAAA4P,QAAA,GAASzM,YAAY,CAACoB,kBAAkB,EAAC,IAAE;kBAAA;oBAAA6L,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9D,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEPvQ,OAAA,CAAC1G,IAAI;YAAC+V,IAAI;YAACgC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAA1B,QAAA,eACvB5P,OAAA,CAACzG,IAAI;cAACiX,OAAO,EAAC,UAAU;cAAAZ,QAAA,eACtB5P,OAAA,CAACxG,WAAW;gBAAAoW,QAAA,gBACV5P,OAAA,CAAC7G,UAAU;kBAACqX,OAAO,EAAC,IAAI;kBAAC0E,YAAY;kBAAAtF,QAAA,EAAC;gBAEtC;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACbvQ,OAAA,CAAC7G,UAAU;kBAACqX,OAAO,EAAC,OAAO;kBAACN,KAAK,EAAC,gBAAgB;kBAAAN,QAAA,GAAC,UACzC,eAAA5P,OAAA;oBAAA4P,QAAA,EAASzM,YAAY,CAAC8F;kBAAkB;oBAAAmH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAS,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChD,CAAC,eACbvQ,OAAA,CAAC7G,UAAU;kBAACqX,OAAO,EAAC,OAAO;kBAACN,KAAK,EAAC,gBAAgB;kBAAAN,QAAA,GAAC,QAC3C,eAAA5P,OAAA;oBAAA4P,QAAA,EAAS,IAAIjJ,IAAI,CAACxD,YAAY,CAAC4D,mBAAmB,CAAC,CAACgH,kBAAkB,CAAC;kBAAC;oBAAAqC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAS,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9E,CAAC,eACbvQ,OAAA,CAAC7G,UAAU;kBAACqX,OAAO,EAAC,OAAO;kBAACN,KAAK,EAAC,gBAAgB;kBAAAN,QAAA,GAAC,aACtC,eAAA5P,OAAA;oBAAA4P,QAAA,EAASzM,YAAY,CAACnB,SAAS,IAAImB,YAAY,CAACkB;kBAAY;oBAAA+L,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAS,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEPvQ,OAAA,CAAC1G,IAAI;YAAC+V,IAAI;YAACgC,EAAE,EAAE,EAAG;YAAAzB,QAAA,eAChB5P,OAAA,CAACzG,IAAI;cAACiX,OAAO,EAAC,UAAU;cAAAZ,QAAA,eACtB5P,OAAA,CAACxG,WAAW;gBAAAoW,QAAA,gBACV5P,OAAA,CAAC7G,UAAU;kBAACqX,OAAO,EAAC,IAAI;kBAAC0E,YAAY;kBAAAtF,QAAA,EAAC;gBAEtC;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACbvQ,OAAA,CAAC1G,IAAI;kBAAC8X,SAAS;kBAACtB,OAAO,EAAE,CAAE;kBAAAF,QAAA,gBACzB5P,OAAA,CAAC1G,IAAI;oBAAC+V,IAAI;oBAACgC,EAAE,EAAE,CAAE;oBAAAzB,QAAA,gBACf5P,OAAA,CAAC7G,UAAU;sBAACqX,OAAO,EAAC,OAAO;sBAACN,KAAK,EAAC,gBAAgB;sBAAAN,QAAA,EAAC;oBAEnD;sBAAAQ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACbvQ,OAAA,CAAC9E,IAAI;sBACH6V,IAAI,EAAC,OAAO;sBACZyB,KAAK,EAAErP,YAAY,CAACqB,iBAAkB;sBACtC0L,KAAK,EAAE/M,YAAY,CAACqB,iBAAiB,KAAK,IAAI,GAAG,SAAS,GAAG;oBAAQ;sBAAA4L,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC,eACPvQ,OAAA,CAAC1G,IAAI;oBAAC+V,IAAI;oBAACgC,EAAE,EAAE,CAAE;oBAAAzB,QAAA,gBACf5P,OAAA,CAAC7G,UAAU;sBAACqX,OAAO,EAAC,OAAO;sBAACN,KAAK,EAAC,gBAAgB;sBAAAN,QAAA,EAAC;oBAEnD;sBAAAQ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACbvQ,OAAA,CAAC9E,IAAI;sBACH6V,IAAI,EAAC,OAAO;sBACZyB,KAAK,EAAE,GAAGrP,YAAY,CAACsB,iBAAiB,KAAM;sBAC9CyL,KAAK,EAAE/G,UAAU,CAAChG,YAAY,CAACsB,iBAAiB,CAAC,IAAI,GAAG,GAAG,SAAS,GAAG;oBAAU;sBAAA2L,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAClF,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC,eACPvQ,OAAA,CAAC1G,IAAI;oBAAC+V,IAAI;oBAACgC,EAAE,EAAE,CAAE;oBAAAzB,QAAA,gBACf5P,OAAA,CAAC7G,UAAU;sBAACqX,OAAO,EAAC,OAAO;sBAACN,KAAK,EAAC,gBAAgB;sBAAAN,QAAA,EAAC;oBAEnD;sBAAAQ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACbvQ,OAAA,CAAC9E,IAAI;sBACH6V,IAAI,EAAC,OAAO;sBACZyB,KAAK,EAAErP,YAAY,CAACuB,iBAAkB;sBACtCwL,KAAK,EAAE/M,YAAY,CAACuB,iBAAiB,KAAK,IAAI,GAAG,SAAS,GAAG;oBAAQ;sBAAA0L,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EAENpN,YAAY,CAACwB,IAAI,iBAChB3E,OAAA,CAAC1G,IAAI;YAAC+V,IAAI;YAACgC,EAAE,EAAE,EAAG;YAAAzB,QAAA,eAChB5P,OAAA,CAACzG,IAAI;cAACiX,OAAO,EAAC,UAAU;cAAAZ,QAAA,eACtB5P,OAAA,CAACxG,WAAW;gBAAAoW,QAAA,gBACV5P,OAAA,CAAC7G,UAAU;kBAACqX,OAAO,EAAC,IAAI;kBAAC0E,YAAY;kBAAAtF,QAAA,EAAC;gBAEtC;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACbvQ,OAAA,CAAC7G,UAAU;kBAACqX,OAAO,EAAC,OAAO;kBAAAZ,QAAA,EACxBzM,YAAY,CAACwB;gBAAI;kBAAAyL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACP;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC,eAChBvQ,OAAA,CAACpG,aAAa;QAAAgW,QAAA,gBACZ5P,OAAA,CAAC5G,MAAM;UAAC6X,OAAO,EAAElG,WAAY;UAAA6E,QAAA,EAAC;QAAM;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAC7CvQ,OAAA,CAAC5G,MAAM;UACL6X,OAAO,EAAEA,CAAA,KAAMrF,iBAAiB,CAACzI,YAAY,CAAE;UAC/CqN,OAAO,EAAC,WAAW;UACnBQ,SAAS,eAAEhR,OAAA,CAACtD,OAAO;YAAA0T,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACvB4B,QAAQ,EAAEzR,OAAQ;UAAAkP,QAAA,EACnB;QAED;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAEb,CAAC;;EAED;EACA,MAAM4E,WAAW,GAAGA,CAAA,KAAM;IACxB,MAAMC,SAAS,GAAGpU,IAAI,CAACuF,MAAM;IAC7B,MAAM8O,cAAc,GAAGrU,IAAI,CAAC6F,MAAM,CAACyE,CAAC,IAAIA,CAAC,CAAC/C,mBAAmB,KAAK,YAAY,CAAC,CAAChC,MAAM;IACtF,MAAMnB,eAAe,GAAGtE,cAAc,CAACyF,MAAM;IAC7C,MAAM+O,yBAAyB,GAAGF,SAAS,GAAG,CAAC,GAAG5O,IAAI,CAACC,KAAK,CAAErB,eAAe,GAAGiQ,cAAc,GAAI,GAAG,CAAC,GAAG,CAAC;IAE1G,oBACErV,OAAA,CAAC1G,IAAI;MAAC8X,SAAS;MAACtB,OAAO,EAAE,CAAE;MAACN,EAAE,EAAE;QAAEE,EAAE,EAAE;MAAE,CAAE;MAAAE,QAAA,gBACxC5P,OAAA,CAAC1G,IAAI;QAAC+V,IAAI;QAACgC,EAAE,EAAE,EAAG;QAACkE,EAAE,EAAE,CAAE;QAACjE,EAAE,EAAE,CAAE;QAAA1B,QAAA,eAC9B5P,OAAA,CAACzG,IAAI;UAAAqW,QAAA,eACH5P,OAAA,CAACxG,WAAW;YAAAoW,QAAA,gBACV5P,OAAA,CAAC7G,UAAU;cAAC+W,KAAK,EAAC,gBAAgB;cAACgF,YAAY;cAAAtF,QAAA,EAAC;YAEhD;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbvQ,OAAA,CAAC7G,UAAU;cAACqX,OAAO,EAAC,IAAI;cAAAZ,QAAA,EACrBwF;YAAS;cAAAhF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACPvQ,OAAA,CAAC1G,IAAI;QAAC+V,IAAI;QAACgC,EAAE,EAAE,EAAG;QAACkE,EAAE,EAAE,CAAE;QAACjE,EAAE,EAAE,CAAE;QAAA1B,QAAA,eAC9B5P,OAAA,CAACzG,IAAI;UAAAqW,QAAA,eACH5P,OAAA,CAACxG,WAAW;YAAAoW,QAAA,gBACV5P,OAAA,CAAC7G,UAAU;cAAC+W,KAAK,EAAC,gBAAgB;cAACgF,YAAY;cAAAtF,QAAA,EAAC;YAEhD;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbvQ,OAAA,CAAC7G,UAAU;cAACqX,OAAO,EAAC,IAAI;cAAAZ,QAAA,EACrByF;YAAc;cAAAjF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACPvQ,OAAA,CAAC1G,IAAI;QAAC+V,IAAI;QAACgC,EAAE,EAAE,EAAG;QAACkE,EAAE,EAAE,CAAE;QAACjE,EAAE,EAAE,CAAE;QAAA1B,QAAA,eAC9B5P,OAAA,CAACzG,IAAI;UAAAqW,QAAA,eACH5P,OAAA,CAACxG,WAAW;YAAAoW,QAAA,gBACV5P,OAAA,CAAC7G,UAAU;cAAC+W,KAAK,EAAC,gBAAgB;cAACgF,YAAY;cAAAtF,QAAA,EAAC;YAEhD;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbvQ,OAAA,CAAC7G,UAAU;cAACqX,OAAO,EAAC,IAAI;cAAAZ,QAAA,EACrBxK;YAAe;cAAAgL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACPvQ,OAAA,CAAC1G,IAAI;QAAC+V,IAAI;QAACgC,EAAE,EAAE,EAAG;QAACkE,EAAE,EAAE,CAAE;QAACjE,EAAE,EAAE,CAAE;QAAA1B,QAAA,eAC9B5P,OAAA,CAACzG,IAAI;UAAAqW,QAAA,eACH5P,OAAA,CAACxG,WAAW;YAAAoW,QAAA,gBACV5P,OAAA,CAAC7G,UAAU;cAAC+W,KAAK,EAAC,gBAAgB;cAACgF,YAAY;cAAAtF,QAAA,EAAC;YAEhD;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbvQ,OAAA,CAAC7G,UAAU;cAACqX,OAAO,EAAC,IAAI;cAACN,KAAK,EAAEoF,yBAAyB,IAAI,EAAE,GAAG,cAAc,GAAG,cAAe;cAAA1F,QAAA,GAC/F0F,yBAAyB,EAAC,GAC7B;YAAA;cAAAlF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEX,CAAC;EAED,oBACEvQ,OAAA,CAAClE,SAAS;IAACoY,QAAQ,EAAC,IAAI;IAAC1E,EAAE,EAAE;MAAEgG,EAAE,EAAE;IAAE,CAAE;IAAA5F,QAAA,gBAErC5P,OAAA,CAAC9G,GAAG;MAACsW,EAAE,EAAE;QAAEE,EAAE,EAAE;MAAE,CAAE;MAAAE,QAAA,gBACjB5P,OAAA,CAAC7G,UAAU;QAACqX,OAAO,EAAC,IAAI;QAACqC,SAAS,EAAC,IAAI;QAACqC,YAAY;QAACzE,UAAU,EAAC,MAAM;QAAAb,QAAA,EAAC;MAEvE;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACbvQ,OAAA,CAAC7G,UAAU;QAACqX,OAAO,EAAC,OAAO;QAACN,KAAK,EAAC,gBAAgB;QAACV,EAAE,EAAE;UAAEE,EAAE,EAAE;QAAE,CAAE;QAAAE,QAAA,EAAC;MAElE;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAGbvQ,OAAA,CAAC3G,KAAK;QAACmW,EAAE,EAAE;UAAEC,CAAC,EAAE,CAAC;UAAEE,OAAO,EAAE,YAAY;UAAEO,KAAK,EAAE,mBAAmB;UAAER,EAAE,EAAE;QAAE,CAAE;QAAAE,QAAA,eAC5E5P,OAAA,CAAC/E,KAAK;UAAC4U,SAAS,EAAC,KAAK;UAACE,UAAU,EAAC,QAAQ;UAACD,OAAO,EAAE,CAAE;UAAAF,QAAA,gBACpD5P,OAAA,CAAChB,QAAQ;YAAAoR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACZvQ,OAAA,CAAC9G,GAAG;YAAA0W,QAAA,gBACF5P,OAAA,CAAC7G,UAAU;cAACqX,OAAO,EAAC,OAAO;cAACC,UAAU,EAAC,MAAM;cAAAb,QAAA,EAAC;YAE9C;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbvQ,OAAA,CAAC7G,UAAU;cAACqX,OAAO,EAAC,SAAS;cAAAZ,QAAA,GAAC,SAC1B,eAAA5P,OAAA;gBAAA4P,QAAA,EAAQ;cAA0B;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,8EAC3C,eAAAvQ,OAAA;gBAAA4P,QAAA,EAAQ;cAAe;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,mGAChC,eAAAvQ,OAAA;gBAAA4P,QAAA,EAAQ;cAAgC;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,kGACjD,eAAAvQ,OAAA;gBAAA4P,QAAA,EAAQ;cAAO;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,kFAC5B;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,EAGLhB,eAAe,CAAC,CAAC,EAGjB,CAAC7O,OAAO,IAAIsD,mBAAmB,kBAC9BhE,OAAA,CAAC9G,GAAG;MAACsW,EAAE,EAAE;QAAEE,EAAE,EAAE;MAAE,CAAE;MAAAE,QAAA,gBACjB5P,OAAA,CAAC3E,cAAc;QAAA+U,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,EACjBzM,QAAQ,GAAG,CAAC,iBACX9D,OAAA,CAAC7G,UAAU;QAACqX,OAAO,EAAC,SAAS;QAACN,KAAK,EAAC,gBAAgB;QAACV,EAAE,EAAE;UAAE6D,EAAE,EAAE;QAAE,CAAE;QAAAzD,QAAA,GAAC,iBACnD,EAAC9L,QAAQ,EAAC,GAC3B;MAAA;QAAAsM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CACb;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CACN,eAGDvQ,OAAA,CAAC3G,KAAK;MAACmW,EAAE,EAAE;QAAEE,EAAE,EAAE;MAAE,CAAE;MAAAE,QAAA,eACnB5P,OAAA,CAACpF,IAAI;QACHsQ,KAAK,EAAEtK,SAAU;QACjB6Q,QAAQ,EAAEjH,eAAgB;QAC1BiL,cAAc,EAAC,SAAS;QACxBC,SAAS,EAAC,SAAS;QACnBlF,OAAO,EAAC,WAAW;QAAAZ,QAAA,gBAEnB5P,OAAA,CAACnF,GAAG;UACF2X,KAAK,eACHxS,OAAA,CAAC/E,KAAK;YAAC4U,SAAS,EAAC,KAAK;YAACE,UAAU,EAAC,QAAQ;YAACD,OAAO,EAAE,CAAE;YAAAF,QAAA,gBACpD5P,OAAA,CAACR,SAAS;cAAA4Q,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACbvQ,OAAA,CAAC9G,GAAG;cAAA0W,QAAA,gBACF5P,OAAA,CAAC7G,UAAU;gBAACqX,OAAO,EAAC,OAAO;gBAACC,UAAU,EAAC,MAAM;gBAAAb,QAAA,EAAC;cAE9C;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACbvQ,OAAA,CAAC7G,UAAU;gBAACqX,OAAO,EAAC,SAAS;gBAACN,KAAK,EAAC,gBAAgB;gBAAAN,QAAA,GACjDtO,YAAY,CAACiF,MAAM,EAAC,cACvB;cAAA;gBAAA6J,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,EACLtL,UAAU,CAACI,kBAAkB,GAAG,CAAC,iBAChCrF,OAAA,CAAC5E,KAAK;cAACua,YAAY,EAAE1Q,UAAU,CAACI,kBAAmB;cAAC6K,KAAK,EAAC,SAAS;cAAAN,QAAA,eACjE5P,OAAA,CAACpC,WAAW;gBAAAwS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CACR;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI;QACR;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACFvQ,OAAA,CAACnF,GAAG;UACF2X,KAAK,eACHxS,OAAA,CAAC/E,KAAK;YAAC4U,SAAS,EAAC,KAAK;YAACE,UAAU,EAAC,QAAQ;YAACD,OAAO,EAAE,CAAE;YAAAF,QAAA,gBACpD5P,OAAA,CAACN,WAAW;cAAA0Q,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACfvQ,OAAA,CAAC9G,GAAG;cAAA0W,QAAA,gBACF5P,OAAA,CAAC7G,UAAU;gBAACqX,OAAO,EAAC,OAAO;gBAACC,UAAU,EAAC,MAAM;gBAAAb,QAAA,EAAC;cAE9C;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACbvQ,OAAA,CAAC7G,UAAU;gBAACqX,OAAO,EAAC,SAAS;gBAACN,KAAK,EAAC,gBAAgB;gBAAAN,QAAA,GACjDpO,sBAAsB,CAAC+E,MAAM,EAAC,iBACjC;cAAA;gBAAA6J,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,EACLtL,UAAU,CAACM,kBAAkB,GAAG,CAAC,iBAChCvF,OAAA,CAAC5E,KAAK;cAACua,YAAY,EAAE1Q,UAAU,CAACM,kBAAmB;cAAC2K,KAAK,EAAC,SAAS;cAAAN,QAAA,eACjE5P,OAAA,CAACtC,SAAS;gBAAA0S,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CACR;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI;QACR;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,EAGPY,sBAAsB,CAAC,CAAC,EAGxB,CAACzQ,OAAO,IAAIE,SAAS,KAAK,CAAC,IAAI+R,eAAe,CAAC,CAAC,EAChD,CAACjS,OAAO,IAAIE,SAAS,KAAK,CAAC,IAAI4S,yBAAyB,CAAC,CAAC,EAG1DQ,0BAA0B,CAAC,CAAC,EAC5BiB,gBAAgB,CAAC,CAAC,eAGnBjV,OAAA,CAACrE,QAAQ;MACPgI,IAAI,EAAEF,QAAQ,CAACE,IAAK;MACpBiS,gBAAgB,EAAE,IAAK;MACvB3B,OAAO,EAAExM,aAAc;MACvBoO,YAAY,EAAE;QAAEC,QAAQ,EAAE,QAAQ;QAAEC,UAAU,EAAE;MAAQ,CAAE;MAAAnG,QAAA,eAE1D5P,OAAA,CAAC5F,KAAK;QAAC6Z,OAAO,EAAExM,aAAc;QAAC5D,QAAQ,EAAEJ,QAAQ,CAACI,QAAS;QAAC2L,EAAE,EAAE;UAAEmB,KAAK,EAAE;QAAO,CAAE;QAAAf,QAAA,EAC/EnM,QAAQ,CAACG;MAAO;QAAAwM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACZ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eAGXvQ,OAAA,CAAChE,SAAS;MACRga,SAAS,EAAC,eAAe;MACzBxG,EAAE,EAAE;QAAEsC,QAAQ,EAAE,OAAO;QAAEmE,MAAM,EAAE,EAAE;QAAEC,KAAK,EAAE;MAAG,CAAE;MACjD9C,IAAI,eAAEpT,OAAA,CAAC9D,aAAa;QAAAkU,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAE;MAAAX,QAAA,gBAExB5P,OAAA,CAAC/D,eAAe;QACdmX,IAAI,eAAEpT,OAAA,CAAC5D,OAAO;UAAAgU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAClB4F,YAAY,EAAC,sBAAsB;QACnClF,OAAO,EAAEtG;MAAiB;QAAAyF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3B,CAAC,eACFvQ,OAAA,CAAC/D,eAAe;QACdmX,IAAI,eAAEpT,OAAA,CAAClC,UAAU;UAAAsS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACrB4F,YAAY,EAAC,eAAe;QAC5BlF,OAAO,EAAEzC;MAAgB;QAAA4B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1B,CAAC,eACFvQ,OAAA,CAAC/D,eAAe;QACdmX,IAAI,eAAEpT,OAAA,CAACtB,WAAW;UAAA0R,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACtB4F,YAAY,EAAC,eAAe;QAC5BlF,OAAO,EAAExL;MAAgB;QAAA2K,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1B,CAAC,eACFvQ,OAAA,CAAC/D,eAAe;QACdmX,IAAI,eAAEpT,OAAA,CAAC1B,UAAU;UAAA8R,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACrB4F,YAAY,EAAC,iBAAiB;QAC9BlF,OAAO,EAAEA,CAAA,KAAMhL,YAAY,CAAC,0BAA0B,EAAE,MAAM;MAAE;QAAAmK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACO,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEhB,CAAC,kCAAC;AAAC6F,GAAA,GAp+DGjW,0BAA0B;AAs+DhC,eAAeA,0BAA0B;AAAC,IAAAE,EAAA,EAAA+V,GAAA;AAAAC,YAAA,CAAAhW,EAAA;AAAAgW,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}