import React, { useState, useEffect } from 'react';
import '../../styles/reports.css';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Card,
  CardContent,

  Button,
  Chip,
  Alert,
  CircularProgress,
  Divider,

  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  TextField,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Switch,
  FormControlLabel
} from '@mui/material';
import {
  Assessment as AssessmentIcon,

  Timeline as TimelineIcon,
  List as ListIcon,
  Download as DownloadIcon,
  Visibility as VisibilityIcon,
  Refresh as RefreshIcon,

  DateRange as DateRangeIcon,
  Cable as CableIcon,
  Inventory as InventoryIcon,
  ExpandMore as ExpandMoreIcon,
  ShowChart as ShowChartIcon
} from '@mui/icons-material';
import { useParams } from 'react-router-dom';
import AdminHomeButton from '../../components/common/AdminHomeButton';
import reportService from '../../services/reportService';
import FilterableTable from '../../components/common/FilterableTable';
import EmptyState from '../../components/common/EmptyState';
import MetricCard from '../../components/common/MetricCard';


// Import dei componenti grafici
import ProgressChart from '../../components/charts/ProgressChart';
import BoqChart from '../../components/charts/BoqChart';
import TimelineChart from '../../components/charts/TimelineChart';

const ReportCaviPageNew = () => {
  const { cantiereId } = useParams();

  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const [openDialog, setOpenDialog] = useState(false);
  const [dialogType, setDialogType] = useState('');
  const [selectedReportType, setSelectedReportType] = useState('progress');
  const [formData, setFormData] = useState({
    formato: 'video',
    data_inizio: '',
    data_fine: '',
    id_bobina: ''
  });

  // New state to store all report data
  const [reportsData, setReportsData] = useState({
    progress: null,
    boq: null,
    bobinaSpecifica: null,
    posaPeriodo: null
  });

  // State per controllo visualizzazione grafici
  const [showCharts, setShowCharts] = useState(true);

  // Load all basic reports on component mount
  useEffect(() => {
    const loadAllReports = async () => {
      setLoading(true);
      try {
        // Import certificazione service
        const certificazioneService = await import('../../services/certificazioneService');

        // Create individual promises that handle their own errors
        const progressPromise = reportService.getProgressReport(cantiereId, 'video')
          .catch(err => {
            console.error('Error loading progress report:', err);
            return { content: null };
          });

        const boqPromise = reportService.getBillOfQuantities(cantiereId, 'video')
          .catch(err => {
            console.error('Error loading BOQ report:', err);
            return { content: null };
          });

        // Carica statistiche certificazioni
        const certificazioniPromise = certificazioneService.default.getCertificazioni(cantiereId)
          .catch(err => {
            console.error('Error loading certificazioni:', err);
            return [];
          });

        // Wait for all promises to resolve (they won't reject due to the catch handlers)
        const [progressData, boqData, certificazioniData] = await Promise.all([
          progressPromise,
          boqPromise,
          certificazioniPromise
        ]);

        // Aggiungi statistiche certificazioni ai dati del progress report
        if (progressData.content && certificazioniData) {
          const totaleCavi = progressData.content.totale_cavi || 0;
          const caviCertificati = certificazioniData.length;
          const percentualeCertificazione = totaleCavi > 0 ? Math.round((caviCertificati / totaleCavi) * 100) : 0;

          // Calcola certificazioni di oggi
          const oggi = new Date().toDateString();
          const certificazioniOggi = certificazioniData.filter(cert =>
            new Date(cert.data_certificazione).toDateString() === oggi
          ).length;

          progressData.content.certificazioni = {
            totale: caviCertificati,
            percentuale: percentualeCertificazione,
            oggi: certificazioniOggi,
            rimanenti: totaleCavi - caviCertificati
          };
        }

        // Set the data for each report, even if some are null
        setReportsData({
          progress: progressData.content,
          boq: boqData.content,
          bobinaSpecifica: null,
          posaPeriodo: null
        });

        // Only set error to null if we successfully loaded at least one report
        if (progressData.content || boqData.content) {
          setError(null);
        } else {
          setError('Errore nel caricamento dei report. Riprova più tardi.');
        }
      } catch (err) {
        // This catch block should rarely be hit due to the individual error handling above
        console.error('Unexpected error loading reports:', err);
        setError('Errore nel caricamento dei report. Riprova più tardi.');
      } finally {
        setLoading(false);
      }
    };

    if (cantiereId) {
      loadAllReports();
    }
  }, [cantiereId]);



  // Nuova funzione per generare report con formato specificato
  const generateReportWithFormat = async (reportType, format) => {
    try {
      setLoading(true);
      setError(null);

      let response;

      switch (reportType) {
        case 'progress':
          response = await reportService.getProgressReport(cantiereId, format);
          break;
        case 'boq':
          response = await reportService.getBillOfQuantities(cantiereId, format);
          break;

        case 'posa-periodo':
          if (!formData.data_inizio || !formData.data_fine) {
            setError('Seleziona le date di inizio e fine periodo');
            return;
          }
          response = await reportService.getPosaPerPeriodoReport(
            cantiereId,
            formData.data_inizio,
            formData.data_fine,
            format
          );
          break;
        default:
          throw new Error('Tipo di report non riconosciuto');
      }

      if (format === 'video') {
        // For special reports, update the specific report data
        if (reportType === 'bobina-specifica' || reportType === 'posa-periodo') {
          setReportsData(prev => ({
            ...prev,
            [reportType === 'bobina-specifica' ? 'bobinaSpecifica' : 'posaPeriodo']: response.content
          }));
        }
      } else {
        // Per PDF/Excel, apri il link di download
        if (response.file_url) {
          window.open(response.file_url, '_blank');
        }
      }
    } catch (err) {
      console.error('Errore nella generazione del report:', err);
      setError(err.detail || err.message || 'Errore durante la generazione del report');
    } finally {
      setLoading(false);
    }
  };



  const handleGenerateReport = async () => {
    await generateReportWithFormat(dialogType, formData.formato);
    setOpenDialog(false);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
    setError(null);
    setFormData({
      formato: 'video',
      data_inizio: '',
      data_fine: '',
      id_bobina: ''
    });
  };



  const renderProgressReport = (data) => (
    <Box>
      {/* Header con controlli migliorato */}
      <Box sx={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        mb: 3,
        p: 2,
        bgcolor: '#f8f9fa',
        borderRadius: 2,
        border: '1px solid #e0e0e0'
      }}>
        <Typography variant="h5" sx={{ fontWeight: 600, color: '#2c3e50' }}>
          📊 Report Avanzamento Lavori
        </Typography>
        <FormControlLabel
          control={
            <Switch
              checked={showCharts}
              onChange={(e) => setShowCharts(e.target.checked)}
              color="primary"
            />
          }
          label={
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <ShowChartIcon sx={{ mr: 1 }} />
              Grafici
            </Box>
          }
        />
      </Box>

      {/* Metriche Principali - Cards Moderne con MetricCard */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <MetricCard
            title="Metri Totali"
            value={data.metri_totali}
            unit="m"
            subtitle="Lunghezza complessiva del progetto"
            gradient="linear-gradient(135deg, #667eea 0%, #764ba2 100%)"
            size="medium"
          />
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <MetricCard
            title="Metri Posati"
            value={data.metri_posati}
            unit="m"
            subtitle={`${data.percentuale_avanzamento}% completato`}
            gradient="linear-gradient(135deg, #f093fb 0%, #f5576c 100%)"
            progress={data.percentuale_avanzamento}
            trend={data.percentuale_avanzamento > 50 ? 'up' : data.percentuale_avanzamento > 25 ? 'flat' : 'down'}
            trendValue={`${data.percentuale_avanzamento}%`}
            size="medium"
          />
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <MetricCard
            title="Metri Rimanenti"
            value={data.metri_da_posare}
            unit="m"
            subtitle={`${(100 - data.percentuale_avanzamento).toFixed(1)}% da completare`}
            gradient="linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)"
            size="medium"
          />
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <MetricCard
            title="Media/Giorno"
            value={data.media_giornaliera || 0}
            unit="m"
            subtitle={
              data.giorni_stimati
                ? `${data.giorni_stimati} giorni lavorativi rimasti`
                : (data.media_giornaliera > 0
                    ? 'Calcolo in corso'
                    : 'Nessuna posa recente')
            }
            gradient="linear-gradient(135deg, #fa709a 0%, #fee140 100%)"
            size="medium"
            tooltip={
              data.giorni_lavorativi_effettivi
                ? `Calcolata su ${data.giorni_lavorativi_effettivi} giorni di lavoro effettivo. Include solo i giorni in cui è stata effettuata posa.`
                : 'Media giornaliera basata sui giorni di lavoro effettivo'
            }
          />
        </Grid>
      </Grid>

      {/* Grafici */}
      {showCharts && (
        <Box sx={{ mb: 4 }}>
          <ProgressChart data={data} />
        </Box>
      )}

      {/* Dettagli Performance - Cards Informative */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} md={6}>
          <Card sx={{ height: '100%', border: '1px solid #e0e0e0' }}>
            <CardContent sx={{ p: 3 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <CableIcon sx={{ color: '#3498db', mr: 1, fontSize: 28 }} />
                <Typography variant="h6" sx={{ fontWeight: 600, color: '#2c3e50' }}>
                  Stato Cavi
                </Typography>
              </Box>
              <Grid container spacing={2}>
                <Grid item xs={6}>
                  <Box sx={{ textAlign: 'center', p: 2, bgcolor: '#f8f9fa', borderRadius: 1 }}>
                    <Typography variant="h4" sx={{ fontWeight: 700, color: '#2c3e50', mb: 1 }}>
                      {data.totale_cavi}
                    </Typography>
                    <Typography variant="body2" sx={{ color: '#666' }}>
                      Cavi Totali
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={6}>
                  <Box sx={{ textAlign: 'center', p: 2, bgcolor: '#e8f5e8', borderRadius: 1 }}>
                    <Typography variant="h4" sx={{ fontWeight: 700, color: '#27ae60', mb: 1 }}>
                      {data.cavi_posati}
                    </Typography>
                    <Typography variant="body2" sx={{ color: '#666' }}>
                      Cavi Posati ({data.percentuale_cavi}%)
                    </Typography>
                  </Box>
                </Grid>
              </Grid>
              <Box sx={{ mt: 2 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                  <Typography variant="body2">Progresso</Typography>
                  <Typography variant="body2" sx={{ fontWeight: 600 }}>
                    {data.percentuale_cavi}%
                  </Typography>
                </Box>
                <Box sx={{
                  width: '100%',
                  height: 8,
                  bgcolor: '#e0e0e0',
                  borderRadius: 4,
                  overflow: 'hidden'
                }}>
                  <Box sx={{
                    width: `${data.percentuale_cavi}%`,
                    height: '100%',
                    bgcolor: '#27ae60',
                    transition: 'width 0.3s ease'
                  }} />
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={6}>
          <Card sx={{ height: '100%', border: '1px solid #e0e0e0' }}>
            <CardContent sx={{ p: 3 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <TimelineIcon sx={{ color: '#e74c3c', mr: 1, fontSize: 28 }} />
                <Typography variant="h6" sx={{ fontWeight: 600, color: '#2c3e50' }}>
                  Timeline Progetto
                </Typography>
              </Box>
              <Box sx={{ textAlign: 'center', mb: 2 }}>
                <Typography variant="h4" sx={{ fontWeight: 700, color: '#e74c3c', mb: 1 }}>
                  {data.media_giornaliera || 0}m
                </Typography>
                <Typography variant="body2" sx={{ color: '#666', mb: 1 }}>
                  Media Giornaliera
                </Typography>
                {data.giorni_lavorativi_effettivi && (
                  <Typography variant="caption" sx={{ color: '#999', fontSize: '0.75rem' }}>
                    Basata su {data.giorni_lavorativi_effettivi} giorni di lavoro effettivo
                  </Typography>
                )}
              </Box>
              {data.giorni_stimati ? (
                <Box sx={{ textAlign: 'center', p: 2, bgcolor: '#fff3cd', borderRadius: 1 }}>
                  <Typography variant="h6" sx={{ fontWeight: 600, color: '#856404', mb: 0.5 }}>
                    {data.data_completamento}
                  </Typography>
                  <Typography variant="body2" sx={{ color: '#856404' }}>
                    Completamento previsto in {data.giorni_stimati} giorni
                  </Typography>
                </Box>
              ) : (
                <Box sx={{ textAlign: 'center', p: 2, bgcolor: '#f8f9fa', borderRadius: 1 }}>
                  <Typography variant="body2" sx={{ color: '#666' }}>
                    {data.media_giornaliera > 0 ? 'Timeline in calcolo...' : 'Necessaria attività di posa per calcolare la timeline'}
                  </Typography>
                </Box>
              )}
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Sezione Certificazioni - Nuova */}
      {data.certificazioni && (
        <Grid container spacing={3} sx={{ mb: 4 }}>
          <Grid item xs={12}>
            <Card sx={{ border: '1px solid #e0e0e0' }}>
              <CardContent sx={{ p: 3 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                  <Box sx={{
                    bgcolor: '#9c27b0',
                    borderRadius: '50%',
                    p: 1,
                    mr: 2,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}>
                    <Typography variant="h6" sx={{ color: 'white', fontWeight: 'bold' }}>
                      🔒
                    </Typography>
                  </Box>
                  <Typography variant="h6" sx={{ fontWeight: 600, color: '#2c3e50' }}>
                    Stato Certificazioni Cavi
                  </Typography>
                </Box>

                <Grid container spacing={3}>
                  <Grid item xs={12} sm={6} md={3}>
                    <Box sx={{ textAlign: 'center', p: 2, bgcolor: '#e8f5e8', borderRadius: 1 }}>
                      <Typography variant="h4" sx={{ fontWeight: 700, color: '#27ae60', mb: 1 }}>
                        {data.certificazioni.totale}
                      </Typography>
                      <Typography variant="body2" sx={{ color: '#666' }}>
                        Cavi Certificati
                      </Typography>
                    </Box>
                  </Grid>

                  <Grid item xs={12} sm={6} md={3}>
                    <Box sx={{ textAlign: 'center', p: 2, bgcolor: '#fff3cd', borderRadius: 1 }}>
                      <Typography variant="h4" sx={{ fontWeight: 700, color: '#856404', mb: 1 }}>
                        {data.certificazioni.rimanenti}
                      </Typography>
                      <Typography variant="body2" sx={{ color: '#666' }}>
                        Da Certificare
                      </Typography>
                    </Box>
                  </Grid>

                  <Grid item xs={12} sm={6} md={3}>
                    <Box sx={{ textAlign: 'center', p: 2, bgcolor: '#f0f8ff', borderRadius: 1 }}>
                      <Typography variant="h4" sx={{ fontWeight: 700, color: '#3498db', mb: 1 }}>
                        {data.certificazioni.percentuale}%
                      </Typography>
                      <Typography variant="body2" sx={{ color: '#666' }}>
                        Completamento
                      </Typography>
                    </Box>
                  </Grid>

                  <Grid item xs={12} sm={6} md={3}>
                    <Box sx={{ textAlign: 'center', p: 2, bgcolor: '#ffeaa7', borderRadius: 1 }}>
                      <Typography variant="h4" sx={{ fontWeight: 700, color: '#d63031', mb: 1 }}>
                        {data.certificazioni.oggi}
                      </Typography>
                      <Typography variant="body2" sx={{ color: '#666' }}>
                        Oggi
                      </Typography>
                    </Box>
                  </Grid>
                </Grid>

                {/* Progress bar certificazioni */}
                <Box sx={{ mt: 3 }}>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                    <Typography variant="body2">Progresso Certificazioni</Typography>
                    <Typography variant="body2" sx={{ fontWeight: 600 }}>
                      {data.certificazioni.percentuale}%
                    </Typography>
                  </Box>
                  <Box sx={{
                    width: '100%',
                    height: 8,
                    bgcolor: '#e0e0e0',
                    borderRadius: 4,
                    overflow: 'hidden'
                  }}>
                    <Box sx={{
                      width: `${data.certificazioni.percentuale}%`,
                      height: '100%',
                      bgcolor: '#9c27b0',
                      transition: 'width 0.3s ease'
                    }} />
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}

      {/* Attività Recente - Design Migliorato */}
      {data.posa_recente && data.posa_recente.length > 0 && (
        <Card sx={{ border: '1px solid #e0e0e0' }}>
          <CardContent sx={{ p: 3 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
              <DateRangeIcon sx={{ color: '#9b59b6', mr: 1, fontSize: 28 }} />
              <Typography variant="h6" sx={{ fontWeight: 600, color: '#2c3e50' }}>
                📈 Attività Recente
              </Typography>
            </Box>

            {/* Mostra solo gli ultimi 5 record in formato card per mobile-friendly */}
            <Grid container spacing={2}>
              {data.posa_recente.slice(0, 5).map((posa, index) => (
                <Grid item xs={12} sm={6} md={4} key={index}>
                  <Box sx={{
                    p: 2,
                    border: '1px solid #e0e0e0',
                    borderRadius: 2,
                    bgcolor: index === 0 ? '#f0f8ff' : '#fafafa',
                    transition: 'all 0.2s',
                    '&:hover': {
                      bgcolor: '#f5f5f5',
                      transform: 'translateY(-2px)',
                      boxShadow: '0 4px 8px rgba(0,0,0,0.1)'
                    }
                  }}>
                    <Typography variant="body2" sx={{ color: '#666', mb: 1 }}>
                      {posa.data}
                    </Typography>
                    <Typography variant="h5" sx={{ fontWeight: 700, color: '#2c3e50' }}>
                      {posa.metri}m
                    </Typography>
                    {index === 0 && (
                      <Chip
                        label="Più recente"
                        size="small"
                        sx={{
                          mt: 1,
                          bgcolor: '#3498db',
                          color: 'white',
                          fontSize: '0.7rem'
                        }}
                      />
                    )}
                  </Box>
                </Grid>
              ))}
            </Grid>

            {/* Link per vedere tutti i dati se ce ne sono di più */}
            {data.posa_recente.length > 5 && (
              <Box sx={{ mt: 3, textAlign: 'center' }}>
                <Accordion>
                  <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                    <Typography variant="body2" sx={{ color: '#3498db' }}>
                      Mostra tutti i {data.posa_recente.length} record
                    </Typography>
                  </AccordionSummary>
                  <AccordionDetails>
                    <FilterableTable
                      data={data.posa_recente.map(posa => ({
                        data: posa.data,
                        metri: `${posa.metri}m`
                      }))}
                      columns={[
                        { field: 'data', headerName: 'Data', width: 200 },
                        { field: 'metri', headerName: 'Metri Posati', width: 150, align: 'right' }
                      ]}
                      pagination={true}
                      pageSize={10}
                    />
                  </AccordionDetails>
                </Accordion>
              </Box>
            )}
          </CardContent>
        </Card>
      )}
    </Box>
  );

  const renderBoqReport = (data) => (
    <Box>
      {/* Header migliorato */}
      <Box sx={{
        display: 'flex',
        alignItems: 'center',
        mb: 3,
        p: 2,
        bgcolor: '#f8f9fa',
        borderRadius: 2,
        border: '1px solid #e0e0e0'
      }}>
        <ListIcon sx={{ color: '#8e44ad', mr: 1, fontSize: 28 }} />
        <Typography variant="h5" sx={{ fontWeight: 600, color: '#2c3e50' }}>
          📋 Bill of Quantities - Distinta Materiali
        </Typography>
      </Box>

      {/* Grafici BOQ se disponibili */}
      {showCharts && (
        <Box sx={{ mb: 4 }}>
          <BoqChart data={data} />
        </Box>
      )}


    </Box>
  );





  const renderPosaPeriodoReport = (data) => (
    <Box>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h5" sx={{ fontWeight: 600, color: 'warning.main' }}>
          Report Posa per Periodo
        </Typography>
        <FormControlLabel
          control={
            <Switch
              checked={showCharts}
              onChange={(e) => setShowCharts(e.target.checked)}
              color="primary"
            />
          }
          label={
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <ShowChartIcon sx={{ mr: 1 }} />
              Grafici
            </Box>
          }
        />
      </Box>

      {/* Statistiche Periodo - Layout Orizzontale */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} md={3}>
          <Paper sx={{ p: 3, textAlign: 'center', bgcolor: 'warning.main', color: 'white' }}>
            <Typography variant="h4" sx={{ fontWeight: 'bold', mb: 1 }}>
              {data.totale_metri_periodo}m
            </Typography>
            <Typography variant="body1">Metri Totali</Typography>
            <Typography variant="caption">{data.data_inizio} - {data.data_fine}</Typography>
          </Paper>
        </Grid>
        <Grid item xs={12} md={3}>
          <Paper sx={{ p: 3, textAlign: 'center', bgcolor: 'info.main', color: 'white' }}>
            <Typography variant="h4" sx={{ fontWeight: 'bold', mb: 1 }}>
              {data.giorni_attivi}
            </Typography>
            <Typography variant="body1">Giorni Attivi</Typography>
          </Paper>
        </Grid>
        <Grid item xs={12} md={3}>
          <Paper sx={{ p: 3, textAlign: 'center', bgcolor: 'success.main', color: 'white' }}>
            <Typography variant="h4" sx={{ fontWeight: 'bold', mb: 1 }}>
              {data.media_giornaliera}m
            </Typography>
            <Typography variant="body1">Media/Giorno</Typography>
          </Paper>
        </Grid>
        <Grid item xs={12} md={3}>
          <Paper sx={{ p: 3, textAlign: 'center', bgcolor: 'primary.main', color: 'white' }}>
            <Typography variant="h4" sx={{ fontWeight: 'bold', mb: 1 }}>
              {Math.round(data.totale_metri_periodo / data.giorni_attivi * 7)}m
            </Typography>
            <Typography variant="body1">Media/Settimana</Typography>
          </Paper>
        </Grid>
      </Grid>

      {/* Grafici */}
      {showCharts && (
        <Box sx={{ mb: 4 }}>
          <TimelineChart data={data} />
        </Box>
      )}

      {/* Posa Giornaliera */}
      <Paper sx={{ p: 3 }}>
        <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
          Dettaglio Posa Giornaliera
        </Typography>
        <FilterableTable
          data={data.posa_giornaliera || []}
          columns={[
            { field: 'data', headerName: 'Data', width: 200 },
            { field: 'metri', headerName: 'Metri Posati', width: 150, align: 'right', dataType: 'number',
              renderCell: (row) => `${row.metri}m` }
          ]}
          pageSize={10}
        />
      </Paper>
    </Box>
  );



  const renderDialog = () => (
    <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="sm" fullWidth>
      <DialogTitle>
        {dialogType === 'posa-periodo' ? 'Report Posa per Periodo' : 'Genera Report'}
      </DialogTitle>
      <DialogContent>
        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}

        <Grid container spacing={2} sx={{ mt: 1 }}>
          <Grid item xs={12}>
            <FormControl fullWidth>
              <InputLabel>Formato</InputLabel>
              <Select
                value={formData.formato}
                label="Formato"
                onChange={(e) => setFormData({ ...formData, formato: e.target.value })}
              >
                <MenuItem value="video">Visualizza a schermo</MenuItem>
                <MenuItem value="pdf">Download PDF</MenuItem>
                <MenuItem value="excel">Download Excel</MenuItem>
              </Select>
            </FormControl>
          </Grid>



          {dialogType === 'posa-periodo' && (
            <>
              <Grid item xs={6}>
                <TextField
                  fullWidth
                  type="date"
                  label="Data Inizio"
                  value={formData.data_inizio}
                  onChange={(e) => setFormData({ ...formData, data_inizio: e.target.value })}
                  InputLabelProps={{ shrink: true }}
                />
              </Grid>
              <Grid item xs={6}>
                <TextField
                  fullWidth
                  type="date"
                  label="Data Fine"
                  value={formData.data_fine}
                  onChange={(e) => setFormData({ ...formData, data_fine: e.target.value })}
                  InputLabelProps={{ shrink: true }}
                />
              </Grid>
            </>
          )}
        </Grid>
      </DialogContent>
      <DialogActions>
        <Button onClick={handleCloseDialog}>Annulla</Button>
        <Button
          onClick={handleGenerateReport}
          variant="contained"
          disabled={loading}
          startIcon={loading ? <CircularProgress size={20} /> : <VisibilityIcon />}
        >
          {loading ? 'Generazione...' : 'Genera Report'}
        </Button>
      </DialogActions>
    </Dialog>
  );

  return (
    <Box className="report-main-container report-fade-in">
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'flex-end', alignItems: 'center', mb: 2 }}>
        <AdminHomeButton />
      </Box>

      {/* Loading indicator */}
      {loading && (
        <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
          <CircularProgress />
        </Box>
      )}

      {/* Reports Navigation */}
      <Box sx={{ mt: 3 }}>
        {/* Report Navigation - Design Compatto */}
        <Box sx={{ mb: 3 }}>
          <Typography variant="h6" sx={{ fontWeight: 600, color: '#2c3e50', mb: 2, textAlign: 'center' }}>
            🎯 Seleziona il tipo di report
          </Typography>
          <Grid container spacing={2}>
            {/* Report Avanzamento */}
            <Grid item xs={6} sm={4} md={3}>
              <Card
                className={`report-card ${selectedReportType === 'progress' ? 'report-card-selected' : ''}`}
                sx={{
                  height: '140px',
                  cursor: 'pointer',
                  border: selectedReportType === 'progress' ? '2px solid #3498db' : '1px solid #e0e0e0',
                  bgcolor: selectedReportType === 'progress' ? '#f0f8ff' : 'white',
                  transition: 'all 0.2s'
                }}
                onClick={() => setSelectedReportType('progress')}
              >
                <CardContent sx={{ p: 2, textAlign: 'center', height: '100%', display: 'flex', flexDirection: 'column', justifyContent: 'center' }}>
                  <AssessmentIcon sx={{ fontSize: 32, color: '#3498db', mb: 1 }} />
                  <Typography variant="subtitle1" sx={{ fontWeight: 600, mb: 0.5, fontSize: '1.1rem' }}>
                    Avanzamento
                  </Typography>
                  <Typography variant="body2" sx={{ color: '#666', fontSize: '0.9rem' }}>
                    Panoramica lavori
                  </Typography>
                </CardContent>
              </Card>
            </Grid>

            {/* Bill of Quantities */}
            <Grid item xs={6} sm={4} md={3}>
              <Card
                sx={{
                  height: '140px',
                  cursor: 'pointer',
                  border: selectedReportType === 'boq' ? '2px solid #8e44ad' : '1px solid #e0e0e0',
                  bgcolor: selectedReportType === 'boq' ? '#f8f4ff' : 'white',
                  transition: 'all 0.2s'
                }}
                onClick={() => setSelectedReportType('boq')}
              >
                <CardContent sx={{ p: 2, textAlign: 'center', height: '100%', display: 'flex', flexDirection: 'column', justifyContent: 'center' }}>
                  <ListIcon sx={{ fontSize: 32, color: '#8e44ad', mb: 1 }} />
                  <Typography variant="subtitle1" sx={{ fontWeight: 600, mb: 0.5, fontSize: '1.1rem' }}>
                    Bill of Quantities
                  </Typography>
                  <Typography variant="body2" sx={{ color: '#666', fontSize: '0.9rem' }}>
                    Distinta materiali
                  </Typography>
                </CardContent>
              </Card>
            </Grid>



            {/* Posa per Periodo */}
            <Grid item xs={6} sm={4} md={3}>
              <Card
                sx={{
                  height: '140px',
                  cursor: 'pointer',
                  border: selectedReportType === 'posa-periodo' ? '2px solid #9b59b6' : '1px solid #e0e0e0',
                  bgcolor: selectedReportType === 'posa-periodo' ? '#f8f4ff' : 'white',
                  transition: 'all 0.2s'
                }}
                onClick={() => setSelectedReportType('posa-periodo')}
              >
                <CardContent sx={{ p: 2, textAlign: 'center', height: '100%', display: 'flex', flexDirection: 'column', justifyContent: 'center' }}>
                  <TimelineIcon sx={{ fontSize: 32, color: '#9b59b6', mb: 1 }} />
                  <Typography variant="subtitle1" sx={{ fontWeight: 600, mb: 0.5, fontSize: '1.1rem' }}>
                    Posa per Periodo
                  </Typography>
                  <Typography variant="body2" sx={{ color: '#666', fontSize: '0.9rem' }}>
                    Analisi temporale
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </Box>

        {/* Report Content */}
        <Box sx={{ minHeight: '400px' }}>
          {/* Progress Report */}
          {selectedReportType === 'progress' && (
            <Paper sx={{ p: 3 }}>
              {reportsData.progress ? (
                <Box>
                  <Box sx={{ display: 'flex', justifyContent: 'flex-end', mb: 2 }}>
                    <Button
                      startIcon={<DownloadIcon />}
                      onClick={() => generateReportWithFormat('progress', 'pdf')}
                      variant="outlined"
                      size="small"
                      color="primary"
                      sx={{ mr: 1 }}
                    >
                      PDF
                    </Button>
                    <Button
                      startIcon={<DownloadIcon />}
                      onClick={() => generateReportWithFormat('progress', 'excel')}
                      variant="outlined"
                      size="small"
                      color="success"
                    >
                      Excel
                    </Button>
                  </Box>
                  {renderProgressReport(reportsData.progress)}
                </Box>
              ) : loading ? (
                <EmptyState
                  type="loading"
                  reportType="progress"
                  title="Caricamento Report Avanzamento..."
                  description="Stiamo elaborando i dati dell'avanzamento dei lavori"
                />
              ) : (
                <EmptyState
                  type="error"
                  reportType="progress"
                  title="Errore nel caricamento"
                  description="Impossibile caricare il report di avanzamento. Verifica la connessione e riprova."
                  onRetry={() => {
                    setLoading(true);
                    reportService.getProgressReport(cantiereId, 'video')
                      .then(data => {
                        setReportsData(prev => ({
                          ...prev,
                          progress: data.content
                        }));
                      })
                      .catch(err => {
                        console.error('Error retrying progress report:', err);
                      })
                      .finally(() => {
                        setLoading(false);
                      });
                  }}
                  loading={loading}
                />
              )}
            </Paper>
          )}

          {/* Bill of Quantities */}
          {selectedReportType === 'boq' && (
            <Paper sx={{ p: 3 }}>
              {reportsData.boq ? (
                <Box>
                  <Box sx={{ display: 'flex', justifyContent: 'flex-end', mb: 2 }}>
                    <Button
                      startIcon={<DownloadIcon />}
                      onClick={() => generateReportWithFormat('boq', 'pdf')}
                      variant="outlined"
                      size="small"
                      color="primary"
                      sx={{ mr: 1 }}
                    >
                      PDF
                    </Button>
                    <Button
                      startIcon={<DownloadIcon />}
                      onClick={() => generateReportWithFormat('boq', 'excel')}
                      variant="outlined"
                      size="small"
                      color="success"
                    >
                      Excel
                    </Button>
                  </Box>
                  {renderBoqReport(reportsData.boq)}
                </Box>
              ) : loading ? (
                <EmptyState
                  type="loading"
                  reportType="boq"
                  title="Caricamento Bill of Quantities..."
                  description="Stiamo elaborando la distinta materiali"
                />
              ) : (
                <EmptyState
                  type="error"
                  reportType="boq"
                  title="Errore nel caricamento"
                  description="Impossibile caricare la distinta materiali. Verifica la connessione e riprova."
                  onRetry={() => {
                    setLoading(true);
                    reportService.getBillOfQuantities(cantiereId, 'video')
                      .then(data => {
                        setReportsData(prev => ({
                          ...prev,
                          boq: data.content
                        }));
                      })
                      .catch(err => {
                        console.error('Error retrying BOQ report:', err);
                      })
                      .finally(() => {
                        setLoading(false);
                      });
                  }}
                  loading={loading}
                />
              )}
            </Paper>
          )}



          {/* Posa per Periodo Report */}
          {selectedReportType === 'posa-periodo' && (
            <Paper sx={{ p: 3 }}>
              {reportsData.posaPeriodo ? (
                <Box>
                  <Box sx={{ display: 'flex', justifyContent: 'flex-end', mb: 2 }}>
                    <Button
                      startIcon={<DownloadIcon />}
                      onClick={() => generateReportWithFormat('posa-periodo', 'pdf')}
                      variant="outlined"
                      size="small"
                      color="primary"
                      sx={{ mr: 1 }}
                    >
                      PDF
                    </Button>
                    <Button
                      startIcon={<DownloadIcon />}
                      onClick={() => generateReportWithFormat('posa-periodo', 'excel')}
                      variant="outlined"
                      size="small"
                      color="success"
                    >
                      Excel
                    </Button>
                  </Box>
                  {renderPosaPeriodoReport(reportsData.posaPeriodo)}
                </Box>
              ) : (
                <EmptyState
                  type="action-required"
                  reportType="posa-periodo"
                  title="Seleziona un Periodo"
                  description="Scegli un intervallo di date per analizzare i trend temporali, pattern di lavoro e produttività del team."
                  actionLabel="Seleziona Periodo"
                  onAction={() => {
                    setDialogType('posa-periodo');
                    // Set default date range (last month to today)
                    const today = new Date();
                    const lastMonth = new Date();
                    lastMonth.setMonth(today.getMonth() - 1);

                    setFormData({
                      ...formData,
                      data_inizio: lastMonth.toISOString().split('T')[0],
                      data_fine: today.toISOString().split('T')[0]
                    });
                    setOpenDialog(true);
                  }}
                />
              )}
            </Paper>
          )}
        </Box>
      </Box>

      {/* Dialog per configurazione report */}
      {renderDialog()}
    </Box>
  );
};

export default ReportCaviPageNew;
