import React, { useState, useEffect, forwardRef, useImperativeHandle } from 'react';
import {
  Box,
  Typo<PERSON>,
  Button,
  Paper,
  Grid,
  Card,
  CardContent,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Autocomplete,
  CircularProgress,
  Alert,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  IconButton,
  Tabs,
  Tab,
  Pagination,
  InputAdornment,
  Divider,
  Stack,
  Chip,
  Tooltip,
  Badge,
  LinearProgress,
  Collapse,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Snackbar,
  AppBar,
  Toolbar,
  Container,
  Fab,
  SpeedDial,
  SpeedDialAction,
  SpeedDialIcon
} from '@mui/material';
import {
  Add as AddIcon,
  Search as SearchIcon,
  FilterList as FilterIcon,
  PictureAsPdf as PdfIcon,
  Download as DownloadIcon,
  Visibility as ViewIcon,
  Delete as DeleteIcon,
  Edit as EditIcon,
  Save as SaveIcon,
  Clear as ClearIcon,
  Build as BuildIcon,
  CheckCircle as CheckIcon,
  Warning as WarningIcon,
  GetApp as ExportIcon,
  Print as PrintIcon,
  Email as EmailIcon,
  CloudUpload as UploadIcon,
  Assessment as ReportIcon,
  Settings as SettingsIcon,
  Refresh as RefreshIcon,
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon,
  Info as InfoIcon,
  Error as ErrorIcon,
  Schedule as ScheduleIcon,
  Person as PersonIcon,
  Cable as CableIcon,
  Science as ScienceIcon,
  Block as BlockIcon
} from '@mui/icons-material';

import certificazioneService from '../../services/certificazioneService';
import caviService from '../../services/caviService';

const CertificazioneCaviImproved = forwardRef(({ cantiereId, onSuccess, onError }, ref) => {
  // Stati principali
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState(0);
  const [certificazioni, setCertificazioni] = useState([]);
  const [cavi, setCavi] = useState([]);
  const [strumenti, setStrumenti] = useState([]);

  // Stati per ricerca e filtri avanzati
  const [searchTerm, setSearchTerm] = useState('');
  const [filteredCavi, setFilteredCavi] = useState([]);
  const [filteredCertificazioni, setFilteredCertificazioni] = useState([]);
  const [advancedFiltersOpen, setAdvancedFiltersOpen] = useState(false);
  const [filters, setFilters] = useState({
    stato: '',
    tipologia: '',
    operatore: '',
    dataInizio: '',
    dataFine: '',
    valoreIsolamento: '',
    risultatoTest: '',
    strumento: '',
    certificazione: ''
  });

  // Stati per paginazione e ordinamento
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [sortBy, setSortBy] = useState('data_certificazione');
  const [sortOrder, setSortOrder] = useState('desc');

  // Stati per dialogs e modali
  const [openDialog, setOpenDialog] = useState(false);
  const [dialogType, setDialogType] = useState('');
  const [selectedItem, setSelectedItem] = useState(null);
  const [bulkSelection, setBulkSelection] = useState([]);
  const [bulkMode, setBulkMode] = useState(false);

  // Stati per notifiche e feedback
  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'info' });
  const [progress, setProgress] = useState(0);
  const [operationInProgress, setOperationInProgress] = useState(false);

  // Stati per form certificazione avanzato
  const [formData, setFormData] = useState({
    id_cavo: '',
    id_operatore: '',
    id_strumento: '',
    lunghezza_misurata: '',
    valore_continuita: 'OK',
    valore_isolamento: '',
    valore_resistenza: 'OK',
    note: '',
    temperatura_ambiente: '',
    umidita: '',
    tensione_prova: '',
    durata_prova: '',
    risultato_finale: 'CONFORME'
  });

  // Stati per statistiche e dashboard
  const [statistics, setStatistics] = useState({
    totaleCavi: 0,
    caviCertificati: 0,
    caviNonCertificati: 0,
    percentualeCompletamento: 0,
    certificazioniOggi: 0,
    certificazioniSettimana: 0
  });

  // Carica dati iniziali
  useEffect(() => {
    loadInitialData();
  }, [cantiereId]);

  // Filtra cavi in base alla ricerca
  useEffect(() => {
    filterCavi();
  }, [cavi, searchTerm, filters, sortBy, sortOrder]);

  // Filtra certificazioni
  useEffect(() => {
    filterCertificazioni();
  }, [certificazioni, searchTerm, filters, sortBy, sortOrder]);

  // Ricalcola statistiche quando cambiano i dati
  useEffect(() => {
    calculateStatistics();
  }, [cavi, certificazioni]);

  const loadInitialData = async () => {
    try {
      setLoading(true);
      setProgress(0);

      // Carica dati in sequenza con progress
      setProgress(25);
      await loadCavi();

      setProgress(50);
      await loadCertificazioni();

      setProgress(75);
      await loadStrumenti();

      setProgress(100);
      calculateStatistics();

    } catch (error) {
      showSnackbar('Errore nel caricamento dei dati iniziali', 'error');
      onError('Errore nel caricamento dei dati iniziali');
    } finally {
      setLoading(false);
      setProgress(0);
    }
  };

  const loadCertificazioni = async () => {
    try {
      const data = await certificazioneService.getCertificazioni(cantiereId);
      setCertificazioni(data);
      return data;
    } catch (error) {
      console.error('Errore nel caricamento delle certificazioni:', error);
      throw error;
    }
  };

  const loadCavi = async () => {
    try {
      const data = await caviService.getCavi(cantiereId);
      setCavi(data);
      return data;
    } catch (error) {
      console.error('Errore nel caricamento dei cavi:', error);
      throw error;
    }
  };

  const loadStrumenti = async () => {
    try {
      const data = await certificazioneService.getStrumenti(cantiereId);
      setStrumenti(data);
      return data;
    } catch (error) {
      console.error('Errore nel caricamento degli strumenti:', error);
      throw error;
    }
  };

  // Calcola statistiche avanzate
  const calculateStatistics = () => {
    const totaleCavi = cavi.length;
    const caviCertificati = certificazioni.length;
    const caviNonCertificati = totaleCavi - caviCertificati;
    const percentualeCompletamento = totaleCavi > 0 ? Math.round((caviCertificati / totaleCavi) * 100) : 0;

    // Calcola certificazioni di oggi
    const oggi = new Date().toDateString();
    const certificazioniOggi = certificazioni.filter(cert =>
      new Date(cert.data_certificazione).toDateString() === oggi
    ).length;

    // Calcola certificazioni della settimana
    const unaSettimanaFa = new Date();
    unaSettimanaFa.setDate(unaSettimanaFa.getDate() - 7);
    const certificazioniSettimana = certificazioni.filter(cert =>
      new Date(cert.data_certificazione) >= unaSettimanaFa
    ).length;

    // Calcola cavi certificabili (solo posati/installati)
    const caviCertificabili = cavi.filter(cavo => puoEssereCertificato(cavo)).length;
    const caviNonCertificabili = totaleCavi - caviCertificabili;

    // Calcola cavi collegati completamente
    const caviCollegati = cavi.filter(cavo => isCavoCollegato(cavo)).length;

    setStatistics({
      totaleCavi,
      caviCertificati,
      caviNonCertificati,
      caviCertificabili,
      caviNonCertificabili,
      caviCollegati,
      percentualeCompletamento,
      certificazioniOggi,
      certificazioniSettimana
    });
  };

  // Gestione snackbar
  const showSnackbar = (message, severity = 'info') => {
    setSnackbar({ open: true, message, severity });
  };

  const closeSnackbar = () => {
    setSnackbar({ ...snackbar, open: false });
  };

  const filterCavi = () => {
    let filtered = cavi;

    // Filtro per ricerca testuale avanzata
    if (searchTerm) {
      const searchLower = searchTerm.toLowerCase();
      filtered = filtered.filter(cavo =>
        cavo.id_cavo.toLowerCase().includes(searchLower) ||
        cavo.tipologia?.toLowerCase().includes(searchLower) ||
        cavo.ubicazione_partenza?.toLowerCase().includes(searchLower) ||
        cavo.ubicazione_arrivo?.toLowerCase().includes(searchLower) ||
        cavo.sezione?.toLowerCase().includes(searchLower) ||
        cavo.utility?.toLowerCase().includes(searchLower)
      );
    }

    // Filtri specifici avanzati
    if (filters.stato) {
      filtered = filtered.filter(cavo => cavo.stato_installazione === filters.stato);
    }
    if (filters.tipologia) {
      filtered = filtered.filter(cavo => cavo.tipologia === filters.tipologia);
    }

    // Filtro per stato certificazione
    if (filters.certificazione) {
      if (filters.certificazione === 'CERTIFICATO') {
        filtered = filtered.filter(cavo =>
          certificazioni.some(cert => cert.id_cavo === cavo.id_cavo)
        );
      } else if (filters.certificazione === 'NON_CERTIFICATO') {
        filtered = filtered.filter(cavo =>
          !certificazioni.some(cert => cert.id_cavo === cavo.id_cavo)
        );
      }
    }

    // Ordinamento
    filtered.sort((a, b) => {
      let aValue = a[sortBy];
      let bValue = b[sortBy];

      if (typeof aValue === 'string') {
        aValue = aValue.toLowerCase();
        bValue = bValue.toLowerCase();
      }

      if (sortOrder === 'asc') {
        return aValue > bValue ? 1 : -1;
      } else {
        return aValue < bValue ? 1 : -1;
      }
    });

    setFilteredCavi(filtered);
  };

  const filterCertificazioni = () => {
    let filtered = certificazioni;

    // Ricerca testuale avanzata
    if (searchTerm) {
      const searchLower = searchTerm.toLowerCase();
      filtered = filtered.filter(cert =>
        cert.id_cavo.toLowerCase().includes(searchLower) ||
        cert.operatore?.toLowerCase().includes(searchLower) ||
        cert.numero_certificato?.toLowerCase().includes(searchLower) ||
        cert.note?.toLowerCase().includes(searchLower)
      );
    }

    // Filtri avanzati
    if (filters.operatore) {
      filtered = filtered.filter(cert => cert.operatore === filters.operatore);
    }
    if (filters.strumento) {
      filtered = filtered.filter(cert => cert.strumento === filters.strumento);
    }
    if (filters.risultatoTest) {
      filtered = filtered.filter(cert => cert.risultato_finale === filters.risultatoTest);
    }
    if (filters.dataInizio) {
      filtered = filtered.filter(cert =>
        new Date(cert.data_certificazione) >= new Date(filters.dataInizio)
      );
    }
    if (filters.dataFine) {
      filtered = filtered.filter(cert =>
        new Date(cert.data_certificazione) <= new Date(filters.dataFine)
      );
    }
    if (filters.valoreIsolamento) {
      const valore = parseFloat(filters.valoreIsolamento);
      filtered = filtered.filter(cert =>
        parseFloat(cert.valore_isolamento) >= valore
      );
    }

    // Ordinamento
    filtered.sort((a, b) => {
      let aValue = a[sortBy];
      let bValue = b[sortBy];

      if (sortBy === 'data_certificazione') {
        aValue = new Date(aValue);
        bValue = new Date(bValue);
      } else if (typeof aValue === 'string') {
        aValue = aValue.toLowerCase();
        bValue = bValue.toLowerCase();
      }

      if (sortOrder === 'asc') {
        return aValue > bValue ? 1 : -1;
      } else {
        return aValue < bValue ? 1 : -1;
      }
    });

    setFilteredCertificazioni(filtered);
  };

  // Gestione selezione multipla - SOLO per certificazioni
  const toggleBulkMode = () => {
    if (activeTab !== 1) {
      showSnackbar('La selezione multipla è disponibile solo per le certificazioni', 'warning');
      return;
    }
    setBulkMode(!bulkMode);
    setBulkSelection([]);
    showSnackbar(
      !bulkMode
        ? 'Modalità selezione attivata - Clicca sulle certificazioni per selezionarle'
        : 'Modalità selezione disattivata',
      'info'
    );
  };

  const toggleItemSelection = (itemId) => {
    setBulkSelection(prev => {
      const newSelection = prev.includes(itemId)
        ? prev.filter(id => id !== itemId)
        : [...prev, itemId];

      showSnackbar(
        `${newSelection.length} certificazioni selezionate`,
        'info'
      );
      return newSelection;
    });
  };

  const selectAllItems = () => {
    if (activeTab !== 1) return;

    const allIds = filteredCertificazioni.map(cert => cert.id_certificazione);
    setBulkSelection(allIds);
    showSnackbar(`Tutte le ${allIds.length} certificazioni selezionate`, 'success');
  };

  const clearSelection = () => {
    setBulkSelection([]);
    showSnackbar('Selezione cancellata', 'info');
  };

  // Funzione per verificare se un cavo è certificato
  const isCavoCertificato = (idCavo) => {
    return certificazioni.some(cert => cert.id_cavo === idCavo);
  };

  // Funzione per verificare se un cavo può essere certificato
  const puoEssereCertificato = (cavo) => {
    // Verifica che il cavo sia installato/posato
    const isInstallato = cavo.stato_installazione === 'Installato' ||
                        cavo.stato_installazione === 'INSTALLATO' ||
                        cavo.stato_installazione === 'POSATO';

    // Per la certificazione basta che sia posato
    // Il collegamento può essere gestito durante la certificazione
    return isInstallato;
  };

  // Funzione per verificare se un cavo è completamente collegato
  const isCavoCollegato = (cavo) => {
    const isCollegato = cavo.collegamenti === 3;
    const hasResponsabili = cavo.responsabile_partenza && cavo.responsabile_arrivo;
    return isCollegato && hasResponsabili;
  };

  // Funzione per ottenere il messaggio di errore per cavi non certificabili
  const getMessaggioErroreCertificazione = (cavo) => {
    const isInstallato = cavo.stato_installazione === 'Installato' ||
                        cavo.stato_installazione === 'INSTALLATO' ||
                        cavo.stato_installazione === 'POSATO';

    if (!isInstallato) {
      return 'Il cavo deve essere posato/installato prima di poter essere certificato';
    }

    return 'Cavo non certificabile per motivi sconosciuti';
  };

  // Gestione tabs
  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
    setCurrentPage(1);
    setSearchTerm('');
    setFilters({ stato: '', tipologia: '', operatore: '' });
  };

  // Gestione dialogs
  const openCreateDialog = (cavoPreselezionato = null) => {
    setDialogType('create');
    setSelectedItem(null);

    // Se viene passato un cavo, precompila il form
    if (cavoPreselezionato) {
      setFormData({
        id_cavo: cavoPreselezionato.id_cavo,
        id_operatore: '',
        id_strumento: '',
        lunghezza_misurata: cavoPreselezionato.metratura_reale || cavoPreselezionato.metri_teorici || '',
        valore_continuita: 'OK',
        valore_isolamento: '',
        valore_resistenza: 'OK',
        note: '',
        temperatura_ambiente: '',
        umidita: '',
        tensione_prova: '',
        durata_prova: '',
        risultato_finale: 'CONFORME'
      });
      showSnackbar(`Cavo ${cavoPreselezionato.id_cavo} selezionato automaticamente`, 'success');
    } else {
      // Reset form per nuova certificazione generica
      setFormData({
        id_cavo: '',
        id_operatore: '',
        id_strumento: '',
        lunghezza_misurata: '',
        valore_continuita: 'OK',
        valore_isolamento: '',
        valore_resistenza: 'OK',
        note: '',
        temperatura_ambiente: '',
        umidita: '',
        tensione_prova: '',
        durata_prova: '',
        risultato_finale: 'CONFORME'
      });
    }

    setOpenDialog(true);
  };

  const closeDialog = () => {
    setOpenDialog(false);
    setSelectedItem(null);
    setDialogType('');
  };

  // Gestione form
  const handleFormChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleCavoSelect = (cavo) => {
    setFormData(prev => ({
      ...prev,
      id_cavo: cavo.id_cavo,
      lunghezza_misurata: cavo.metratura_reale || cavo.metri_teorici || ''
    }));
  };

  // Operazioni CRUD avanzate
  const handleCreateCertificazione = async () => {
    try {
      if (!formData.id_cavo || !formData.id_operatore || !formData.id_strumento || !formData.valore_isolamento) {
        showSnackbar('Compila tutti i campi obbligatori', 'warning');
        return;
      }

      // Verifica che il cavo possa essere certificato
      const cavo = cavi.find(c => c.id_cavo === formData.id_cavo);
      if (!cavo) {
        showSnackbar('Cavo non trovato', 'error');
        return;
      }

      if (!puoEssereCertificato(cavo)) {
        const messaggio = getMessaggioErroreCertificazione(cavo);
        showSnackbar(`Impossibile certificare il cavo: ${messaggio}`, 'error');
        return;
      }

      // Verifica che il cavo non sia già certificato
      if (isCavoCertificato(formData.id_cavo)) {
        showSnackbar('Il cavo è già stato certificato', 'warning');
        return;
      }

      // Verifica se il cavo è collegato, altrimenti chiedi conferma
      if (!isCavoCollegato(cavo)) {
        const conferma = window.confirm(
          `ATTENZIONE: Il cavo ${cavo.id_cavo} non risulta completamente collegato.\n\n` +
          `Stato collegamenti: ${cavo.collegamenti === 0 ? 'Non collegato' :
                                cavo.collegamenti === 1 ? 'Solo partenza collegata' :
                                cavo.collegamenti === 2 ? 'Solo arrivo collegato' :
                                'Stato sconosciuto'}\n\n` +
          `Vuoi procedere comunque con la certificazione?\n` +
          `(Ricorda di completare i collegamenti prima della messa in servizio)`
        );

        if (!conferma) {
          return;
        }
      }

      setOperationInProgress(true);
      await certificazioneService.createCertificazione(cantiereId, formData);
      showSnackbar('Certificazione creata con successo', 'success');
      closeDialog();
      await loadCertificazioni();
      calculateStatistics();
    } catch (error) {
      showSnackbar('Errore nella creazione della certificazione: ' + (error.message || 'Errore sconosciuto'), 'error');
    } finally {
      setOperationInProgress(false);
    }
  };

  const handleGeneratePdf = async (certificazione) => {
    try {
      setOperationInProgress(true);
      showSnackbar('Generazione PDF in corso...', 'info');

      const response = await certificazioneService.generatePdf(cantiereId, certificazione.id_certificazione);

      if (response.file_url) {
        // Apri il PDF in una nuova finestra
        const newWindow = window.open(response.file_url, '_blank');
        if (newWindow) {
          showSnackbar('PDF generato e aperto in una nuova finestra', 'success');
        } else {
          // Se il popup è bloccato, offri il download diretto
          const link = document.createElement('a');
          link.href = response.file_url;
          link.download = `certificazione_${certificazione.numero_certificato}.pdf`;
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
          showSnackbar('PDF scaricato nella cartella Download', 'success');
        }
      } else if (response.pdf_content) {
        // Se il PDF viene restituito come contenuto base64
        const blob = new Blob([atob(response.pdf_content)], { type: 'application/pdf' });
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `certificazione_${certificazione.numero_certificato}.pdf`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);
        showSnackbar('PDF scaricato con successo', 'success');
      } else {
        showSnackbar('Errore: Formato PDF non riconosciuto', 'error');
      }
    } catch (error) {
      console.error('Errore generazione PDF:', error);
      showSnackbar('Errore nella generazione del PDF: ' + (error.message || 'Errore sconosciuto'), 'error');
    } finally {
      setOperationInProgress(false);
    }
  };

  const handleDeleteCertificazione = async (certificazione) => {
    if (window.confirm(`Sei sicuro di voler eliminare la certificazione ${certificazione.numero_certificato}?`)) {
      try {
        setOperationInProgress(true);
        await certificazioneService.deleteCertificazione(cantiereId, certificazione.id_certificazione);
        showSnackbar('Certificazione eliminata con successo', 'success');
        await loadCertificazioni();
        calculateStatistics();
      } catch (error) {
        showSnackbar('Errore nell\'eliminazione della certificazione: ' + (error.message || 'Errore sconosciuto'), 'error');
      } finally {
        setOperationInProgress(false);
      }
    }
  };

  // Operazioni bulk
  const handleBulkDelete = async () => {
    if (bulkSelection.length === 0) {
      showSnackbar('Seleziona almeno un elemento', 'warning');
      return;
    }

    if (window.confirm(`Sei sicuro di voler eliminare ${bulkSelection.length} certificazioni?`)) {
      try {
        setOperationInProgress(true);
        for (const id of bulkSelection) {
          await certificazioneService.deleteCertificazione(cantiereId, id);
        }
        showSnackbar(`${bulkSelection.length} certificazioni eliminate con successo`, 'success');
        setBulkSelection([]);
        await loadCertificazioni();
        calculateStatistics();
      } catch (error) {
        showSnackbar('Errore nell\'eliminazione delle certificazioni', 'error');
      } finally {
        setOperationInProgress(false);
      }
    }
  };

  const handleBulkExport = async () => {
    if (bulkSelection.length === 0) {
      showSnackbar('Seleziona almeno un elemento', 'warning');
      return;
    }

    try {
      setOperationInProgress(true);
      // Implementa export bulk
      const selectedCerts = certificazioni.filter(cert =>
        bulkSelection.includes(cert.id_certificazione)
      );

      // Crea CSV
      const csvContent = generateCSV(selectedCerts);
      downloadCSV(csvContent, `certificazioni_${new Date().toISOString().split('T')[0]}.csv`);

      showSnackbar(`${bulkSelection.length} certificazioni esportate`, 'success');
    } catch (error) {
      showSnackbar('Errore nell\'esportazione', 'error');
    } finally {
      setOperationInProgress(false);
    }
  };

  // Funzioni di export
  const generateCSV = (data) => {
    const headers = ['ID Cavo', 'Numero Certificato', 'Data', 'Operatore', 'Strumento', 'Lunghezza', 'Isolamento', 'Risultato'];
    const rows = data.map(cert => [
      cert.id_cavo,
      cert.numero_certificato,
      new Date(cert.data_certificazione).toLocaleDateString(),
      cert.operatore,
      cert.strumento,
      cert.lunghezza_misurata,
      cert.valore_isolamento,
      cert.risultato_finale
    ]);

    return [headers, ...rows].map(row => row.join(',')).join('\n');
  };

  const downloadCSV = (content, filename) => {
    const blob = new Blob([content], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    if (link.download !== undefined) {
      const url = URL.createObjectURL(blob);
      link.setAttribute('href', url);
      link.setAttribute('download', filename);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  };

  const handleExportAll = () => {
    const csvContent = generateCSV(filteredCertificazioni);
    downloadCSV(csvContent, `tutte_certificazioni_${new Date().toISOString().split('T')[0]}.csv`);
    showSnackbar('Esportazione completata', 'success');
  };

  // Espone metodi tramite ref
  useImperativeHandle(ref, () => ({
    handleOptionSelect: (option) => {
      if (option === 'creaCertificazione') {
        openCreateDialog();
      } else if (option === 'visualizzaCertificazioni') {
        setActiveTab(1);
      }
    }
  }));

  // Calcola elementi per paginazione
  const getCurrentPageItems = (items) => {
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    return items.slice(startIndex, endIndex);
  };

  const getTotalPages = (items) => Math.ceil(items.length / itemsPerPage);

  // Ottieni opzioni uniche per filtri
  const getUniqueValues = (array, field) => {
    return [...new Set(array.map(item => item[field]).filter(Boolean))];
  };

  // Dashboard minimal con statistiche essenziali
  const renderDashboard = () => (
    <Paper sx={{ p: 2, mb: 3, bgcolor: 'grey.50' }}>
      <Stack direction="row" spacing={4} alignItems="center" justifyContent="space-between" flexWrap="wrap">
        {/* Statistiche essenziali in formato compatto */}
        <Stack direction="row" alignItems="center" spacing={1}>
          <CableIcon color="primary" fontSize="small" />
          <Box>
            <Typography variant="h6" fontWeight="bold" sx={{ lineHeight: 1 }}>
              {statistics.totaleCavi}
            </Typography>
            <Typography variant="caption" color="text.secondary">
              Totale
            </Typography>
          </Box>
        </Stack>

        <Stack direction="row" alignItems="center" spacing={1}>
          <CheckIcon color="success" fontSize="small" />
          <Box>
            <Typography variant="h6" fontWeight="bold" sx={{ lineHeight: 1 }}>
              {statistics.caviCertificati}
            </Typography>
            <Typography variant="caption" color="text.secondary">
              Certificati
            </Typography>
          </Box>
        </Stack>

        <Stack direction="row" alignItems="center" spacing={1}>
          <BuildIcon color="info" fontSize="small" />
          <Box>
            <Typography variant="h6" fontWeight="bold" sx={{ lineHeight: 1 }}>
              {statistics.caviCertificabili}
            </Typography>
            <Typography variant="caption" color="text.secondary">
              Pronti
            </Typography>
          </Box>
        </Stack>

        <Stack direction="row" alignItems="center" spacing={1}>
          <Box sx={{
            width: 32,
            height: 32,
            borderRadius: '50%',
            bgcolor: statistics.percentualeCompletamento >= 80 ? 'success.main' :
                     statistics.percentualeCompletamento >= 50 ? 'warning.main' : 'error.main',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center'
          }}>
            <Typography variant="caption" fontWeight="bold" color="white">
              {statistics.percentualeCompletamento}%
            </Typography>
          </Box>
          <Box>
            <Typography variant="body2" fontWeight="medium" sx={{ lineHeight: 1 }}>
              Completamento
            </Typography>
            <Typography variant="caption" color="text.secondary">
              {statistics.certificazioniOggi} oggi
            </Typography>
          </Box>
        </Stack>

        {/* Link al report dettagliato */}
        <Button
          variant="outlined"
          size="small"
          startIcon={<ReportIcon />}
          onClick={() => window.open(`/cantieri/${cantiereId}/report-avanzamento`, '_blank')}
          sx={{ ml: 'auto' }}
        >
          Report Dettagliato
        </Button>
      </Stack>
    </Paper>
  );

  // Componente barra di ricerca avanzata
  const renderSearchAndFilters = () => (
    <Paper sx={{ p: 2, mb: 3 }}>
      <Grid container spacing={2} alignItems="center">
        <Grid item xs={12} md={4}>
          <TextField
            fullWidth
            placeholder="Cerca cavi, certificazioni, operatori..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <SearchIcon />
                </InputAdornment>
              ),
              endAdornment: searchTerm && (
                <InputAdornment position="end">
                  <IconButton onClick={() => setSearchTerm('')} size="small">
                    <ClearIcon />
                  </IconButton>
                </InputAdornment>
              )
            }}
          />
        </Grid>

        <Grid item xs={12} md={2}>
          <Button
            fullWidth
            variant="outlined"
            startIcon={<FilterIcon />}
            onClick={() => setAdvancedFiltersOpen(!advancedFiltersOpen)}
            color={Object.values(filters).some(f => f) ? 'primary' : 'inherit'}
          >
            Filtri {Object.values(filters).filter(f => f).length > 0 && `(${Object.values(filters).filter(f => f).length})`}
          </Button>
        </Grid>

        <Grid item xs={12} md={2}>
          <Button
            fullWidth
            variant="outlined"
            startIcon={bulkMode ? <ClearIcon /> : <CheckIcon />}
            onClick={toggleBulkMode}
            color={bulkMode ? 'secondary' : 'inherit'}
            disabled={activeTab === 0}
          >
            {bulkMode ? 'Esci Selezione' : 'Selezione Multipla'}
          </Button>
        </Grid>

        <Grid item xs={12} md={2}>
          <Button
            fullWidth
            variant="outlined"
            startIcon={<ExportIcon />}
            onClick={handleExportAll}
            disabled={activeTab === 0 || filteredCertificazioni.length === 0}
          >
            {activeTab === 0 ? 'Export (solo certificazioni)' : 'Esporta Certificazioni'}
          </Button>
        </Grid>

        <Grid item xs={12} md={2}>
          <Button
            fullWidth
            variant="contained"
            startIcon={<AddIcon />}
            onClick={openCreateDialog}
          >
            Nuova Certificazione
          </Button>
        </Grid>
      </Grid>

      {/* Filtri avanzati - Diversi per ogni tab */}
      <Collapse in={advancedFiltersOpen}>
        <Divider sx={{ my: 2 }} />
        <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
          {activeTab === 0 ? 'Filtri per Cavi' : 'Filtri per Certificazioni'}
        </Typography>

        <Grid container spacing={2}>
          {/* Filtri per tab Cavi */}
          {activeTab === 0 && (
            <>
              <Grid item xs={12} md={3}>
                <FormControl fullWidth size="small">
                  <InputLabel>Stato Installazione</InputLabel>
                  <Select
                    value={filters.stato}
                    onChange={(e) => setFilters({...filters, stato: e.target.value})}
                  >
                    <MenuItem value="">Tutti</MenuItem>
                    <MenuItem value="INSTALLATO">Installato</MenuItem>
                    <MenuItem value="NON_INSTALLATO">Non Installato</MenuItem>
                    <MenuItem value="IN_CORSO">In Corso</MenuItem>
                  </Select>
                </FormControl>
              </Grid>

              <Grid item xs={12} md={3}>
                <FormControl fullWidth size="small">
                  <InputLabel>Tipologia</InputLabel>
                  <Select
                    value={filters.tipologia}
                    onChange={(e) => setFilters({...filters, tipologia: e.target.value})}
                  >
                    <MenuItem value="">Tutte</MenuItem>
                    {[...new Set(cavi.map(c => c.tipologia))].filter(Boolean).map(tip => (
                      <MenuItem key={tip} value={tip}>{tip}</MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>

              <Grid item xs={12} md={3}>
                <FormControl fullWidth size="small">
                  <InputLabel>Stato Certificazione</InputLabel>
                  <Select
                    value={filters.certificazione}
                    onChange={(e) => setFilters({...filters, certificazione: e.target.value})}
                  >
                    <MenuItem value="">Tutti</MenuItem>
                    <MenuItem value="CERTIFICATO">Certificato</MenuItem>
                    <MenuItem value="NON_CERTIFICATO">Non Certificato</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
            </>
          )}

          {/* Filtri per tab Certificazioni */}
          {activeTab === 1 && (
            <>
              <Grid item xs={12} md={3}>
                <FormControl fullWidth size="small">
                  <InputLabel>Operatore</InputLabel>
                  <Select
                    value={filters.operatore}
                    onChange={(e) => setFilters({...filters, operatore: e.target.value})}
                  >
                    <MenuItem value="">Tutti</MenuItem>
                    {[...new Set(certificazioni.map(c => c.operatore))].filter(Boolean).map(op => (
                      <MenuItem key={op} value={op}>{op}</MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>

              <Grid item xs={12} md={3}>
                <FormControl fullWidth size="small">
                  <InputLabel>Risultato Test</InputLabel>
                  <Select
                    value={filters.risultatoTest}
                    onChange={(e) => setFilters({...filters, risultatoTest: e.target.value})}
                  >
                    <MenuItem value="">Tutti</MenuItem>
                    <MenuItem value="CONFORME">Conforme</MenuItem>
                    <MenuItem value="NON_CONFORME">Non Conforme</MenuItem>
                    <MenuItem value="DA_VERIFICARE">Da Verificare</MenuItem>
                  </Select>
                </FormControl>
              </Grid>

              <Grid item xs={12} md={3}>
                <TextField
                  fullWidth
                  size="small"
                  label="Isolamento Min (MΩ)"
                  type="number"
                  value={filters.valoreIsolamento}
                  onChange={(e) => setFilters({...filters, valoreIsolamento: e.target.value})}
                  placeholder="es. 500"
                />
              </Grid>

              <Grid item xs={12} md={3}>
                <TextField
                  fullWidth
                  size="small"
                  label="Data Inizio"
                  type="date"
                  value={filters.dataInizio}
                  onChange={(e) => setFilters({...filters, dataInizio: e.target.value})}
                  InputLabelProps={{ shrink: true }}
                />
              </Grid>

              <Grid item xs={12} md={3}>
                <TextField
                  fullWidth
                  size="small"
                  label="Data Fine"
                  type="date"
                  value={filters.dataFine}
                  onChange={(e) => setFilters({...filters, dataFine: e.target.value})}
                  InputLabelProps={{ shrink: true }}
                />
              </Grid>
            </>
          )}

          <Grid item xs={12}>
            <Stack direction="row" spacing={1} justifyContent="flex-end">
              <Button
                variant="outlined"
                size="small"
                onClick={() => setFilters({
                  stato: '', tipologia: '', operatore: '', dataInizio: '',
                  dataFine: '', valoreIsolamento: '', risultatoTest: '', strumento: '',
                  certificazione: ''
                })}
              >
                Pulisci Tutti i Filtri
              </Button>
            </Stack>
          </Grid>
        </Grid>
      </Collapse>

      {/* Barra azioni bulk */}
      {bulkMode && bulkSelection.length > 0 && (
        <>
          <Divider sx={{ my: 2 }} />
          <Stack direction="row" spacing={2} alignItems="center">
            <Typography variant="body2">
              {bulkSelection.length} elementi selezionati
            </Typography>
            <Button
              size="small"
              variant="outlined"
              onClick={selectAllItems}
            >
              Seleziona Tutto
            </Button>
            <Button
              size="small"
              variant="outlined"
              onClick={clearSelection}
            >
              Deseleziona
            </Button>
            <Button
              size="small"
              variant="outlined"
              startIcon={<ExportIcon />}
              onClick={handleBulkExport}
            >
              Esporta Selezionati
            </Button>
            <Button
              size="small"
              variant="outlined"
              color="error"
              startIcon={<DeleteIcon />}
              onClick={handleBulkDelete}
            >
              Elimina Selezionati
            </Button>
          </Stack>
        </>
      )}
    </Paper>
  );

  // Renderizza la tabella dei cavi
  const renderCaviTable = () => {
    const currentItems = getCurrentPageItems(filteredCavi);

    if (filteredCavi.length === 0) {
      return (
        <Alert severity="info">
          {searchTerm || filters.stato || filters.tipologia
            ? 'Nessun cavo trovato con i filtri applicati'
            : 'Nessun cavo disponibile'}
        </Alert>
      );
    }

    return (
      <>
        <TableContainer component={Paper}>
          <Table size="small">
            <TableHead>
              <TableRow>
                <TableCell>ID Cavo</TableCell>
                <TableCell>Tipologia</TableCell>
                <TableCell>Sezione</TableCell>
                <TableCell>Partenza</TableCell>
                <TableCell>Arrivo</TableCell>
                <TableCell>Metri</TableCell>
                <TableCell>Stato</TableCell>
                <TableCell>Collegamenti</TableCell>
                <TableCell>Certificato</TableCell>
                <TableCell>Azioni</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {currentItems.map((cavo) => {
                const isCertificato = isCavoCertificato(cavo.id_cavo);
                const puoCertificare = puoEssereCertificato(cavo);
                const messaggioErrore = !puoCertificare ? getMessaggioErroreCertificazione(cavo) : '';

                return (
                  <TableRow key={cavo.id_cavo}>
                    <TableCell>
                      <Typography variant="body2" fontWeight="medium">
                        {cavo.id_cavo}
                      </Typography>
                    </TableCell>
                    <TableCell>{cavo.tipologia}</TableCell>
                    <TableCell>{cavo.sezione}</TableCell>
                    <TableCell>{cavo.ubicazione_partenza}</TableCell>
                    <TableCell>{cavo.ubicazione_arrivo}</TableCell>
                    <TableCell>{cavo.metratura_reale || cavo.metri_teorici} m</TableCell>
                    <TableCell>
                      <Chip
                        size="small"
                        label={cavo.stato_installazione}
                        color={cavo.stato_installazione === 'INSTALLATO' ? 'success' : 'default'}
                      />
                    </TableCell>
                    <TableCell>
                      {(() => {
                        const collegamenti = cavo.collegamenti || 0;
                        const statoCollegamento = collegamenti === 0 ? 'Non collegato' :
                                                 collegamenti === 1 ? 'Solo partenza' :
                                                 collegamenti === 2 ? 'Solo arrivo' :
                                                 collegamenti === 3 ? 'Completo' :
                                                 'Sconosciuto';
                        const colore = collegamenti === 3 ? 'success' :
                                      collegamenti === 0 ? 'error' : 'warning';

                        return (
                          <Tooltip title={`Partenza: ${cavo.responsabile_partenza || 'Non collegato'} | Arrivo: ${cavo.responsabile_arrivo || 'Non collegato'}`}>
                            <Chip
                              size="small"
                              label={statoCollegamento}
                              color={colore}
                              icon={collegamenti === 3 ? <CheckIcon /> : <WarningIcon />}
                            />
                          </Tooltip>
                        );
                      })()}
                    </TableCell>
                    <TableCell>
                      {isCertificato ? (
                        <Chip
                          size="small"
                          icon={<CheckIcon />}
                          label="Certificato"
                          color="success"
                        />
                      ) : (
                        <Chip
                          size="small"
                          icon={<WarningIcon />}
                          label="Non certificato"
                          color="warning"
                        />
                      )}
                    </TableCell>
                    <TableCell>
                      {isCertificato ? (
                        <Tooltip title="Cavo già certificato">
                          <Chip
                            icon={<CheckIcon />}
                            label="Certificato"
                            color="success"
                            size="small"
                          />
                        </Tooltip>
                      ) : puoCertificare ? (
                        <Tooltip title="Crea certificazione per questo cavo">
                          <IconButton
                            size="small"
                            onClick={() => openCreateDialog(cavo)}
                            color="primary"
                          >
                            <AddIcon />
                          </IconButton>
                        </Tooltip>
                      ) : (
                        <Tooltip title={messaggioErrore}>
                          <span>
                            <IconButton
                              size="small"
                              disabled
                              onClick={() => showSnackbar(messaggioErrore, 'warning')}
                            >
                              <BlockIcon />
                            </IconButton>
                          </span>
                        </Tooltip>
                      )}
                    </TableCell>
                  </TableRow>
                );
              })}
            </TableBody>
          </Table>
        </TableContainer>

        {getTotalPages(filteredCavi) > 1 && (
          <Box sx={{ display: 'flex', justifyContent: 'center', mt: 2 }}>
            <Pagination
              count={getTotalPages(filteredCavi)}
              page={currentPage}
              onChange={(event, value) => setCurrentPage(value)}
              color="primary"
            />
          </Box>
        )}
      </>
    );
  };

  // Renderizza la tabella delle certificazioni
  const renderCertificazioniTable = () => {
    const currentItems = getCurrentPageItems(filteredCertificazioni);

    if (filteredCertificazioni.length === 0) {
      return (
        <Alert severity="info">
          {searchTerm || filters.operatore
            ? 'Nessuna certificazione trovata con i filtri applicati'
            : 'Nessuna certificazione disponibile'}
        </Alert>
      );
    }

    return (
      <>
        <TableContainer component={Paper}>
          <Table size="small">
            <TableHead>
              <TableRow>
                {bulkMode && (
                  <TableCell padding="checkbox">
                    <IconButton
                      size="small"
                      onClick={bulkSelection.length === filteredCertificazioni.length ? clearSelection : selectAllItems}
                    >
                      {bulkSelection.length === filteredCertificazioni.length ? <ClearIcon /> : <CheckIcon />}
                    </IconButton>
                  </TableCell>
                )}
                <TableCell>
                  <Stack direction="row" alignItems="center" spacing={1}>
                    <Typography variant="body2" fontWeight="bold">N° Certificato</Typography>
                    <IconButton size="small" onClick={() => {
                      setSortBy('numero_certificato');
                      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
                    }}>
                      {sortBy === 'numero_certificato' ? (sortOrder === 'asc' ? <ExpandLessIcon /> : <ExpandMoreIcon />) : <ExpandMoreIcon />}
                    </IconButton>
                  </Stack>
                </TableCell>
                <TableCell>ID Cavo</TableCell>
                <TableCell>
                  <Stack direction="row" alignItems="center" spacing={1}>
                    <Typography variant="body2" fontWeight="bold">Data</Typography>
                    <IconButton size="small" onClick={() => {
                      setSortBy('data_certificazione');
                      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
                    }}>
                      {sortBy === 'data_certificazione' ? (sortOrder === 'asc' ? <ExpandLessIcon /> : <ExpandMoreIcon />) : <ExpandMoreIcon />}
                    </IconButton>
                  </Stack>
                </TableCell>
                <TableCell>Operatore</TableCell>
                <TableCell>Strumento</TableCell>
                <TableCell>Lunghezza</TableCell>
                <TableCell>Isolamento</TableCell>
                <TableCell>Risultato</TableCell>
                <TableCell>Azioni</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {currentItems.map((cert) => (
                <TableRow
                  key={cert.id_certificazione}
                  selected={bulkSelection.includes(cert.id_certificazione)}
                  hover
                >
                  {bulkMode && (
                    <TableCell padding="checkbox">
                      <IconButton
                        size="small"
                        onClick={() => toggleItemSelection(cert.id_certificazione)}
                        color={bulkSelection.includes(cert.id_certificazione) ? 'primary' : 'default'}
                      >
                        {bulkSelection.includes(cert.id_certificazione) ? <CheckIcon /> : <AddIcon />}
                      </IconButton>
                    </TableCell>
                  )}
                  <TableCell>
                    <Typography variant="body2" fontWeight="medium">
                      {cert.numero_certificato}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Chip size="small" label={cert.id_cavo} variant="outlined" />
                  </TableCell>
                  <TableCell>{new Date(cert.data_certificazione).toLocaleDateString()}</TableCell>
                  <TableCell>
                    <Stack direction="row" alignItems="center" spacing={1}>
                      <PersonIcon fontSize="small" />
                      <Typography variant="body2">{cert.operatore || cert.id_operatore}</Typography>
                    </Stack>
                  </TableCell>
                  <TableCell>
                    <Typography variant="body2">
                      {cert.id_strumento ?
                        (() => {
                          const strumento = strumenti.find(s => s.id_strumento === cert.id_strumento);
                          return strumento ? `${strumento.nome} - ${strumento.marca}` : 'Strumento non trovato';
                        })()
                        : (cert.strumento_utilizzato || 'N/A')
                      }
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Typography variant="body2">{cert.lunghezza_misurata} m</Typography>
                  </TableCell>
                  <TableCell>
                    <Chip
                      size="small"
                      label={`${cert.valore_isolamento} MΩ`}
                      color={parseFloat(cert.valore_isolamento) >= 500 ? 'success' : 'warning'}
                      icon={parseFloat(cert.valore_isolamento) >= 500 ? <CheckIcon /> : <WarningIcon />}
                    />
                  </TableCell>
                  <TableCell>
                    <Chip
                      size="small"
                      label={cert.risultato_finale || 'CONFORME'}
                      color={cert.risultato_finale === 'CONFORME' ? 'success' : cert.risultato_finale === 'NON_CONFORME' ? 'error' : 'warning'}
                    />
                  </TableCell>
                  <TableCell>
                    <Stack direction="row" spacing={0.5}>
                      <Tooltip title="Visualizza dettagli">
                        <IconButton
                          size="small"
                          onClick={() => {
                            setSelectedItem(cert);
                            setDialogType('view');
                            setOpenDialog(true);
                          }}
                        >
                          <ViewIcon />
                        </IconButton>
                      </Tooltip>
                      <Tooltip title="Genera PDF">
                        <IconButton
                          size="small"
                          onClick={() => handleGeneratePdf(cert)}
                          disabled={operationInProgress}
                        >
                          <PdfIcon />
                        </IconButton>
                      </Tooltip>
                      <Tooltip title="Elimina">
                        <IconButton
                          size="small"
                          color="error"
                          onClick={() => handleDeleteCertificazione(cert)}
                          disabled={operationInProgress}
                        >
                          <DeleteIcon />
                        </IconButton>
                      </Tooltip>
                    </Stack>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>

        {getTotalPages(filteredCertificazioni) > 1 && (
          <Box sx={{ display: 'flex', justifyContent: 'center', mt: 2 }}>
            <Pagination
              count={getTotalPages(filteredCertificazioni)}
              page={currentPage}
              onChange={(event, value) => setCurrentPage(value)}
              color="primary"
            />
          </Box>
        )}
      </>
    );
  };

  // Renderizza il dialog per creare/modificare certificazione
  const renderCertificazioneDialog = () => {
    if (dialogType !== 'create' && dialogType !== 'edit') return null;

    return (
      <Dialog open={openDialog} onClose={closeDialog} maxWidth="md" fullWidth>
        <DialogTitle>
          {dialogType === 'create' ? 'Nuova Certificazione' : 'Modifica Certificazione'}
        </DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12} md={6}>
              <Autocomplete
                options={cavi.filter(cavo => {
                  // Mostra solo cavi che possono essere certificati o quello già selezionato
                  const isSelected = cavo.id_cavo === formData.id_cavo;
                  const isNotCertified = !certificazioni.some(cert => cert.id_cavo === cavo.id_cavo);
                  const canBeCertified = puoEssereCertificato(cavo);

                  return isSelected || (isNotCertified && canBeCertified);
                })}
                getOptionLabel={(option) => `${option.id_cavo} - ${option.tipologia}`}
                value={cavi.find(c => c.id_cavo === formData.id_cavo) || null}
                onChange={(event, newValue) => {
                  if (newValue) {
                    handleCavoSelect(newValue);
                  } else {
                    setFormData(prev => ({ ...prev, id_cavo: '', lunghezza_misurata: '' }));
                  }
                }}
                renderInput={(params) => (
                  <TextField
                    {...params}
                    label="Cavo *"
                    placeholder="Seleziona un cavo posato"
                    required
                    helperText="Solo cavi posati/installati (il collegamento può essere gestito al momento)"
                  />
                )}
                renderOption={(props, option) => {
                  const collegamenti = option.collegamenti || 0;
                  const isCollegato = collegamenti === 3;

                  return (
                    <Box component="li" {...props}>
                      <Box sx={{ width: '100%' }}>
                        <Stack direction="row" justifyContent="space-between" alignItems="center">
                          <Box>
                            <Typography variant="body2" fontWeight="medium">
                              {option.id_cavo}
                            </Typography>
                            <Typography variant="caption" color="text.secondary">
                              {option.tipologia} - {option.ubicazione_partenza} → {option.ubicazione_arrivo}
                            </Typography>
                          </Box>
                          <Stack direction="row" spacing={1}>
                            <Chip
                              size="small"
                              label={option.stato_installazione}
                              color={option.stato_installazione === 'INSTALLATO' ? 'success' : 'default'}
                            />
                            <Chip
                              size="small"
                              label={isCollegato ? 'Collegato' : 'Da collegare'}
                              color={isCollegato ? 'success' : 'warning'}
                              icon={isCollegato ? <CheckIcon /> : <WarningIcon />}
                            />
                          </Stack>
                        </Stack>
                      </Box>
                    </Box>
                  );
                }}
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Operatore *"
                value={formData.id_operatore}
                onChange={(e) => handleFormChange('id_operatore', e.target.value)}
                required
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <FormControl fullWidth required>
                <InputLabel>Strumento *</InputLabel>
                <Select
                  value={formData.id_strumento}
                  onChange={(e) => handleFormChange('id_strumento', e.target.value)}
                  label="Strumento *"
                >
                  {strumenti.map((strumento) => (
                    <MenuItem key={strumento.id_strumento} value={strumento.id_strumento}>
                      {strumento.nome} - {strumento.marca} {strumento.modello} (S/N: {strumento.numero_serie})
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Lunghezza Misurata (m) *"
                type="number"
                value={formData.lunghezza_misurata}
                onChange={(e) => handleFormChange('lunghezza_misurata', e.target.value)}
                required
              />
            </Grid>

            <Grid item xs={12} md={4}>
              <FormControl fullWidth>
                <InputLabel>Continuità</InputLabel>
                <Select
                  value={formData.valore_continuita}
                  onChange={(e) => handleFormChange('valore_continuita', e.target.value)}
                  label="Continuità"
                >
                  <MenuItem value="OK">OK</MenuItem>
                  <MenuItem value="NOK">NOK</MenuItem>
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                label="Isolamento (MΩ) *"
                type="number"
                value={formData.valore_isolamento}
                onChange={(e) => handleFormChange('valore_isolamento', e.target.value)}
                required
                helperText="Valore minimo consigliato: 500 MΩ"
              />
            </Grid>

            <Grid item xs={12} md={4}>
              <FormControl fullWidth>
                <InputLabel>Resistenza</InputLabel>
                <Select
                  value={formData.valore_resistenza}
                  onChange={(e) => handleFormChange('valore_resistenza', e.target.value)}
                  label="Resistenza"
                >
                  <MenuItem value="OK">OK</MenuItem>
                  <MenuItem value="NOK">NOK</MenuItem>
                </Select>
              </FormControl>
            </Grid>

            {/* Sezione Collegamenti */}
            <Grid item xs={12}>
              <Divider sx={{ my: 2 }}>
                <Typography variant="body2" color="text.secondary">
                  Stato Collegamenti Cavo
                </Typography>
              </Divider>
            </Grid>

            {formData.id_cavo && (() => {
              const cavo = cavi.find(c => c.id_cavo === formData.id_cavo);
              if (!cavo) return null;

              const collegamenti = cavo.collegamenti || 0;
              const isCollegato = collegamenti === 3;

              return (
                <Grid item xs={12}>
                  <Paper sx={{ p: 2, bgcolor: isCollegato ? 'success.light' : 'warning.light' }}>
                    <Stack direction="row" alignItems="center" spacing={2}>
                      {isCollegato ? <CheckIcon color="success" /> : <WarningIcon color="warning" />}
                      <Box>
                        <Typography variant="body2" fontWeight="bold">
                          {isCollegato ? 'Cavo Completamente Collegato' : 'Cavo Non Completamente Collegato'}
                        </Typography>
                        <Typography variant="caption">
                          Stato: {collegamenti === 0 ? 'Non collegato' :
                                  collegamenti === 1 ? 'Solo partenza collegata' :
                                  collegamenti === 2 ? 'Solo arrivo collegato' :
                                  collegamenti === 3 ? 'Completamente collegato' :
                                  'Stato sconosciuto'}
                        </Typography>
                        {!isCollegato && (
                          <Typography variant="caption" display="block" sx={{ mt: 1 }}>
                            ⚠️ Il cavo può essere certificato ma ricorda di completare i collegamenti prima della messa in servizio
                          </Typography>
                        )}
                      </Box>
                    </Stack>
                  </Paper>
                </Grid>
              );
            })()}

            {/* Campi avanzati */}
            <Grid item xs={12}>
              <Divider sx={{ my: 2 }}>
                <Typography variant="body2" color="text.secondary">
                  Parametri Ambientali e Test Avanzati
                </Typography>
              </Divider>
            </Grid>

            <Grid item xs={12} md={3}>
              <TextField
                fullWidth
                label="Temperatura Ambiente (°C)"
                type="number"
                value={formData.temperatura_ambiente}
                onChange={(e) => handleFormChange('temperatura_ambiente', e.target.value)}
                helperText="Temperatura durante il test"
              />
            </Grid>

            <Grid item xs={12} md={3}>
              <TextField
                fullWidth
                label="Umidità (%)"
                type="number"
                value={formData.umidita}
                onChange={(e) => handleFormChange('umidita', e.target.value)}
                helperText="Umidità relativa"
              />
            </Grid>

            <Grid item xs={12} md={3}>
              <TextField
                fullWidth
                label="Tensione di Prova (V)"
                type="number"
                value={formData.tensione_prova}
                onChange={(e) => handleFormChange('tensione_prova', e.target.value)}
                helperText="Tensione applicata per il test"
              />
            </Grid>

            <Grid item xs={12} md={3}>
              <TextField
                fullWidth
                label="Durata Prova (min)"
                type="number"
                value={formData.durata_prova}
                onChange={(e) => handleFormChange('durata_prova', e.target.value)}
                helperText="Durata del test in minuti"
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <FormControl fullWidth>
                <InputLabel>Risultato Finale</InputLabel>
                <Select
                  value={formData.risultato_finale}
                  onChange={(e) => handleFormChange('risultato_finale', e.target.value)}
                  label="Risultato Finale"
                >
                  <MenuItem value="CONFORME">
                    <Stack direction="row" alignItems="center" spacing={1}>
                      <CheckIcon color="success" />
                      <Typography>Conforme</Typography>
                    </Stack>
                  </MenuItem>
                  <MenuItem value="NON_CONFORME">
                    <Stack direction="row" alignItems="center" spacing={1}>
                      <ErrorIcon color="error" />
                      <Typography>Non Conforme</Typography>
                    </Stack>
                  </MenuItem>
                  <MenuItem value="DA_VERIFICARE">
                    <Stack direction="row" alignItems="center" spacing={1}>
                      <WarningIcon color="warning" />
                      <Typography>Da Verificare</Typography>
                    </Stack>
                  </MenuItem>
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Note"
                multiline
                rows={3}
                value={formData.note}
                onChange={(e) => handleFormChange('note', e.target.value)}
                placeholder="Inserisci eventuali note, osservazioni o anomalie riscontrate durante il test..."
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={closeDialog}>Annulla</Button>
          <Button
            onClick={handleCreateCertificazione}
            variant="contained"
            disabled={loading || !formData.id_cavo || !formData.id_operatore || !formData.id_strumento || !formData.valore_isolamento}
            startIcon={loading ? <CircularProgress size={20} /> : <SaveIcon />}
          >
            {dialogType === 'create' ? 'Crea Certificazione' : 'Salva Modifiche'}
          </Button>
        </DialogActions>
      </Dialog>
    );
  };

  // Renderizza il dialog di visualizzazione dettagli
  const renderViewDialog = () => {
    if (dialogType !== 'view' || !selectedItem) return null;

    return (
      <Dialog open={openDialog} onClose={closeDialog} maxWidth="md" fullWidth>
        <DialogTitle>
          Dettagli Certificazione - {selectedItem.numero_certificato}
        </DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12} md={6}>
              <Card variant="outlined">
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Informazioni Cavo
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    ID Cavo: <strong>{selectedItem.id_cavo}</strong>
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Lunghezza Misurata: <strong>{selectedItem.lunghezza_misurata} m</strong>
                  </Typography>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} md={6}>
              <Card variant="outlined">
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Informazioni Certificazione
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Numero: <strong>{selectedItem.numero_certificato}</strong>
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Data: <strong>{new Date(selectedItem.data_certificazione).toLocaleDateString()}</strong>
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Operatore: <strong>{selectedItem.operatore || selectedItem.id_operatore}</strong>
                  </Typography>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12}>
              <Card variant="outlined">
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Risultati Test
                  </Typography>
                  <Grid container spacing={2}>
                    <Grid item xs={4}>
                      <Typography variant="body2" color="text.secondary">
                        Continuità
                      </Typography>
                      <Chip
                        size="small"
                        label={selectedItem.valore_continuita}
                        color={selectedItem.valore_continuita === 'OK' ? 'success' : 'error'}
                      />
                    </Grid>
                    <Grid item xs={4}>
                      <Typography variant="body2" color="text.secondary">
                        Isolamento
                      </Typography>
                      <Chip
                        size="small"
                        label={`${selectedItem.valore_isolamento} MΩ`}
                        color={parseFloat(selectedItem.valore_isolamento) >= 500 ? 'success' : 'warning'}
                      />
                    </Grid>
                    <Grid item xs={4}>
                      <Typography variant="body2" color="text.secondary">
                        Resistenza
                      </Typography>
                      <Chip
                        size="small"
                        label={selectedItem.valore_resistenza}
                        color={selectedItem.valore_resistenza === 'OK' ? 'success' : 'error'}
                      />
                    </Grid>
                  </Grid>
                </CardContent>
              </Card>
            </Grid>

            {selectedItem.note && (
              <Grid item xs={12}>
                <Card variant="outlined">
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      Note
                    </Typography>
                    <Typography variant="body2">
                      {selectedItem.note}
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
            )}
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={closeDialog}>Chiudi</Button>
          <Button
            onClick={() => handleGeneratePdf(selectedItem)}
            variant="contained"
            startIcon={<PdfIcon />}
            disabled={loading}
          >
            Genera PDF
          </Button>
        </DialogActions>
      </Dialog>
    );
  };

  // Renderizza le statistiche
  const renderStats = () => {
    const totalCavi = cavi.length;
    const caviInstallati = cavi.filter(c => c.stato_installazione === 'INSTALLATO').length;
    const caviCertificati = certificazioni.length;
    const percentualeCertificazione = totalCavi > 0 ? Math.round((caviCertificati / caviInstallati) * 100) : 0;

    return (
      <Grid container spacing={2} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="text.secondary" gutterBottom>
                Cavi Totali
              </Typography>
              <Typography variant="h4">
                {totalCavi}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="text.secondary" gutterBottom>
                Cavi Installati
              </Typography>
              <Typography variant="h4">
                {caviInstallati}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="text.secondary" gutterBottom>
                Certificazioni
              </Typography>
              <Typography variant="h4">
                {caviCertificati}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="text.secondary" gutterBottom>
                % Certificazione
              </Typography>
              <Typography variant="h4" color={percentualeCertificazione >= 80 ? 'success.main' : 'warning.main'}>
                {percentualeCertificazione}%
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    );
  };

  return (
    <Container maxWidth="xl" sx={{ py: 3 }}>
      {/* Header con titolo e azioni rapide */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom fontWeight="bold">
          🔌 Sistema di Certificazione Cavi
        </Typography>
        <Typography variant="body1" color="text.secondary" sx={{ mb: 2 }}>
          Gestione completa delle certificazioni elettriche secondo standard CEI 64-8
        </Typography>

        {/* Messaggio di aiuto */}
        <Paper sx={{ p: 2, bgcolor: 'info.light', color: 'info.contrastText', mb: 2 }}>
          <Stack direction="row" alignItems="center" spacing={2}>
            <InfoIcon />
            <Box>
              <Typography variant="body2" fontWeight="bold">
                Come utilizzare il sistema:
              </Typography>
              <Typography variant="caption">
                • <strong>Cavi da Certificare:</strong> Clicca sul "+" per certificare cavi posati (collegamento gestibile al momento)
                • <strong>Certificazioni Completate:</strong> Gestisci certificazioni esistenti, usa "Selezione" per operazioni multiple
                • <strong>Report Dettagliato:</strong> Clicca il pulsante per grafici e analisi avanzate di avanzamento
              </Typography>
            </Box>
          </Stack>
        </Paper>
      </Box>

      {/* Dashboard con statistiche */}
      {renderDashboard()}

      {/* Progress bar per operazioni in corso */}
      {(loading || operationInProgress) && (
        <Box sx={{ mb: 2 }}>
          <LinearProgress />
          {progress > 0 && (
            <Typography variant="caption" color="text.secondary" sx={{ mt: 1 }}>
              Caricamento... {progress}%
            </Typography>
          )}
        </Box>
      )}

      {/* Tabs per navigazione */}
      <Paper sx={{ mb: 3 }}>
        <Tabs
          value={activeTab}
          onChange={handleTabChange}
          indicatorColor="primary"
          textColor="primary"
          variant="fullWidth"
        >
          <Tab
            label={
              <Stack direction="row" alignItems="center" spacing={1}>
                <CableIcon />
                <Box>
                  <Typography variant="body2" fontWeight="bold">
                    Cavi da Certificare
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    {filteredCavi.length} cavi totali
                  </Typography>
                </Box>
                {statistics.caviNonCertificati > 0 && (
                  <Badge badgeContent={statistics.caviNonCertificati} color="warning">
                    <WarningIcon />
                  </Badge>
                )}
              </Stack>
            }
          />
          <Tab
            label={
              <Stack direction="row" alignItems="center" spacing={1}>
                <ScienceIcon />
                <Box>
                  <Typography variant="body2" fontWeight="bold">
                    Certificazioni Completate
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    {filteredCertificazioni.length} certificazioni
                  </Typography>
                </Box>
                {statistics.certificazioniOggi > 0 && (
                  <Badge badgeContent={statistics.certificazioniOggi} color="success">
                    <CheckIcon />
                  </Badge>
                )}
              </Stack>
            }
          />
        </Tabs>
      </Paper>

      {/* Barra di ricerca e filtri avanzati */}
      {renderSearchAndFilters()}

      {/* Contenuto delle tabs */}
      {!loading && activeTab === 0 && renderCaviTable()}
      {!loading && activeTab === 1 && renderCertificazioniTable()}

      {/* Dialogs */}
      {renderCertificazioneDialog()}
      {renderViewDialog()}

      {/* Snackbar per notifiche */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={closeSnackbar}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert onClose={closeSnackbar} severity={snackbar.severity} sx={{ width: '100%' }}>
          {snackbar.message}
        </Alert>
      </Snackbar>

      {/* Speed Dial per azioni rapide */}
      <SpeedDial
        ariaLabel="Azioni rapide"
        sx={{ position: 'fixed', bottom: 16, right: 16 }}
        icon={<SpeedDialIcon />}
      >
        <SpeedDialAction
          icon={<AddIcon />}
          tooltipTitle="Nuova Certificazione"
          onClick={openCreateDialog}
        />
        <SpeedDialAction
          icon={<ExportIcon />}
          tooltipTitle="Esporta Tutto"
          onClick={handleExportAll}
        />
        <SpeedDialAction
          icon={<RefreshIcon />}
          tooltipTitle="Aggiorna Dati"
          onClick={loadInitialData}
        />
      </SpeedDial>
    </Container>
  );
});

export default CertificazioneCaviImproved;
