import React, { useState, useEffect, forwardRef, useImperativeHandle } from 'react';
import {
  Box,
  Typo<PERSON>,
  Button,
  Paper,
  Grid,
  Card,
  CardContent,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Autocomplete,
  CircularProgress,
  Alert,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  IconButton,
  Tabs,
  Tab,
  Pagination,
  InputAdornment,
  Divider,
  Stack,
  Chip,
  Tooltip,
  Badge,
  LinearProgress,
  Collapse,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Snackbar,
  AppBar,
  Toolbar,
  Container,
  Fab,
  SpeedDial,
  SpeedDialAction,
  SpeedDialIcon
} from '@mui/material';
import {
  Add as AddIcon,
  Search as SearchIcon,
  FilterList as FilterIcon,
  PictureAsPdf as PdfIcon,
  Download as DownloadIcon,
  Visibility as ViewIcon,
  Delete as DeleteIcon,
  Edit as EditIcon,
  Save as SaveIcon,
  Clear as ClearIcon,
  Build as BuildIcon,
  CheckCircle as CheckIcon,
  Warning as WarningIcon,
  GetApp as ExportIcon,
  Print as PrintIcon,
  Email as EmailIcon,
  CloudUpload as UploadIcon,
  Assessment as ReportIcon,
  Settings as SettingsIcon,
  Refresh as RefreshIcon,
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon,
  Info as InfoIcon,
  Error as ErrorIcon,
  Schedule as ScheduleIcon,
  Person as PersonIcon,
  Cable as CableIcon,
  Science as ScienceIcon
} from '@mui/icons-material';

import certificazioneService from '../../services/certificazioneService';
import caviService from '../../services/caviService';

const CertificazioneCaviImproved = forwardRef(({ cantiereId, onSuccess, onError }, ref) => {
  // Stati principali
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState(0);
  const [certificazioni, setCertificazioni] = useState([]);
  const [cavi, setCavi] = useState([]);
  const [strumenti, setStrumenti] = useState([]);

  // Stati per ricerca e filtri avanzati
  const [searchTerm, setSearchTerm] = useState('');
  const [filteredCavi, setFilteredCavi] = useState([]);
  const [filteredCertificazioni, setFilteredCertificazioni] = useState([]);
  const [advancedFiltersOpen, setAdvancedFiltersOpen] = useState(false);
  const [filters, setFilters] = useState({
    stato: '',
    tipologia: '',
    operatore: '',
    dataInizio: '',
    dataFine: '',
    valoreIsolamento: '',
    risultatoTest: '',
    strumento: ''
  });

  // Stati per paginazione e ordinamento
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [sortBy, setSortBy] = useState('data_certificazione');
  const [sortOrder, setSortOrder] = useState('desc');

  // Stati per dialogs e modali
  const [openDialog, setOpenDialog] = useState(false);
  const [dialogType, setDialogType] = useState('');
  const [selectedItem, setSelectedItem] = useState(null);
  const [bulkSelection, setBulkSelection] = useState([]);
  const [bulkMode, setBulkMode] = useState(false);

  // Stati per notifiche e feedback
  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'info' });
  const [progress, setProgress] = useState(0);
  const [operationInProgress, setOperationInProgress] = useState(false);

  // Stati per form certificazione avanzato
  const [formData, setFormData] = useState({
    id_cavo: '',
    id_operatore: '',
    id_strumento: '',
    lunghezza_misurata: '',
    valore_continuita: 'OK',
    valore_isolamento: '',
    valore_resistenza: 'OK',
    note: '',
    temperatura_ambiente: '',
    umidita: '',
    tensione_prova: '',
    durata_prova: '',
    risultato_finale: 'CONFORME'
  });

  // Stati per statistiche e dashboard
  const [statistics, setStatistics] = useState({
    totaleCavi: 0,
    caviCertificati: 0,
    caviNonCertificati: 0,
    percentualeCompletamento: 0,
    certificazioniOggi: 0,
    certificazioniSettimana: 0
  });

  // Carica dati iniziali
  useEffect(() => {
    loadInitialData();
  }, [cantiereId]);

  // Filtra cavi in base alla ricerca
  useEffect(() => {
    filterCavi();
  }, [cavi, searchTerm, filters, sortBy, sortOrder]);

  // Filtra certificazioni
  useEffect(() => {
    filterCertificazioni();
  }, [certificazioni, searchTerm, filters, sortBy, sortOrder]);

  // Ricalcola statistiche quando cambiano i dati
  useEffect(() => {
    calculateStatistics();
  }, [cavi, certificazioni]);

  const loadInitialData = async () => {
    try {
      setLoading(true);
      setProgress(0);

      // Carica dati in sequenza con progress
      setProgress(25);
      await loadCavi();

      setProgress(50);
      await loadCertificazioni();

      setProgress(75);
      await loadStrumenti();

      setProgress(100);
      calculateStatistics();

    } catch (error) {
      showSnackbar('Errore nel caricamento dei dati iniziali', 'error');
      onError('Errore nel caricamento dei dati iniziali');
    } finally {
      setLoading(false);
      setProgress(0);
    }
  };

  const loadCertificazioni = async () => {
    try {
      const data = await certificazioneService.getCertificazioni(cantiereId);
      setCertificazioni(data);
      return data;
    } catch (error) {
      console.error('Errore nel caricamento delle certificazioni:', error);
      throw error;
    }
  };

  const loadCavi = async () => {
    try {
      const data = await caviService.getCavi(cantiereId);
      setCavi(data);
      return data;
    } catch (error) {
      console.error('Errore nel caricamento dei cavi:', error);
      throw error;
    }
  };

  const loadStrumenti = async () => {
    try {
      const data = await certificazioneService.getStrumenti(cantiereId);
      setStrumenti(data);
      return data;
    } catch (error) {
      console.error('Errore nel caricamento degli strumenti:', error);
      throw error;
    }
  };

  // Calcola statistiche avanzate
  const calculateStatistics = () => {
    const totaleCavi = cavi.length;
    const caviCertificati = certificazioni.length;
    const caviNonCertificati = totaleCavi - caviCertificati;
    const percentualeCompletamento = totaleCavi > 0 ? Math.round((caviCertificati / totaleCavi) * 100) : 0;

    // Calcola certificazioni di oggi
    const oggi = new Date().toDateString();
    const certificazioniOggi = certificazioni.filter(cert =>
      new Date(cert.data_certificazione).toDateString() === oggi
    ).length;

    // Calcola certificazioni della settimana
    const unaSettimanaFa = new Date();
    unaSettimanaFa.setDate(unaSettimanaFa.getDate() - 7);
    const certificazioniSettimana = certificazioni.filter(cert =>
      new Date(cert.data_certificazione) >= unaSettimanaFa
    ).length;

    setStatistics({
      totaleCavi,
      caviCertificati,
      caviNonCertificati,
      percentualeCompletamento,
      certificazioniOggi,
      certificazioniSettimana
    });
  };

  // Gestione snackbar
  const showSnackbar = (message, severity = 'info') => {
    setSnackbar({ open: true, message, severity });
  };

  const closeSnackbar = () => {
    setSnackbar({ ...snackbar, open: false });
  };

  const filterCavi = () => {
    let filtered = cavi;

    // Filtro per ricerca testuale avanzata
    if (searchTerm) {
      const searchLower = searchTerm.toLowerCase();
      filtered = filtered.filter(cavo =>
        cavo.id_cavo.toLowerCase().includes(searchLower) ||
        cavo.tipologia?.toLowerCase().includes(searchLower) ||
        cavo.ubicazione_partenza?.toLowerCase().includes(searchLower) ||
        cavo.ubicazione_arrivo?.toLowerCase().includes(searchLower) ||
        cavo.sezione?.toLowerCase().includes(searchLower) ||
        cavo.utility?.toLowerCase().includes(searchLower)
      );
    }

    // Filtri specifici avanzati
    if (filters.stato) {
      filtered = filtered.filter(cavo => cavo.stato_installazione === filters.stato);
    }
    if (filters.tipologia) {
      filtered = filtered.filter(cavo => cavo.tipologia === filters.tipologia);
    }

    // Ordinamento
    filtered.sort((a, b) => {
      let aValue = a[sortBy];
      let bValue = b[sortBy];

      if (typeof aValue === 'string') {
        aValue = aValue.toLowerCase();
        bValue = bValue.toLowerCase();
      }

      if (sortOrder === 'asc') {
        return aValue > bValue ? 1 : -1;
      } else {
        return aValue < bValue ? 1 : -1;
      }
    });

    setFilteredCavi(filtered);
  };

  const filterCertificazioni = () => {
    let filtered = certificazioni;

    // Ricerca testuale avanzata
    if (searchTerm) {
      const searchLower = searchTerm.toLowerCase();
      filtered = filtered.filter(cert =>
        cert.id_cavo.toLowerCase().includes(searchLower) ||
        cert.operatore?.toLowerCase().includes(searchLower) ||
        cert.numero_certificato?.toLowerCase().includes(searchLower) ||
        cert.note?.toLowerCase().includes(searchLower)
      );
    }

    // Filtri avanzati
    if (filters.operatore) {
      filtered = filtered.filter(cert => cert.operatore === filters.operatore);
    }
    if (filters.strumento) {
      filtered = filtered.filter(cert => cert.strumento === filters.strumento);
    }
    if (filters.risultatoTest) {
      filtered = filtered.filter(cert => cert.risultato_finale === filters.risultatoTest);
    }
    if (filters.dataInizio) {
      filtered = filtered.filter(cert =>
        new Date(cert.data_certificazione) >= new Date(filters.dataInizio)
      );
    }
    if (filters.dataFine) {
      filtered = filtered.filter(cert =>
        new Date(cert.data_certificazione) <= new Date(filters.dataFine)
      );
    }
    if (filters.valoreIsolamento) {
      const valore = parseFloat(filters.valoreIsolamento);
      filtered = filtered.filter(cert =>
        parseFloat(cert.valore_isolamento) >= valore
      );
    }

    // Ordinamento
    filtered.sort((a, b) => {
      let aValue = a[sortBy];
      let bValue = b[sortBy];

      if (sortBy === 'data_certificazione') {
        aValue = new Date(aValue);
        bValue = new Date(bValue);
      } else if (typeof aValue === 'string') {
        aValue = aValue.toLowerCase();
        bValue = bValue.toLowerCase();
      }

      if (sortOrder === 'asc') {
        return aValue > bValue ? 1 : -1;
      } else {
        return aValue < bValue ? 1 : -1;
      }
    });

    setFilteredCertificazioni(filtered);
  };

  // Gestione selezione multipla
  const toggleBulkMode = () => {
    setBulkMode(!bulkMode);
    setBulkSelection([]);
  };

  const toggleItemSelection = (itemId) => {
    setBulkSelection(prev =>
      prev.includes(itemId)
        ? prev.filter(id => id !== itemId)
        : [...prev, itemId]
    );
  };

  const selectAllItems = () => {
    const currentItems = activeTab === 0 ? filteredCavi : filteredCertificazioni;
    const allIds = currentItems.map(item =>
      activeTab === 0 ? item.id_cavo : item.id_certificazione
    );
    setBulkSelection(allIds);
  };

  const clearSelection = () => {
    setBulkSelection([]);
  };

  // Gestione tabs
  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
    setCurrentPage(1);
    setSearchTerm('');
    setFilters({ stato: '', tipologia: '', operatore: '' });
  };

  // Gestione dialogs
  const openCreateDialog = () => {
    setDialogType('create');
    setSelectedItem(null);
    setFormData({
      id_cavo: '',
      id_operatore: '',
      id_strumento: '',
      lunghezza_misurata: '',
      valore_continuita: 'OK',
      valore_isolamento: '',
      valore_resistenza: 'OK',
      note: ''
    });
    setOpenDialog(true);
  };

  const closeDialog = () => {
    setOpenDialog(false);
    setSelectedItem(null);
    setDialogType('');
  };

  // Gestione form
  const handleFormChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleCavoSelect = (cavo) => {
    setFormData(prev => ({
      ...prev,
      id_cavo: cavo.id_cavo,
      lunghezza_misurata: cavo.metratura_reale || cavo.metri_teorici || ''
    }));
  };

  // Operazioni CRUD avanzate
  const handleCreateCertificazione = async () => {
    try {
      if (!formData.id_cavo || !formData.id_operatore || !formData.id_strumento || !formData.valore_isolamento) {
        showSnackbar('Compila tutti i campi obbligatori', 'warning');
        return;
      }

      setOperationInProgress(true);
      await certificazioneService.createCertificazione(cantiereId, formData);
      showSnackbar('Certificazione creata con successo', 'success');
      closeDialog();
      await loadCertificazioni();
      calculateStatistics();
    } catch (error) {
      showSnackbar('Errore nella creazione della certificazione: ' + (error.message || 'Errore sconosciuto'), 'error');
    } finally {
      setOperationInProgress(false);
    }
  };

  const handleGeneratePdf = async (certificazione) => {
    try {
      setOperationInProgress(true);
      const response = await certificazioneService.generatePdf(cantiereId, certificazione.id_certificazione);

      if (response.file_url) {
        window.open(response.file_url, '_blank');
        showSnackbar('PDF generato con successo', 'success');
      } else {
        showSnackbar('Errore nella generazione del PDF', 'error');
      }
    } catch (error) {
      showSnackbar('Errore nella generazione del PDF: ' + (error.message || 'Errore sconosciuto'), 'error');
    } finally {
      setOperationInProgress(false);
    }
  };

  const handleDeleteCertificazione = async (certificazione) => {
    if (window.confirm(`Sei sicuro di voler eliminare la certificazione ${certificazione.numero_certificato}?`)) {
      try {
        setOperationInProgress(true);
        await certificazioneService.deleteCertificazione(cantiereId, certificazione.id_certificazione);
        showSnackbar('Certificazione eliminata con successo', 'success');
        await loadCertificazioni();
        calculateStatistics();
      } catch (error) {
        showSnackbar('Errore nell\'eliminazione della certificazione: ' + (error.message || 'Errore sconosciuto'), 'error');
      } finally {
        setOperationInProgress(false);
      }
    }
  };

  // Operazioni bulk
  const handleBulkDelete = async () => {
    if (bulkSelection.length === 0) {
      showSnackbar('Seleziona almeno un elemento', 'warning');
      return;
    }

    if (window.confirm(`Sei sicuro di voler eliminare ${bulkSelection.length} certificazioni?`)) {
      try {
        setOperationInProgress(true);
        for (const id of bulkSelection) {
          await certificazioneService.deleteCertificazione(cantiereId, id);
        }
        showSnackbar(`${bulkSelection.length} certificazioni eliminate con successo`, 'success');
        setBulkSelection([]);
        await loadCertificazioni();
        calculateStatistics();
      } catch (error) {
        showSnackbar('Errore nell\'eliminazione delle certificazioni', 'error');
      } finally {
        setOperationInProgress(false);
      }
    }
  };

  const handleBulkExport = async () => {
    if (bulkSelection.length === 0) {
      showSnackbar('Seleziona almeno un elemento', 'warning');
      return;
    }

    try {
      setOperationInProgress(true);
      // Implementa export bulk
      const selectedCerts = certificazioni.filter(cert =>
        bulkSelection.includes(cert.id_certificazione)
      );

      // Crea CSV
      const csvContent = generateCSV(selectedCerts);
      downloadCSV(csvContent, `certificazioni_${new Date().toISOString().split('T')[0]}.csv`);

      showSnackbar(`${bulkSelection.length} certificazioni esportate`, 'success');
    } catch (error) {
      showSnackbar('Errore nell\'esportazione', 'error');
    } finally {
      setOperationInProgress(false);
    }
  };

  // Funzioni di export
  const generateCSV = (data) => {
    const headers = ['ID Cavo', 'Numero Certificato', 'Data', 'Operatore', 'Strumento', 'Lunghezza', 'Isolamento', 'Risultato'];
    const rows = data.map(cert => [
      cert.id_cavo,
      cert.numero_certificato,
      new Date(cert.data_certificazione).toLocaleDateString(),
      cert.operatore,
      cert.strumento,
      cert.lunghezza_misurata,
      cert.valore_isolamento,
      cert.risultato_finale
    ]);

    return [headers, ...rows].map(row => row.join(',')).join('\n');
  };

  const downloadCSV = (content, filename) => {
    const blob = new Blob([content], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    if (link.download !== undefined) {
      const url = URL.createObjectURL(blob);
      link.setAttribute('href', url);
      link.setAttribute('download', filename);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  };

  const handleExportAll = () => {
    const csvContent = generateCSV(filteredCertificazioni);
    downloadCSV(csvContent, `tutte_certificazioni_${new Date().toISOString().split('T')[0]}.csv`);
    showSnackbar('Esportazione completata', 'success');
  };

  // Espone metodi tramite ref
  useImperativeHandle(ref, () => ({
    handleOptionSelect: (option) => {
      if (option === 'creaCertificazione') {
        openCreateDialog();
      } else if (option === 'visualizzaCertificazioni') {
        setActiveTab(1);
      }
    }
  }));

  // Calcola elementi per paginazione
  const getCurrentPageItems = (items) => {
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    return items.slice(startIndex, endIndex);
  };

  const getTotalPages = (items) => Math.ceil(items.length / itemsPerPage);

  // Ottieni opzioni uniche per filtri
  const getUniqueValues = (array, field) => {
    return [...new Set(array.map(item => item[field]).filter(Boolean))];
  };

  // Componente Dashboard con statistiche
  const renderDashboard = () => (
    <Grid container spacing={3} sx={{ mb: 3 }}>
      <Grid item xs={12} md={2}>
        <Card sx={{ background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)', color: 'white' }}>
          <CardContent sx={{ textAlign: 'center', py: 2 }}>
            <CableIcon sx={{ fontSize: 40, mb: 1 }} />
            <Typography variant="h4" fontWeight="bold">
              {statistics.totaleCavi}
            </Typography>
            <Typography variant="body2">
              Totale Cavi
            </Typography>
          </CardContent>
        </Card>
      </Grid>

      <Grid item xs={12} md={2}>
        <Card sx={{ background: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)', color: 'white' }}>
          <CardContent sx={{ textAlign: 'center', py: 2 }}>
            <CheckIcon sx={{ fontSize: 40, mb: 1 }} />
            <Typography variant="h4" fontWeight="bold">
              {statistics.caviCertificati}
            </Typography>
            <Typography variant="body2">
              Certificati
            </Typography>
          </CardContent>
        </Card>
      </Grid>

      <Grid item xs={12} md={2}>
        <Card sx={{ background: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)', color: 'white' }}>
          <CardContent sx={{ textAlign: 'center', py: 2 }}>
            <WarningIcon sx={{ fontSize: 40, mb: 1 }} />
            <Typography variant="h4" fontWeight="bold">
              {statistics.caviNonCertificati}
            </Typography>
            <Typography variant="body2">
              Da Certificare
            </Typography>
          </CardContent>
        </Card>
      </Grid>

      <Grid item xs={12} md={2}>
        <Card sx={{ background: 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)', color: 'white' }}>
          <CardContent sx={{ textAlign: 'center', py: 2 }}>
            <ReportIcon sx={{ fontSize: 40, mb: 1 }} />
            <Typography variant="h4" fontWeight="bold">
              {statistics.percentualeCompletamento}%
            </Typography>
            <Typography variant="body2">
              Completamento
            </Typography>
          </CardContent>
        </Card>
      </Grid>

      <Grid item xs={12} md={2}>
        <Card sx={{ background: 'linear-gradient(135deg, #fa709a 0%, #fee140 100%)', color: 'white' }}>
          <CardContent sx={{ textAlign: 'center', py: 2 }}>
            <ScheduleIcon sx={{ fontSize: 40, mb: 1 }} />
            <Typography variant="h4" fontWeight="bold">
              {statistics.certificazioniOggi}
            </Typography>
            <Typography variant="body2">
              Oggi
            </Typography>
          </CardContent>
        </Card>
      </Grid>

      <Grid item xs={12} md={2}>
        <Card sx={{ background: 'linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)', color: '#333' }}>
          <CardContent sx={{ textAlign: 'center', py: 2 }}>
            <ScienceIcon sx={{ fontSize: 40, mb: 1 }} />
            <Typography variant="h4" fontWeight="bold">
              {statistics.certificazioniSettimana}
            </Typography>
            <Typography variant="body2">
              Questa Settimana
            </Typography>
          </CardContent>
        </Card>
      </Grid>
    </Grid>
  );

  // Componente barra di ricerca avanzata
  const renderSearchAndFilters = () => (
    <Paper sx={{ p: 2, mb: 3 }}>
      <Grid container spacing={2} alignItems="center">
        <Grid item xs={12} md={4}>
          <TextField
            fullWidth
            placeholder="Cerca cavi, certificazioni, operatori..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <SearchIcon />
                </InputAdornment>
              ),
              endAdornment: searchTerm && (
                <InputAdornment position="end">
                  <IconButton onClick={() => setSearchTerm('')} size="small">
                    <ClearIcon />
                  </IconButton>
                </InputAdornment>
              )
            }}
          />
        </Grid>

        <Grid item xs={12} md={2}>
          <Button
            fullWidth
            variant="outlined"
            startIcon={<FilterIcon />}
            onClick={() => setAdvancedFiltersOpen(!advancedFiltersOpen)}
            color={Object.values(filters).some(f => f) ? 'primary' : 'inherit'}
          >
            Filtri {Object.values(filters).filter(f => f).length > 0 && `(${Object.values(filters).filter(f => f).length})`}
          </Button>
        </Grid>

        <Grid item xs={12} md={2}>
          <Button
            fullWidth
            variant="outlined"
            startIcon={bulkMode ? <ClearIcon /> : <CheckIcon />}
            onClick={toggleBulkMode}
            color={bulkMode ? 'secondary' : 'inherit'}
          >
            {bulkMode ? 'Esci' : 'Selezione'}
          </Button>
        </Grid>

        <Grid item xs={12} md={2}>
          <Button
            fullWidth
            variant="outlined"
            startIcon={<ExportIcon />}
            onClick={handleExportAll}
            disabled={filteredCertificazioni.length === 0}
          >
            Esporta Tutto
          </Button>
        </Grid>

        <Grid item xs={12} md={2}>
          <Button
            fullWidth
            variant="contained"
            startIcon={<AddIcon />}
            onClick={openCreateDialog}
          >
            Nuova Certificazione
          </Button>
        </Grid>
      </Grid>

      {/* Filtri avanzati */}
      <Collapse in={advancedFiltersOpen}>
        <Divider sx={{ my: 2 }} />
        <Grid container spacing={2}>
          <Grid item xs={12} md={3}>
            <FormControl fullWidth size="small">
              <InputLabel>Operatore</InputLabel>
              <Select
                value={filters.operatore}
                onChange={(e) => setFilters({...filters, operatore: e.target.value})}
              >
                <MenuItem value="">Tutti</MenuItem>
                {[...new Set(certificazioni.map(c => c.operatore))].map(op => (
                  <MenuItem key={op} value={op}>{op}</MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>

          <Grid item xs={12} md={3}>
            <FormControl fullWidth size="small">
              <InputLabel>Strumento</InputLabel>
              <Select
                value={filters.strumento}
                onChange={(e) => setFilters({...filters, strumento: e.target.value})}
              >
                <MenuItem value="">Tutti</MenuItem>
                {strumenti.map(str => (
                  <MenuItem key={str.id} value={str.nome}>{str.nome}</MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>

          <Grid item xs={12} md={3}>
            <FormControl fullWidth size="small">
              <InputLabel>Risultato Test</InputLabel>
              <Select
                value={filters.risultatoTest}
                onChange={(e) => setFilters({...filters, risultatoTest: e.target.value})}
              >
                <MenuItem value="">Tutti</MenuItem>
                <MenuItem value="CONFORME">Conforme</MenuItem>
                <MenuItem value="NON_CONFORME">Non Conforme</MenuItem>
                <MenuItem value="DA_VERIFICARE">Da Verificare</MenuItem>
              </Select>
            </FormControl>
          </Grid>

          <Grid item xs={12} md={3}>
            <TextField
              fullWidth
              size="small"
              label="Isolamento Min (MΩ)"
              type="number"
              value={filters.valoreIsolamento}
              onChange={(e) => setFilters({...filters, valoreIsolamento: e.target.value})}
            />
          </Grid>

          <Grid item xs={12} md={3}>
            <TextField
              fullWidth
              size="small"
              label="Data Inizio"
              type="date"
              value={filters.dataInizio}
              onChange={(e) => setFilters({...filters, dataInizio: e.target.value})}
              InputLabelProps={{ shrink: true }}
            />
          </Grid>

          <Grid item xs={12} md={3}>
            <TextField
              fullWidth
              size="small"
              label="Data Fine"
              type="date"
              value={filters.dataFine}
              onChange={(e) => setFilters({...filters, dataFine: e.target.value})}
              InputLabelProps={{ shrink: true }}
            />
          </Grid>

          <Grid item xs={12} md={6}>
            <Stack direction="row" spacing={1}>
              <Button
                variant="outlined"
                size="small"
                onClick={() => setFilters({
                  stato: '', tipologia: '', operatore: '', dataInizio: '',
                  dataFine: '', valoreIsolamento: '', risultatoTest: '', strumento: ''
                })}
              >
                Pulisci Filtri
              </Button>
            </Stack>
          </Grid>
        </Grid>
      </Collapse>

      {/* Barra azioni bulk */}
      {bulkMode && bulkSelection.length > 0 && (
        <>
          <Divider sx={{ my: 2 }} />
          <Stack direction="row" spacing={2} alignItems="center">
            <Typography variant="body2">
              {bulkSelection.length} elementi selezionati
            </Typography>
            <Button
              size="small"
              variant="outlined"
              onClick={selectAllItems}
            >
              Seleziona Tutto
            </Button>
            <Button
              size="small"
              variant="outlined"
              onClick={clearSelection}
            >
              Deseleziona
            </Button>
            <Button
              size="small"
              variant="outlined"
              startIcon={<ExportIcon />}
              onClick={handleBulkExport}
            >
              Esporta Selezionati
            </Button>
            <Button
              size="small"
              variant="outlined"
              color="error"
              startIcon={<DeleteIcon />}
              onClick={handleBulkDelete}
            >
              Elimina Selezionati
            </Button>
          </Stack>
        </>
      )}
    </Paper>
  );

  // Renderizza la tabella dei cavi
  const renderCaviTable = () => {
    const currentItems = getCurrentPageItems(filteredCavi);

    if (filteredCavi.length === 0) {
      return (
        <Alert severity="info">
          {searchTerm || filters.stato || filters.tipologia
            ? 'Nessun cavo trovato con i filtri applicati'
            : 'Nessun cavo disponibile'}
        </Alert>
      );
    }

    return (
      <>
        <TableContainer component={Paper}>
          <Table size="small">
            <TableHead>
              <TableRow>
                <TableCell>ID Cavo</TableCell>
                <TableCell>Tipologia</TableCell>
                <TableCell>Sezione</TableCell>
                <TableCell>Partenza</TableCell>
                <TableCell>Arrivo</TableCell>
                <TableCell>Metri</TableCell>
                <TableCell>Stato</TableCell>
                <TableCell>Certificato</TableCell>
                <TableCell>Azioni</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {currentItems.map((cavo) => {
                const isCertificato = certificazioni.some(cert => cert.id_cavo === cavo.id_cavo);
                return (
                  <TableRow key={cavo.id_cavo}>
                    <TableCell>
                      <Typography variant="body2" fontWeight="medium">
                        {cavo.id_cavo}
                      </Typography>
                    </TableCell>
                    <TableCell>{cavo.tipologia}</TableCell>
                    <TableCell>{cavo.sezione}</TableCell>
                    <TableCell>{cavo.ubicazione_partenza}</TableCell>
                    <TableCell>{cavo.ubicazione_arrivo}</TableCell>
                    <TableCell>{cavo.metratura_reale || cavo.metri_teorici} m</TableCell>
                    <TableCell>
                      <Chip
                        size="small"
                        label={cavo.stato_installazione}
                        color={cavo.stato_installazione === 'INSTALLATO' ? 'success' : 'default'}
                      />
                    </TableCell>
                    <TableCell>
                      {isCertificato ? (
                        <Chip
                          size="small"
                          icon={<CheckIcon />}
                          label="Certificato"
                          color="success"
                        />
                      ) : (
                        <Chip
                          size="small"
                          icon={<WarningIcon />}
                          label="Non certificato"
                          color="warning"
                        />
                      )}
                    </TableCell>
                    <TableCell>
                      <Tooltip title="Crea certificazione">
                        <IconButton
                          size="small"
                          onClick={() => {
                            handleCavoSelect(cavo);
                            openCreateDialog();
                          }}
                          disabled={isCertificato}
                        >
                          <AddIcon />
                        </IconButton>
                      </Tooltip>
                    </TableCell>
                  </TableRow>
                );
              })}
            </TableBody>
          </Table>
        </TableContainer>

        {getTotalPages(filteredCavi) > 1 && (
          <Box sx={{ display: 'flex', justifyContent: 'center', mt: 2 }}>
            <Pagination
              count={getTotalPages(filteredCavi)}
              page={currentPage}
              onChange={(event, value) => setCurrentPage(value)}
              color="primary"
            />
          </Box>
        )}
      </>
    );
  };

  // Renderizza la tabella delle certificazioni
  const renderCertificazioniTable = () => {
    const currentItems = getCurrentPageItems(filteredCertificazioni);

    if (filteredCertificazioni.length === 0) {
      return (
        <Alert severity="info">
          {searchTerm || filters.operatore
            ? 'Nessuna certificazione trovata con i filtri applicati'
            : 'Nessuna certificazione disponibile'}
        </Alert>
      );
    }

    return (
      <>
        <TableContainer component={Paper}>
          <Table size="small">
            <TableHead>
              <TableRow>
                {bulkMode && (
                  <TableCell padding="checkbox">
                    <IconButton
                      size="small"
                      onClick={bulkSelection.length === filteredCertificazioni.length ? clearSelection : selectAllItems}
                    >
                      {bulkSelection.length === filteredCertificazioni.length ? <ClearIcon /> : <CheckIcon />}
                    </IconButton>
                  </TableCell>
                )}
                <TableCell>
                  <Stack direction="row" alignItems="center" spacing={1}>
                    <Typography variant="body2" fontWeight="bold">N° Certificato</Typography>
                    <IconButton size="small" onClick={() => {
                      setSortBy('numero_certificato');
                      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
                    }}>
                      {sortBy === 'numero_certificato' ? (sortOrder === 'asc' ? <ExpandLessIcon /> : <ExpandMoreIcon />) : <ExpandMoreIcon />}
                    </IconButton>
                  </Stack>
                </TableCell>
                <TableCell>ID Cavo</TableCell>
                <TableCell>
                  <Stack direction="row" alignItems="center" spacing={1}>
                    <Typography variant="body2" fontWeight="bold">Data</Typography>
                    <IconButton size="small" onClick={() => {
                      setSortBy('data_certificazione');
                      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
                    }}>
                      {sortBy === 'data_certificazione' ? (sortOrder === 'asc' ? <ExpandLessIcon /> : <ExpandMoreIcon />) : <ExpandMoreIcon />}
                    </IconButton>
                  </Stack>
                </TableCell>
                <TableCell>Operatore</TableCell>
                <TableCell>Strumento</TableCell>
                <TableCell>Lunghezza</TableCell>
                <TableCell>Isolamento</TableCell>
                <TableCell>Risultato</TableCell>
                <TableCell>Azioni</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {currentItems.map((cert) => (
                <TableRow
                  key={cert.id_certificazione}
                  selected={bulkSelection.includes(cert.id_certificazione)}
                  hover
                >
                  {bulkMode && (
                    <TableCell padding="checkbox">
                      <IconButton
                        size="small"
                        onClick={() => toggleItemSelection(cert.id_certificazione)}
                        color={bulkSelection.includes(cert.id_certificazione) ? 'primary' : 'default'}
                      >
                        {bulkSelection.includes(cert.id_certificazione) ? <CheckIcon /> : <AddIcon />}
                      </IconButton>
                    </TableCell>
                  )}
                  <TableCell>
                    <Typography variant="body2" fontWeight="medium">
                      {cert.numero_certificato}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Chip size="small" label={cert.id_cavo} variant="outlined" />
                  </TableCell>
                  <TableCell>{new Date(cert.data_certificazione).toLocaleDateString()}</TableCell>
                  <TableCell>
                    <Stack direction="row" alignItems="center" spacing={1}>
                      <PersonIcon fontSize="small" />
                      <Typography variant="body2">{cert.operatore || cert.id_operatore}</Typography>
                    </Stack>
                  </TableCell>
                  <TableCell>
                    <Typography variant="body2">
                      {cert.id_strumento ?
                        (() => {
                          const strumento = strumenti.find(s => s.id_strumento === cert.id_strumento);
                          return strumento ? `${strumento.nome} - ${strumento.marca}` : 'Strumento non trovato';
                        })()
                        : (cert.strumento_utilizzato || 'N/A')
                      }
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Typography variant="body2">{cert.lunghezza_misurata} m</Typography>
                  </TableCell>
                  <TableCell>
                    <Chip
                      size="small"
                      label={`${cert.valore_isolamento} MΩ`}
                      color={parseFloat(cert.valore_isolamento) >= 500 ? 'success' : 'warning'}
                      icon={parseFloat(cert.valore_isolamento) >= 500 ? <CheckIcon /> : <WarningIcon />}
                    />
                  </TableCell>
                  <TableCell>
                    <Chip
                      size="small"
                      label={cert.risultato_finale || 'CONFORME'}
                      color={cert.risultato_finale === 'CONFORME' ? 'success' : cert.risultato_finale === 'NON_CONFORME' ? 'error' : 'warning'}
                    />
                  </TableCell>
                  <TableCell>
                    <Stack direction="row" spacing={0.5}>
                      <Tooltip title="Visualizza dettagli">
                        <IconButton
                          size="small"
                          onClick={() => {
                            setSelectedItem(cert);
                            setDialogType('view');
                            setOpenDialog(true);
                          }}
                        >
                          <ViewIcon />
                        </IconButton>
                      </Tooltip>
                      <Tooltip title="Genera PDF">
                        <IconButton
                          size="small"
                          onClick={() => handleGeneratePdf(cert)}
                          disabled={operationInProgress}
                        >
                          <PdfIcon />
                        </IconButton>
                      </Tooltip>
                      <Tooltip title="Elimina">
                        <IconButton
                          size="small"
                          color="error"
                          onClick={() => handleDeleteCertificazione(cert)}
                          disabled={operationInProgress}
                        >
                          <DeleteIcon />
                        </IconButton>
                      </Tooltip>
                    </Stack>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>

        {getTotalPages(filteredCertificazioni) > 1 && (
          <Box sx={{ display: 'flex', justifyContent: 'center', mt: 2 }}>
            <Pagination
              count={getTotalPages(filteredCertificazioni)}
              page={currentPage}
              onChange={(event, value) => setCurrentPage(value)}
              color="primary"
            />
          </Box>
        )}
      </>
    );
  };

  // Renderizza il dialog per creare/modificare certificazione
  const renderCertificazioneDialog = () => {
    if (dialogType !== 'create' && dialogType !== 'edit') return null;

    return (
      <Dialog open={openDialog} onClose={closeDialog} maxWidth="md" fullWidth>
        <DialogTitle>
          {dialogType === 'create' ? 'Nuova Certificazione' : 'Modifica Certificazione'}
        </DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12} md={6}>
              <Autocomplete
                options={cavi.filter(cavo =>
                  !certificazioni.some(cert => cert.id_cavo === cavo.id_cavo) ||
                  cavo.id_cavo === formData.id_cavo
                )}
                getOptionLabel={(option) => `${option.id_cavo} - ${option.tipologia}`}
                value={cavi.find(c => c.id_cavo === formData.id_cavo) || null}
                onChange={(event, newValue) => {
                  if (newValue) {
                    handleCavoSelect(newValue);
                  } else {
                    setFormData(prev => ({ ...prev, id_cavo: '', lunghezza_misurata: '' }));
                  }
                }}
                renderInput={(params) => (
                  <TextField
                    {...params}
                    label="Cavo *"
                    placeholder="Seleziona un cavo"
                    required
                  />
                )}
                renderOption={(props, option) => (
                  <Box component="li" {...props}>
                    <Box>
                      <Typography variant="body2" fontWeight="medium">
                        {option.id_cavo}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        {option.tipologia} - {option.ubicazione_partenza} → {option.ubicazione_arrivo}
                      </Typography>
                    </Box>
                  </Box>
                )}
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Operatore *"
                value={formData.id_operatore}
                onChange={(e) => handleFormChange('id_operatore', e.target.value)}
                required
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <FormControl fullWidth required>
                <InputLabel>Strumento *</InputLabel>
                <Select
                  value={formData.id_strumento}
                  onChange={(e) => handleFormChange('id_strumento', e.target.value)}
                  label="Strumento *"
                >
                  {strumenti.map((strumento) => (
                    <MenuItem key={strumento.id_strumento} value={strumento.id_strumento}>
                      {strumento.nome} - {strumento.marca} {strumento.modello} (S/N: {strumento.numero_serie})
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Lunghezza Misurata (m) *"
                type="number"
                value={formData.lunghezza_misurata}
                onChange={(e) => handleFormChange('lunghezza_misurata', e.target.value)}
                required
              />
            </Grid>

            <Grid item xs={12} md={4}>
              <FormControl fullWidth>
                <InputLabel>Continuità</InputLabel>
                <Select
                  value={formData.valore_continuita}
                  onChange={(e) => handleFormChange('valore_continuita', e.target.value)}
                  label="Continuità"
                >
                  <MenuItem value="OK">OK</MenuItem>
                  <MenuItem value="NOK">NOK</MenuItem>
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                label="Isolamento (MΩ) *"
                type="number"
                value={formData.valore_isolamento}
                onChange={(e) => handleFormChange('valore_isolamento', e.target.value)}
                required
                helperText="Valore minimo consigliato: 500 MΩ"
              />
            </Grid>

            <Grid item xs={12} md={4}>
              <FormControl fullWidth>
                <InputLabel>Resistenza</InputLabel>
                <Select
                  value={formData.valore_resistenza}
                  onChange={(e) => handleFormChange('valore_resistenza', e.target.value)}
                  label="Resistenza"
                >
                  <MenuItem value="OK">OK</MenuItem>
                  <MenuItem value="NOK">NOK</MenuItem>
                </Select>
              </FormControl>
            </Grid>

            {/* Campi avanzati */}
            <Grid item xs={12}>
              <Divider sx={{ my: 2 }}>
                <Typography variant="body2" color="text.secondary">
                  Parametri Ambientali e Test Avanzati
                </Typography>
              </Divider>
            </Grid>

            <Grid item xs={12} md={3}>
              <TextField
                fullWidth
                label="Temperatura Ambiente (°C)"
                type="number"
                value={formData.temperatura_ambiente}
                onChange={(e) => handleFormChange('temperatura_ambiente', e.target.value)}
                helperText="Temperatura durante il test"
              />
            </Grid>

            <Grid item xs={12} md={3}>
              <TextField
                fullWidth
                label="Umidità (%)"
                type="number"
                value={formData.umidita}
                onChange={(e) => handleFormChange('umidita', e.target.value)}
                helperText="Umidità relativa"
              />
            </Grid>

            <Grid item xs={12} md={3}>
              <TextField
                fullWidth
                label="Tensione di Prova (V)"
                type="number"
                value={formData.tensione_prova}
                onChange={(e) => handleFormChange('tensione_prova', e.target.value)}
                helperText="Tensione applicata per il test"
              />
            </Grid>

            <Grid item xs={12} md={3}>
              <TextField
                fullWidth
                label="Durata Prova (min)"
                type="number"
                value={formData.durata_prova}
                onChange={(e) => handleFormChange('durata_prova', e.target.value)}
                helperText="Durata del test in minuti"
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <FormControl fullWidth>
                <InputLabel>Risultato Finale</InputLabel>
                <Select
                  value={formData.risultato_finale}
                  onChange={(e) => handleFormChange('risultato_finale', e.target.value)}
                  label="Risultato Finale"
                >
                  <MenuItem value="CONFORME">
                    <Stack direction="row" alignItems="center" spacing={1}>
                      <CheckIcon color="success" />
                      <Typography>Conforme</Typography>
                    </Stack>
                  </MenuItem>
                  <MenuItem value="NON_CONFORME">
                    <Stack direction="row" alignItems="center" spacing={1}>
                      <ErrorIcon color="error" />
                      <Typography>Non Conforme</Typography>
                    </Stack>
                  </MenuItem>
                  <MenuItem value="DA_VERIFICARE">
                    <Stack direction="row" alignItems="center" spacing={1}>
                      <WarningIcon color="warning" />
                      <Typography>Da Verificare</Typography>
                    </Stack>
                  </MenuItem>
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Note"
                multiline
                rows={3}
                value={formData.note}
                onChange={(e) => handleFormChange('note', e.target.value)}
                placeholder="Inserisci eventuali note, osservazioni o anomalie riscontrate durante il test..."
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={closeDialog}>Annulla</Button>
          <Button
            onClick={handleCreateCertificazione}
            variant="contained"
            disabled={loading || !formData.id_cavo || !formData.id_operatore || !formData.id_strumento || !formData.valore_isolamento}
            startIcon={loading ? <CircularProgress size={20} /> : <SaveIcon />}
          >
            {dialogType === 'create' ? 'Crea Certificazione' : 'Salva Modifiche'}
          </Button>
        </DialogActions>
      </Dialog>
    );
  };

  // Renderizza il dialog di visualizzazione dettagli
  const renderViewDialog = () => {
    if (dialogType !== 'view' || !selectedItem) return null;

    return (
      <Dialog open={openDialog} onClose={closeDialog} maxWidth="md" fullWidth>
        <DialogTitle>
          Dettagli Certificazione - {selectedItem.numero_certificato}
        </DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12} md={6}>
              <Card variant="outlined">
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Informazioni Cavo
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    ID Cavo: <strong>{selectedItem.id_cavo}</strong>
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Lunghezza Misurata: <strong>{selectedItem.lunghezza_misurata} m</strong>
                  </Typography>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} md={6}>
              <Card variant="outlined">
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Informazioni Certificazione
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Numero: <strong>{selectedItem.numero_certificato}</strong>
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Data: <strong>{new Date(selectedItem.data_certificazione).toLocaleDateString()}</strong>
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Operatore: <strong>{selectedItem.operatore || selectedItem.id_operatore}</strong>
                  </Typography>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12}>
              <Card variant="outlined">
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Risultati Test
                  </Typography>
                  <Grid container spacing={2}>
                    <Grid item xs={4}>
                      <Typography variant="body2" color="text.secondary">
                        Continuità
                      </Typography>
                      <Chip
                        size="small"
                        label={selectedItem.valore_continuita}
                        color={selectedItem.valore_continuita === 'OK' ? 'success' : 'error'}
                      />
                    </Grid>
                    <Grid item xs={4}>
                      <Typography variant="body2" color="text.secondary">
                        Isolamento
                      </Typography>
                      <Chip
                        size="small"
                        label={`${selectedItem.valore_isolamento} MΩ`}
                        color={parseFloat(selectedItem.valore_isolamento) >= 500 ? 'success' : 'warning'}
                      />
                    </Grid>
                    <Grid item xs={4}>
                      <Typography variant="body2" color="text.secondary">
                        Resistenza
                      </Typography>
                      <Chip
                        size="small"
                        label={selectedItem.valore_resistenza}
                        color={selectedItem.valore_resistenza === 'OK' ? 'success' : 'error'}
                      />
                    </Grid>
                  </Grid>
                </CardContent>
              </Card>
            </Grid>

            {selectedItem.note && (
              <Grid item xs={12}>
                <Card variant="outlined">
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      Note
                    </Typography>
                    <Typography variant="body2">
                      {selectedItem.note}
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
            )}
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={closeDialog}>Chiudi</Button>
          <Button
            onClick={() => handleGeneratePdf(selectedItem)}
            variant="contained"
            startIcon={<PdfIcon />}
            disabled={loading}
          >
            Genera PDF
          </Button>
        </DialogActions>
      </Dialog>
    );
  };

  // Renderizza le statistiche
  const renderStats = () => {
    const totalCavi = cavi.length;
    const caviInstallati = cavi.filter(c => c.stato_installazione === 'INSTALLATO').length;
    const caviCertificati = certificazioni.length;
    const percentualeCertificazione = totalCavi > 0 ? Math.round((caviCertificati / caviInstallati) * 100) : 0;

    return (
      <Grid container spacing={2} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="text.secondary" gutterBottom>
                Cavi Totali
              </Typography>
              <Typography variant="h4">
                {totalCavi}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="text.secondary" gutterBottom>
                Cavi Installati
              </Typography>
              <Typography variant="h4">
                {caviInstallati}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="text.secondary" gutterBottom>
                Certificazioni
              </Typography>
              <Typography variant="h4">
                {caviCertificati}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="text.secondary" gutterBottom>
                % Certificazione
              </Typography>
              <Typography variant="h4" color={percentualeCertificazione >= 80 ? 'success.main' : 'warning.main'}>
                {percentualeCertificazione}%
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    );
  };

  return (
    <Container maxWidth="xl" sx={{ py: 3 }}>
      {/* Header con titolo e azioni rapide */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom fontWeight="bold">
          🔌 Sistema di Certificazione Cavi
        </Typography>
        <Typography variant="body1" color="text.secondary" sx={{ mb: 2 }}>
          Gestione completa delle certificazioni elettriche secondo standard CEI 64-8
        </Typography>
      </Box>

      {/* Dashboard con statistiche */}
      {renderDashboard()}

      {/* Progress bar per operazioni in corso */}
      {(loading || operationInProgress) && (
        <Box sx={{ mb: 2 }}>
          <LinearProgress />
          {progress > 0 && (
            <Typography variant="caption" color="text.secondary" sx={{ mt: 1 }}>
              Caricamento... {progress}%
            </Typography>
          )}
        </Box>
      )}

      {/* Tabs per navigazione */}
      <Paper sx={{ mb: 3 }}>
        <Tabs
          value={activeTab}
          onChange={handleTabChange}
          indicatorColor="primary"
          textColor="primary"
          variant="fullWidth"
        >
          <Tab
            label={
              <Stack direction="row" alignItems="center" spacing={1}>
                <CableIcon />
                <Typography>Cavi ({filteredCavi.length})</Typography>
                {statistics.caviNonCertificati > 0 && (
                  <Badge badgeContent={statistics.caviNonCertificati} color="warning" />
                )}
              </Stack>
            }
          />
          <Tab
            label={
              <Stack direction="row" alignItems="center" spacing={1}>
                <ScienceIcon />
                <Typography>Certificazioni ({filteredCertificazioni.length})</Typography>
                {statistics.certificazioniOggi > 0 && (
                  <Badge badgeContent={statistics.certificazioniOggi} color="success" />
                )}
              </Stack>
            }
          />
        </Tabs>
      </Paper>

      {/* Barra di ricerca e filtri avanzati */}
      {renderSearchAndFilters()}

      {/* Contenuto delle tabs */}
      {!loading && activeTab === 0 && renderCaviTable()}
      {!loading && activeTab === 1 && renderCertificazioniTable()}

      {/* Dialogs */}
      {renderCertificazioneDialog()}
      {renderViewDialog()}

      {/* Snackbar per notifiche */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={closeSnackbar}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert onClose={closeSnackbar} severity={snackbar.severity} sx={{ width: '100%' }}>
          {snackbar.message}
        </Alert>
      </Snackbar>

      {/* Speed Dial per azioni rapide */}
      <SpeedDial
        ariaLabel="Azioni rapide"
        sx={{ position: 'fixed', bottom: 16, right: 16 }}
        icon={<SpeedDialIcon />}
      >
        <SpeedDialAction
          icon={<AddIcon />}
          tooltipTitle="Nuova Certificazione"
          onClick={openCreateDialog}
        />
        <SpeedDialAction
          icon={<ExportIcon />}
          tooltipTitle="Esporta Tutto"
          onClick={handleExportAll}
        />
        <SpeedDialAction
          icon={<RefreshIcon />}
          tooltipTitle="Aggiorna Dati"
          onClick={loadInitialData}
        />
        <SpeedDialAction
          icon={<ReportIcon />}
          tooltipTitle="Report Avanzato"
          onClick={() => showSnackbar('Funzionalità in sviluppo', 'info')}
        />
      </SpeedDial>
    </Container>
  );
});

export default CertificazioneCaviImproved;
