from typing import Callable, Any
from modules.cantieri_manager_pg import CantieriManager
import os,sys, subprocess
from modules.utenti import Gest<PERSON><PERSON>tenti
from modules.database_pg import Database
import datetime
from datetime import date, timedelta

import logging
from modules.cavi import (
    visualizza_cavi, aggiorna_metri_posati, modifica_cavo_manualmente,
    aggiungi_cavo, elimina_cavo, modifica_bobina_cavo_posato)
from modules.parco_cavi import ParcoCavi
from modules.excel_manager import importa_cavi_da_excel, crea_template_excel, esporta_parco_bobine_excel, crea_template_parco_bobine, importa_parco_bobine_da_excel, esporta_cavi_excel
from modules.reports import (genera_report_avanzamento, genera_report_cavi_per_stato,
                          genera_report_utilizzo_bobine, genera_progress_report,
                          genera_bill_of_quantities, genera_report_posa_per_periodo,
                          genera_report_bobina_specifica)
from modules.certificazione_cavi import menu_gestione_certificazioni
from modules.collegamenti_new import gestisci_collegamenti
from modules.comande_new import (
    crea_comanda, assegna_cavi_a_comanda, ottieni_cavi_comanda,
    aggiorna_dati_posa, aggiorna_dati_collegamento, ottieni_comande_cantiere,
    ottieni_dettagli_comanda, elimina_comanda, cambia_stato_comanda
)

class MenuManager:

    """Classe per gestire funzionalità comuni dei menu"""

    @staticmethod
    def clear_screen():
        """Pulisce lo schermo del terminale."""
        try:
            if os.name == 'nt':  # Windows
                os.system('cls')
            elif os.name == 'posix':  # Unix/Linux/macOS
                os.system('clear')
            else:
                print("Sistema operativo non supportato per la pulizia dello schermo.")
        except Exception as e:
            print(f"Errore durante la pulizia dello schermo: {e}")

    @staticmethod
    def wait_for_input(message: str = "\nPremi INVIO per continuare...") -> None:
        """Attende input dall'utente con un messaggio personalizzabile"""
        try:
            input(message)
        except (KeyboardInterrupt, EOFError):
            # Gestisce Ctrl+C e EOF in modo silenzioso
            print()  # Va a capo
            pass

    @staticmethod
    def confirm_action(message: str, warning: bool = True) -> bool:
        """
        Richiede conferma all'utente per un'azione
        Args:
            message: Messaggio da mostrare
            warning: Se True, aggiunge simbolo di warning
        Returns:
            bool: True se l'utente conferma, False altrimenti
        """
        prefix = "⚠️ " if warning else ""
        response = input(f"\n{prefix}{message} (s/n): ").strip().lower()
        return response == 's'

    @staticmethod
    def handle_menu_action(action: Callable, *args, **kwargs) -> Any:
        """
        Gestisce l'esecuzione di un'azione di menu con gestione errori
        Args:
            action: Funzione da eseguire
            args: Argomenti posizionali
            kwargs: Argomenti nominali
        Returns:
            Any: Risultato dell'azione o None in caso di errore
        """
        try:
            return action(*args, **kwargs)
        except Exception as e:
            print(f"\n❌ Errore: {str(e)}")
            logging.error(f"Errore durante l'esecuzione di {action.__name__}: {str(e)}")
            return None


def menu_amministratore():
    """Menu per l'amministratore."""
    menu = MenuManager()
    db = Database()  # Crea un'istanza di Database
    gestore_utenti = GestoreUtenti(db)  # Passa l'istanza di Database

    while True:
        menu.clear_screen()
        print("\n=== MENU AMMINISTRATORE ===")
        print("1. Visualizza Utenti")
        print("2. Crea Nuovo Utente Standard")  # Modificato per chiarezza
        print("3. Disabilita/Abilita Utente")
        print("4. Elimina Utente")
        print("5. Accedi come Utente")  # Nuova opzione
        print("6. Visualizza Database Raw")
        print("7. Reset Database")  # Nuova opzione
        print("8. Logout")

        scelta = input("\nSeleziona un'opzione: ").strip()

        if scelta == "1":
            utenti = menu.handle_menu_action(gestore_utenti.visualizza_utenti)
            if utenti:
                print("\nLista Utenti:")
                print("-" * 120)  # Aumentato per accomodare la colonna password
                print(f"{'ID':<5} {'Username':<15} {'Password':<20} {'Ruolo':<10} {'Scadenza':<12} {'Stato':<8}")
                print("-" * 120)
                for u in utenti:
                    stato = "✅ Attivo" if u[5] else "❌ Disabilitato"
                    if isinstance(u[4], datetime.date):
                        data_scadenza = u[4].strftime('%Y-%m-%d')
                    else:
                        data_scadenza = 'N/A'

                    password_display = "********"
                    if u[3] == 'owner':
                        password_display = str(u[2])[:20]  # Converti in stringa e limita a 20 caratteri

                    print(f"{u[0]:<5} {u[1]:<15} {password_display:<20} {u[3]:<10} {data_scadenza:<12} {stato:<8}")
            menu.wait_for_input()


        elif scelta == "2":
            username = input("Username: ").strip()
            password = input("Password: ").strip()
            ruolo = "user"

            while True:
                data_scadenza = input("Data scadenza (YYYY-MM-DD) o invio per nessuna: ").strip()
                if not data_scadenza:  # Se non viene inserita alcuna data
                    data_scadenza = None
                    break

                try:
                    # Verifica che la data sia nel formato corretto
                    data = datetime.datetime.strptime(data_scadenza, "%Y-%m-%d")
                    # Verifica che la data non sia nel passato
                    if data.date() < datetime.datetime.now().date():
                        print("❌ La data di scadenza non può essere nel passato")
                        continue
                    break
                except ValueError:
                    print("❌ Formato data non valido. Usa YYYY-MM-DD")
                    continue

            if menu.handle_menu_action(gestore_utenti.aggiungi_utente, username, password, ruolo, data_scadenza):
                print("✅ Utente aggiunto con successo!")
            else:
                print("❌ Errore durante l'aggiunta dell'utente.")
            menu.wait_for_input()

        elif scelta == "3":
            utenti = gestore_utenti.visualizza_utenti()
            if utenti:
                print("\nSeleziona utente da modificare:")
                for u in utenti:
                    # u[5] è 'abilitato' dalla query SQL: SELECT id_utente, username, password, ruolo, data_scadenza, abilitato, created_by
                    stato = "✅ Attivo" if u[5] == 1 else "❌ Disabilitato"
                    print(f"ID: {u[0]} - {u[1]} ({stato})")

                id_utente = input("\nInserisci ID utente: ").strip()
                try:
                    id_utente = int(id_utente)
                    azione = input("Abilitare o disabilitare? (a/d): ").strip().lower()
                    if azione in ['a', 'd']:
                        if menu.handle_menu_action(gestore_utenti.disabilita_abilita_utente,
                                                   id_utente, azione == 'a'):
                            print("✅ Stato utente modificato con successo!")
                        else:
                            print("❌ Errore durante la modifica dello stato!")
                except ValueError:
                    print("❌ ID utente non valido!")
            menu.wait_for_input()

        elif scelta == "4":
            utenti = gestore_utenti.visualizza_utenti()
            if utenti:
                print("\nSeleziona utente da eliminare:")
                for u in utenti:
                    print(f"ID: {u[0]} - {u[1]}")

                id_utente = input("\nInserisci ID utente: ").strip()
                try:
                    id_utente = int(id_utente)
                    if menu.confirm_action(
                            "⚠️ ATTENZIONE: Questa operazione eliminerà definitivamente l'utente e tutti i suoi dati!"):
                        if menu.handle_menu_action(gestore_utenti.elimina_utente, id_utente):
                            print("✅ Utente eliminato con successo!")
                        else:
                            print("❌ Errore durante l'eliminazione!")
                except ValueError:
                    print("❌ ID utente non valido!")

        elif scelta == "5":  # Nuova opzione per accedere come utente
            utenti = gestore_utenti.visualizza_utenti()
            if utenti:
                print("\nSeleziona utente da impersonare:")
                for u in utenti:
                    stato = "✅ Attivo" if u[5] == 1 else "❌ Disabilitato"
                    print(f"ID: {u[0]} - {u[1]} ({stato})")

                id_utente = input("\nInserisci ID utente: ").strip()
                try:
                    id_utente = int(id_utente)
                    # Trova l'username dell'utente selezionato
                    user = next((u for u in utenti if u[0] == id_utente), None)
                    if user:
                        username = user[1]
                        print(f"\n👤 Accesso come: {username}")
                        menu_user(username)
                    else:
                        print("❌ Utente non trovato!")
                except ValueError:
                    print("❌ ID utente non valido!")
                menu.wait_for_input()

        elif scelta == "6":  # Spostato
            menu.handle_menu_action(db.visualizza_database_raw)
            menu.wait_for_input()

        elif scelta == "7":
            print("\n⚠️ ATTENZIONE: Questa operazione è irreversibile!")
            print("Tutti i dati verranno eliminati permanentemente.")
            if input("Digita 'RESET' per confermare: ").strip() == "RESET":
                if db.reset_database():
                    print("\n✅ Database resettato con successo")
                    print("Il programma verrà terminato.")
                    sys.exit(0)
                else:
                    print("\n❌ Reset fallito")
            menu.wait_for_input()

        elif scelta == "8":  # Spostato
            if menu.confirm_action("Sei sicuro di voler uscire?", warning=False):
                print("\nArrivederci! 👋")
                break

        else:
            print("❌ Opzione non valida!")
            menu.wait_for_input()


def menu_user(current_user: str):
    """Menu principale per gli utenti standard."""
    db = Database()  # Inizializza il database
    cantieri_manager = CantieriManager(db, current_user)  # Inizializza il gestore dei cantieri con il database e l'utente corrente
    menu = MenuManager()  # Inizializza il gestore del menu
    gestore_utenti = GestoreUtenti(db)  # Inizializza il gestore degli utenti con il database

    while True:
        menu.clear_screen()  # Pulisce lo schermo
        print(f"\nMENU PRINCIPALE - Utente: {current_user}")
        print("-------------------------")
        print("1. Visualizza i miei cantieri")
        print("2. Gestione cavi del mio cantiere")
        print("3. Elimina cantiere")
        print("4. Esci")

        scelta = input("Seleziona un'opzione: ").strip()  # Legge la scelta dell'utente

        if scelta == "1":
            menu.handle_menu_action(cantieri_manager.visualizza_cantieri)  # Visualizza i cantieri dell'utente
            menu.wait_for_input()  # Attende che l'utente prema INVIO per continuare
        elif scelta == "2":
            id_cantiere, codice_univoco, nome_cantiere = menu.handle_menu_action(
                cantieri_manager.seleziona_cantiere) or (None, None, None)
            if id_cantiere:
                menu_gestione_cavi(id_cantiere, nome_cantiere)  # Gestisce i cavi del cantiere selezionato
            else:
                print("\n❌ Nessun cantiere selezionato.")
                menu.wait_for_input()  # Attende che l'utente prema INVIO per continuare
        elif scelta == "3":
            cantieri_manager.visualizza_cantieri()
            id_cantiere = input("\nInserisci l'ID del cantiere da eliminare: ").strip()
            if id_cantiere:
                if menu.confirm_action(
                        f"ATTENZIONE: Sei sicuro di voler eliminare il cantiere {id_cantiere} e tutti i suoi dati?"):
                    if menu.handle_menu_action(cantieri_manager.elimina_cantiere, id_cantiere):
                        print(f"\n✅ Cantiere {id_cantiere} eliminato con successo!")
                    else:
                        print(f"\n❌ Errore durante l'eliminazione del cantiere {id_cantiere}!")
                menu.wait_for_input()
        elif scelta == "4":
            print("\nArrivederci! 👋")
            break  # Esce dal ciclo e termina la funzione
        else:
            print("\n❌ Opzione non valida!")
            menu.wait_for_input()  # Attende che l'utente prema INVIO per continuare


def menu_gestione_cavi(id_cantiere: str, nome_cantiere: str, is_cantiere_user: bool = True):
    """Menu per la gestione dei cavi di un cantiere specifico."""
    menu = MenuManager()
    while True:
        print("\n" + "=" * 60)
        print("🔧 MENU PRINCIPALE CANTIERE")
        print("=" * 60)
        print(f"\nCantiere attuale: {nome_cantiere} (ID: {id_cantiere})")
        print("\nOpzioni disponibili:")
        print(" 1. Visualizza cavi")
        print(" 2. Posa cavi e collegamenti")
        print(" 3. Parco Cavi")
        print(" 4. Gestione Excel")
        print(" 5. Report")
        print(" 6. Certificazione Cavi")
        print(" 7. Gestione Comande")
        if is_cantiere_user:
            print(" 8. Logout")
        else:
            print(" 8. Torna al menu cantieri")
        try:
            scelta = input("\nSeleziona un'opzione (1-8): ").strip()
            if scelta == "1":
                menu.handle_menu_action(visualizza_cavi, id_cantiere)
                menu.wait_for_input()
            elif scelta == "2":
                gestisci_cavi(id_cantiere)
            elif scelta == "3":
                gestisci_parco_cavi(id_cantiere)
            elif scelta == "4":
                gestisci_excel(id_cantiere)
            elif scelta == "5":
                gestisci_report(id_cantiere)
            elif scelta == "6":
                gestisci_test_collegamenti(id_cantiere)
            elif scelta == "7":
                # Converti l'ID cantiere in intero se è una stringa
                id_cantiere_int = int(id_cantiere) if isinstance(id_cantiere, str) else id_cantiere
                menu_comande(id_cantiere_int)
            elif scelta == "8":
                if is_cantiere_user:
                    print("\nLogout effettuato. 👋")
                    break  # Logout per utenti cantiere
                else:
                    return  # Torna al menu cantieri per utenti standard
            else:
                print("\n❌ Opzione non valida!")
                menu.wait_for_input()
        except Exception as e:
            print(f"\n❌ Errore: {e}")
            menu.wait_for_input()

def gestisci_parco_cavi(id_cantiere: str):
    """Gestione del parco cavi per un cantiere specifico."""
    menu = MenuManager()
    parco_cavi = ParcoCavi()

    while True:
        # Intestazione del menu
        print("\n" + "=" * 60)
        print("🌀 GESTIONE PARCO CAVI - Cantiere {}".format(id_cantiere))
        print("=" * 60)
        print("\nOpzioni disponibili:")
        print(" 1. Visualizza Bobine Disponibili")
        print(" 2. Crea Nuova Bobina")
        print(" 3. Modifica Bobina")
        print(" 4. Elimina Bobina")
        print(" 5. Visualizza Storico Utilizzo")
        print(" 6. Torna al menu precedente")

        # Input dell'utente
        sub_scelta = input("\nSeleziona un'opzione (1-6): ").strip()

        # Gestione delle opzioni
        if sub_scelta == "1":
            print("\n=== Visualizza Bobine Disponibili ===")
            menu.handle_menu_action(parco_cavi.visualizza_parco_cavi, id_cantiere)  # Passa id_cantiere
            menu.wait_for_input()

        elif sub_scelta == "2":
            print("\n=== Crea Nuova Bobina ===")
            menu.handle_menu_action(parco_cavi.crea_bobina, id_cantiere)
            menu.wait_for_input()

        elif sub_scelta == "3":
            print("\n=== Modifica Bobina ===")
            # Ora basta passare l'id_cantiere, il resto lo gestisce la funzione
            menu.handle_menu_action(parco_cavi.modifica_bobina, id_cantiere)
            menu.wait_for_input()

        elif sub_scelta == "4":
            print("\n=== Elimina Bobina ===")
            numero_bobina = input("Inserisci numero bobina da eliminare: ").strip()
            if numero_bobina:
                if menu.confirm_action(f"Sei sicuro di voler eliminare la bobina numero {numero_bobina}?"):
                    menu.handle_menu_action(parco_cavi.elimina_bobina, numero_bobina, id_cantiere)
            else:
                print("\n❌ Numero bobina non valido!")
            menu.wait_for_input()

        elif sub_scelta == "5":
            print("\n=== Visualizza Storico Utilizzo Bobine ===")
            menu.handle_menu_action(parco_cavi.visualizza_utilizzo_bobine, id_cantiere)  # Passa id_cantiere
            menu.wait_for_input()

        elif sub_scelta == "6":
            print("\nTornando al menu precedente...")
            break  # Esce dal ciclo e torna al menu precedente

        else:
            print("\n❌ Opzione non valida! Seleziona un'opzione da 1 a 6.")
            menu.wait_for_input()

def gestisci_report(id_cantiere: str):
    """Gestione dei report per un cantiere specifico."""
    menu = MenuManager()

    while True:  # Aggiungiamo un ciclo per rimanere nel menu dei report
        menu.clear_screen()  # Pulisce lo schermo per una migliore esperienza utente
        print("\n" + "=" * 60)
        print("📈 GESTIONE REPORT")
        print("=" * 60)
        print("\nOpzioni disponibili:")
        print(" 1. Report Avanzamento")
        print(" 2. Bill of Quantities (Distinta Materiali)")
        print(" 3. Report Utilizzo Bobine")
        print(" 4. Report Posa per Periodo")
        print(" 5. Torna al menu precedente")

        try:
            sub_scelta = input("\nSeleziona un'opzione (1-5): ").strip()
            if sub_scelta == '5':
                return  # Torna al menu di gestione cavi

            visualizza = input("\nVisualizzare il report a schermo? (s/n) [default: s]: ").strip().lower() != 'n'
            formato = 'video' if visualizza else input("\nFormato del report (pdf/excel) [default: pdf]: ").strip().lower() or 'pdf'

            # Verifica che l'ID cantiere sia valido
            try:
                # Converte l'ID cantiere in intero se è una stringa
                id_cantiere_int = int(id_cantiere) if isinstance(id_cantiere, str) else id_cantiere
                if id_cantiere_int <= 0:
                    print("\n❌ ID cantiere non valido!")
                    menu.wait_for_input()
                    continue  # Torna all'inizio del ciclo
            except (ValueError, TypeError):
                print("\n❌ ID cantiere non valido!")
                menu.wait_for_input()
                continue  # Torna all'inizio del ciclo

            # Abilita il logging dettagliato per diagnosticare problemi
            logging.info(f"Generazione report: tipo={sub_scelta}, formato={formato}, id_cantiere={id_cantiere_int}")
            if sub_scelta == '1':
                result = menu.handle_menu_action(genera_progress_report, id_cantiere, formato)
                if formato == 'video':
                    # Il report è già stato visualizzato a schermo
                    pass
                elif result:
                    print(f"\n✅ Report Avanzamento generato: {result}")
                else:
                    print("\n❌ Errore nella generazione del report")
            elif sub_scelta == '2':
                result = menu.handle_menu_action(genera_bill_of_quantities, id_cantiere, formato)
                if formato == 'video':
                    # Il report è già stato visualizzato a schermo
                    pass
                elif result:
                    print(f"\n✅ Bill of Quantities generato: {result}")
                else:
                    print("\n❌ Errore nella generazione del report")
            elif sub_scelta == '3':
                # Chiedi all'utente quale tipo di report bobine vuole generare
                print("\nTipo di report bobine:")
                print(" 1. Report Bobina Specifica")
                print(" 2. Report Completo Bobine")
                tipo_report = input("\nSeleziona un'opzione (1/2): ").strip()

                if tipo_report == '1':
                    # Report bobina specifica
                    id_bobina_parte = input("\nInserisci l'identificativo della bobina: ").strip()
                    if not id_bobina_parte:
                        print("\n❌ Identificativo bobina non valido. Inserisci l'identificativo (es. 1, 2, A, B, ecc.)")
                        menu.wait_for_input()
                        continue  # Torna all'inizio del ciclo

                    # Costruisci l'ID completo della bobina (es. C1_B1)
                    id_bobina = f"C{id_cantiere}_B{id_bobina_parte}"
                    result = menu.handle_menu_action(genera_report_bobina_specifica, id_cantiere, id_bobina, formato)
                    if result:
                        print(f"\n✅ Report Bobina {id_bobina_parte} generato: {result}")
                    else:
                        print(f"\n❌ Errore nella generazione del report per la bobina {id_bobina_parte}")
                elif tipo_report == '2':
                    # Report completo bobine
                    result = menu.handle_menu_action(genera_report_utilizzo_bobine, id_cantiere, formato)
                    if result:
                        print(f"\n✅ Report Completo Bobine generato: {result}")
                    else:
                        print("\n❌ Errore nella generazione del report completo bobine")
                else:
                    print("\n❌ Opzione non valida!")
            elif sub_scelta == '4':
                result = menu.handle_menu_action(genera_report_posa_per_periodo, id_cantiere, formato)
                if result:
                    print(f"\n✅ Report Posa per Periodo generato: {result}")
                else:
                    print("\n❌ Errore nella generazione del report")
            else:
                print("\n❌ Opzione non valida!")

            # Attende input dall'utente prima di mostrare di nuovo il menu dei report
            menu.wait_for_input("\nPremi INVIO per tornare al menu dei report...")

        except Exception as e:
            print(f"\n❌ Errore: {str(e)}")
            menu.wait_for_input("\nPremi INVIO per tornare al menu dei report...")


def gestisci_excel(id_cantiere: str):
    """Gestione delle operazioni Excel"""
    menu = MenuManager()
    print("\n" + "=" * 60)
    print("📃 GESTIONE EXCEL")
    print("=" * 60)
    print("\nOpzioni disponibili:")
    print(" 1. Importa cavi da Excel")
    print(" 2. Importa parco bobine da Excel")
    print(" 3. Crea Template Excel per cavi")
    print(" 4. Crea Template Excel per parco bobine")
    print(" 5. Esporta cavi in Excel")
    print(" 6. Esporta parco bobine in Excel")
    print(" 7. Torna al menu precedente")
    sub_scelta = input("\nSeleziona un'opzione (1-7): ").strip()

    if sub_scelta == "1":
        percorso_file = input("\nInserisci il percorso del file Excel: ").strip()
        if percorso_file:
            try:
                # Aggiungiamo un blocco try-except per catturare e mostrare l'errore
                if not menu.handle_menu_action(importa_cavi_da_excel, id_cantiere, percorso_file):
                    print("\n❌ Importazione non riuscita!")
                else:
                    print("\n✅ Importazione completata con successo!")
            except Exception as e:
                print(f"\n❌ Errore durante l'importazione: {str(e)}")
                logging.error(f"Dettaglio errore importazione: {str(e)}")
        else:
            print("\n❌ Percorso file non valido!")
        menu.wait_for_input()

    elif sub_scelta == "2":
        percorso_file = input("\nInserisci il percorso del file Excel: ").strip()
        if percorso_file:
            try:
                # Importa parco bobine da Excel
                successo, messaggio, bobine_importate = importa_parco_bobine_da_excel(percorso_file, id_cantiere)
                if successo:
                    print(f"\n✅ {messaggio}")
                else:
                    print(f"\n❌ {messaggio}")
            except Exception as e:
                print(f"\n❌ Errore durante l'importazione: {str(e)}")
                logging.error(f"Dettaglio errore importazione parco bobine: {str(e)}")
        else:
            print("\n❌ Percorso file non valido!")
        menu.wait_for_input()

    elif sub_scelta == "3":
        try:
            # Crea un template Excel per cavi
            percorso_file = menu.handle_menu_action(crea_template_excel)
            if percorso_file:
                print(f"\n✅ Template Excel per cavi creato con successo: {percorso_file}")
                # Chiedi all'utente se vuole aprire il file
                if input("\nVuoi aprire il file? (s/n): ").strip().lower() == 's':
                    try:
                        if os.name == 'nt':  # Windows
                            os.startfile(percorso_file)
                        else:
                            subprocess.run(['xdg-open', percorso_file], check=True)
                    except Exception as e:
                        print(f"\n❌ Impossibile aprire il file: {str(e)}")
            else:
                print("\n❌ Errore durante la creazione del template Excel!")
        except Exception as e:
            print(f"\n❌ Errore: {str(e)}")
        menu.wait_for_input()

    elif sub_scelta == "4":
        try:
            # Crea un template Excel per parco bobine
            percorso_file = menu.handle_menu_action(crea_template_parco_bobine)
            if percorso_file:
                print(f"\n✅ Template Excel per parco bobine creato con successo: {percorso_file}")
                # Chiedi all'utente se vuole aprire il file
                if input("\nVuoi aprire il file? (s/n): ").strip().lower() == 's':
                    try:
                        if os.name == 'nt':  # Windows
                            os.startfile(percorso_file)
                        else:
                            subprocess.run(['xdg-open', percorso_file], check=True)
                    except Exception as e:
                        print(f"\n❌ Impossibile aprire il file: {str(e)}")
            else:
                print("\n❌ Errore durante la creazione del template Excel!")
        except Exception as e:
            print(f"\n❌ Errore: {str(e)}")
        menu.wait_for_input()

    elif sub_scelta == "5":
        try:
            # Esporta cavi in Excel
            print("\nEsportazione cavi in Excel...")
            percorso_file = menu.handle_menu_action(esporta_cavi_excel, id_cantiere)
            if percorso_file:
                print(f"\n✅ Cavi esportati con successo: {percorso_file}")
                # Chiedi all'utente se vuole aprire il file
                if input("\nVuoi aprire il file? (s/n): ").strip().lower() == 's':
                    try:
                        if os.name == 'nt':  # Windows
                            os.startfile(percorso_file)
                        else:
                            subprocess.run(['xdg-open', percorso_file], check=True)
                    except Exception as e:
                        print(f"\n❌ Impossibile aprire il file: {str(e)}")
            else:
                print("\n❌ Errore durante l'esportazione dei cavi!")
        except Exception as e:
            print(f"\n❌ Errore: {str(e)}")
        menu.wait_for_input()

    elif sub_scelta == "6":
        try:
            # Esporta parco bobine in Excel
            print("\nEsportazione parco bobine in Excel...")
            percorso_file = menu.handle_menu_action(esporta_parco_bobine_excel)
            if percorso_file:
                print(f"\n✅ Parco bobine esportato con successo: {percorso_file}")
                # Chiedi all'utente se vuole aprire il file
                if input("\nVuoi aprire il file? (s/n): ").strip().lower() == 's':
                    try:
                        if os.name == 'nt':  # Windows
                            os.startfile(percorso_file)
                        else:
                            subprocess.run(['xdg-open', percorso_file], check=True)
                    except Exception as e:
                        print(f"\n❌ Impossibile aprire il file: {str(e)}")
            else:
                print("\n❌ Errore durante l'esportazione del parco bobine!")
        except Exception as e:
            print(f"\n❌ Errore: {str(e)}")
        menu.wait_for_input()

    elif sub_scelta == "7":
        return

    else:
        print("\n❌ Opzione non valida!")
        menu.wait_for_input()
        return gestisci_excel(id_cantiere)  # Richiama la funzione per mostrare di nuovo il menu

def gestisci_test_collegamenti(id_cantiere: str):
    """Menu per la certificazione dei cavi."""
    # Converti l'ID cantiere in intero se è una stringa
    id_cantiere_int = int(id_cantiere) if isinstance(id_cantiere, str) else id_cantiere
    # Vai direttamente alla gestione delle certificazioni
    menu_gestione_certificazioni(id_cantiere_int)


def gestisci_cavi(id_cantiere: str):
    """Sottomenù per la gestione dei cavi e collegamenti."""
    menu = MenuManager()
    print("\n" + "=" * 60)
    print("🛠️ GESTIONE CAVI E COLLEGAMENTI")
    print("=" * 60)
    print("\nOpzioni disponibili:")
    print(" 1. Inserisci metri posati")
    print(" 2. Modifica bobina cavo posato")
    print(" 3. Aggiungi nuovo cavo")
    print(" 4. Modifica cavo")
    print(" 5. Elimina cavo")
    print(" 6. Collegamento cavo")
    print(" 7. Torna al menu precedente")
    sub_scelta = input("\nSeleziona un'opzione (1-7): ").strip()
    try:
        if sub_scelta == '1':
            menu.handle_menu_action(aggiorna_metri_posati, id_cantiere)
        elif sub_scelta == '2':
            menu.handle_menu_action(modifica_bobina_cavo_posato, id_cantiere)
        elif sub_scelta == '3':
            menu.handle_menu_action(aggiungi_cavo, id_cantiere)
        elif sub_scelta == '4':
            menu.handle_menu_action(modifica_cavo_manualmente, id_cantiere)
        elif sub_scelta == '5':
            menu.handle_menu_action(elimina_cavo, id_cantiere)
        elif sub_scelta == '6':
            # Converti l'ID cantiere in intero se è una stringa
            id_cantiere_int = int(id_cantiere) if isinstance(id_cantiere, str) else id_cantiere
            from modules.collegamenti_new import gestisci_collegamento_intelligente
            menu.handle_menu_action(gestisci_collegamento_intelligente, id_cantiere_int)
        elif sub_scelta == '7':
            return
        else:
            print("\n❌ Opzione non valida!")
    except Exception as e:
        print(f"\n❌ Errore durante l'operazione: {e}")


def crea_admin_predefinito():
    """Crea un account amministratore predefinito se non esiste."""
    try:
        db = Database()
        gestore_utenti = GestoreUtenti(db)  # Passa l'istanza di Database
        menu = MenuManager()

        with db.get_connection() as conn:
            c = conn.cursor()

            # Controlla se esiste già un utente 'admin'
            c.execute("SELECT COUNT(*) FROM Utenti")
            count = c.fetchone()[0]
            logging.info(f"Numero di utenti nel database: {count}")

            if count == 0:  # Usa il count salvato invece di fare un altro fetchone()
                # Crea l'admin come primo utente con ruolo owner
                username = "admin"
                password = "admin"
                logging.info("Tentativo di creazione admin con ruolo owner")
                if gestore_utenti.aggiungi_utente(username, password, "owner", allow_owner=True):
                    logging.info("Admin creato con successo come owner")
                    print("\n✅ Account amministratore predefinito creato:")
                    print(f"Username: {username}")
                    print(f"Password: {password}")
                    print("\n⚠️ Si raccomanda di cambiare la password al primo accesso")
                    menu.wait_for_input()
                else:
                    logging.error("Fallita la creazione dell'admin come owner")
                    print("\n❌ Errore durante la creazione dell'account amministratore")
                    menu.wait_for_input()

    except Exception as e:
        logging.error(f"Errore critico durante la creazione admin predefinito: {str(e)}")
        print("\n❌ Si è verificato un errore critico. Consultare i log per maggiori dettagli.")
        exit(1)


def recupera_password():
    """Funzione per recuperare la password dell'amministratore."""
    menu = MenuManager()
    db = Database()
    gestore_utenti = GestoreUtenti(db)  # Passa l'istanza di Database

    username = input("Inserisci il nome utente dell'amministratore: ").strip()
    nuova_password = input("Inserisci una nuova password: ").strip()
    conferma_password = input("Conferma la nuova password: ").strip()

    successo, messaggio = gestore_utenti.recupera_password(username, nuova_password, conferma_password)

    if successo:
        print(f"\n✅ {messaggio}")
    else:
        print(f"\n❌ {messaggio}")
    menu.wait_for_input()


class MenuComande:
    """Classe per gestire l'interfaccia utente delle comande."""

    def __init__(self):
        """Inizializza il menu delle comande."""
        self.tipi_comanda = {
            "1": "POSA",
            "2": "COLLEGAMENTO_PARTENZA",
            "3": "COLLEGAMENTO_ARRIVO"
        }
        self.menu = MenuManager()

    def menu_principale_comande(self, id_cantiere: int):
        """
        Menu principale per la gestione delle comande.

        Args:
            id_cantiere: ID del cantiere
        """
        while True:
            self.menu.clear_screen()
            print("\n" + "=" * 60)
            print("📋 GESTIONE COMANDE")
            print("=" * 60)
            print(f"\nCantiere ID: {id_cantiere}")
            print("\nOpzioni disponibili:")
            print(" 1. Visualizza comande")
            print(" 2. Crea nuova comanda")
            print(" 3. Gestisci comanda esistente")
            print(" 4. Torna al menu precedente")

            scelta = input("\nSeleziona un'opzione (1-4): ").strip()

            if scelta == "1":
                self.visualizza_comande(id_cantiere)
            elif scelta == "2":
                self.crea_nuova_comanda(id_cantiere)
            elif scelta == "3":
                self.gestisci_comanda_esistente(id_cantiere)
            elif scelta == "4":
                return
            else:
                print("\n❌ Opzione non valida!")
                self.menu.wait_for_input()

    def visualizza_comande(self, id_cantiere: int):
        """
        Visualizza le comande di un cantiere con filtro integrato.

        Args:
            id_cantiere: ID del cantiere
        """
        # Dizionario per mappare gli stati delle comande
        stati_comande = {
            "0": None,             # Tutte le comande
            "1": "CREATA",        # Da assegnare
            "2": "ASSEGNATA",     # In attesa
            "3": "IN_CORSO",      # In corso
            "4": "COMPLETATA"     # Completate
        }

        # Etichette più descrittive per gli stati
        etichette_stati = {
            None: "Tutti",
            "CREATA": "Da assegnare",
            "ASSEGNATA": "In attesa",
            "IN_CORSO": "In corso",
            "COMPLETATA": "Completate"
        }

        # Stato iniziale: mostra tutte le comande
        stato_filtro = None

        while True:
            self.menu.clear_screen()
            print("\n" + "=" * 60)
            print("📋 VISUALIZZA COMANDE")
            print("=" * 60)

            # Mostra il filtro attuale
            print(f"\nStato: [{etichette_stati[stato_filtro]}]")

            # Ottieni le comande filtrate
            comande = ottieni_comande_cantiere(id_cantiere, stato_filtro)

            # Visualizza le comande
            if not comande:
                print("\n⚠️ Nessuna comanda trovata con il filtro selezionato.")
            else:
                print("\nComande trovate:")
                print(f"{'Codice':<20} {'Tipo':<25} {'Stato':<15} {'Responsabile':<20} {'Data creazione':<15}")
                print("-" * 95)

                for comanda in comande:
                    tipo = comanda['tipo_comanda']
                    stato = comanda['stato']
                    responsabile = comanda['responsabile'] or "N/A"
                    data_creazione = comanda['data_creazione'].strftime('%d/%m/%Y') if comanda['data_creazione'] else "N/A"

                    print(f"{comanda['codice_comanda']:<20} {tipo:<25} {etichette_stati[stato]:<15} {responsabile:<20} {data_creazione:<15}")

            # Opzioni di filtro e navigazione
            print("\nOpzioni:")
            print(" 0. Mostra tutte le comande")
            print(" 1. Filtra: Da assegnare")
            print(" 2. Filtra: In attesa")
            print(" 3. Filtra: In corso")
            print(" 4. Filtra: Completate")
            print(" 9. Torna al menu precedente")

            scelta = input("\nSeleziona un'opzione: ").strip()

            if scelta in stati_comande:
                # Cambia il filtro
                stato_filtro = stati_comande[scelta]
            elif scelta == "9":
                # Torna al menu precedente
                return
            else:
                print("\n❌ Opzione non valida!")
                self.menu.wait_for_input()

    def crea_nuova_comanda(self, id_cantiere: int):
        """
        Crea una nuova comanda.

        Args:
            id_cantiere: ID del cantiere
        """
        self.menu.clear_screen()
        print("\n" + "=" * 60)
        print("📋 CREA NUOVA COMANDA")
        print("=" * 60)

        # Selezione tipo comanda
        print("\nTipo di comanda:")
        print(" 1. Posa cavi")
        print(" 2. Collegamento lato partenza")
        print(" 3. Collegamento lato arrivo")

        tipo_scelta = input("\nSeleziona il tipo di comanda (1-3): ").strip()
        if tipo_scelta not in self.tipi_comanda:
            print("\n❌ Tipo di comanda non valido!")
            self.menu.wait_for_input()
            return

        tipo_comanda = self.tipi_comanda[tipo_scelta]

        # Inserimento descrizione
        descrizione = input("\nInserisci una descrizione per la comanda: ").strip()
        if not descrizione:
            print("\n❌ La descrizione non può essere vuota!")
            self.menu.wait_for_input()
            return

        # Inserimento responsabile
        responsabile = input("\nInserisci il nome del responsabile: ").strip()
        if not responsabile:
            print("\n❌ Il responsabile non può essere vuoto!")
            self.menu.wait_for_input()
            return

        # Inserimento data scadenza (opzionale)
        data_scadenza_str = input("\nInserisci la data di scadenza (gg/mm/aaaa) o lascia vuoto: ").strip()
        data_scadenza = None

        if data_scadenza_str:
            try:
                giorno, mese, anno = map(int, data_scadenza_str.split('/'))
                data_scadenza = date(anno, mese, giorno)

                # Verifica che la data non sia nel passato
                if data_scadenza < date.today():
                    print("\n❌ La data di scadenza non può essere nel passato!")
                    self.menu.wait_for_input()
                    return
            except ValueError:
                print("\n❌ Formato data non valido! Usa il formato gg/mm/aaaa.")
                self.menu.wait_for_input()
                return

        # Crea la comanda
        codice_comanda = crea_comanda(id_cantiere, tipo_comanda, descrizione, responsabile, data_scadenza)

        if codice_comanda:
            print(f"\n✅ Comanda creata con successo! Codice: {codice_comanda}")

            # Chiedi se si vogliono assegnare subito i cavi
            assegna_subito = input("\nVuoi assegnare subito i cavi a questa comanda? (s/n): ").strip().lower()
            if assegna_subito == 's':
                self.assegna_cavi_a_comanda(id_cantiere, codice_comanda, tipo_comanda)
        else:
            print("\n❌ Errore nella creazione della comanda!")

        self.menu.wait_for_input()

    def assegna_cavi_a_comanda(self, id_cantiere: int, codice_comanda: str, tipo_comanda: str):
        """
        Assegna cavi a una comanda.

        Args:
            id_cantiere: ID del cantiere
            codice_comanda: Codice della comanda
            tipo_comanda: Tipo della comanda
        """
        self.menu.clear_screen()
        print("\n" + "=" * 60)
        print("📋 ASSEGNA CAVI ALLA COMANDA")
        print("=" * 60)
        print(f"\nComanda: {codice_comanda}")
        print(f"Tipo: {tipo_comanda}")

        # Qui dovresti implementare la logica per selezionare i cavi da assegnare
        # Per semplicità, chiediamo all'utente di inserire gli ID dei cavi separati da virgola

        print("\nInserisci gli ID dei cavi da assegnare, separati da virgola.")
        print("Esempio: CAVO001,CAVO002,CAVO003")

        id_cavi_input = input("\nID cavi: ").strip()
        if not id_cavi_input:
            print("\n❌ Nessun cavo inserito!")
            self.menu.wait_for_input()
            return

        # Converti l'input in una lista di ID cavi
        lista_id_cavi = [id_cavo.strip() for id_cavo in id_cavi_input.split(',')]

        # Assegna i cavi alla comanda
        if assegna_cavi_a_comanda(codice_comanda, lista_id_cavi):
            print(f"\n✅ Cavi assegnati con successo alla comanda {codice_comanda}!")
        else:
            print("\n❌ Errore nell'assegnazione dei cavi!")

        self.menu.wait_for_input()

    def gestisci_comanda_esistente(self, id_cantiere: int):
        """
        Gestisce una comanda esistente.

        Args:
            id_cantiere: ID del cantiere
        """
        self.menu.clear_screen()
        print("\n" + "=" * 60)
        print("📋 GESTISCI COMANDA ESISTENTE")
        print("=" * 60)

        # Visualizza le comande disponibili
        comande = ottieni_comande_cantiere(id_cantiere)

        if not comande:
            print("\n⚠️ Nessuna comanda trovata per questo cantiere.")
            self.menu.wait_for_input()
            return

        print("\nComande disponibili:")
        print(f"{'#':<3} {'Codice':<20} {'Tipo':<25} {'Stato':<15} {'Responsabile':<20}")
        print("-" * 83)

        for i, comanda in enumerate(comande, 1):
            tipo = comanda['tipo_comanda']
            stato = comanda['stato']
            responsabile = comanda['responsabile'] or "N/A"

            print(f"{i:<3} {comanda['codice_comanda']:<20} {tipo:<25} {stato:<15} {responsabile:<20}")

        # Selezione della comanda
        try:
            indice = int(input("\nSeleziona il numero della comanda (0 per tornare indietro): ").strip())

            if indice == 0:
                return

            if indice < 1 or indice > len(comande):
                print("\n❌ Numero non valido!")
                self.menu.wait_for_input()
                return

            comanda_selezionata = comande[indice - 1]
            self.menu_gestione_comanda(id_cantiere, comanda_selezionata['codice_comanda'])

        except ValueError:
            print("\n❌ Inserisci un numero valido!")
            self.menu.wait_for_input()

    def menu_gestione_comanda(self, id_cantiere: int, codice_comanda: str):
        """
        Menu per la gestione di una comanda specifica.

        Args:
            id_cantiere: ID del cantiere
            codice_comanda: Codice della comanda
        """
        while True:
            # Ottieni i dettagli aggiornati della comanda
            comanda = ottieni_dettagli_comanda(codice_comanda)

            if not comanda:
                print("\n❌ Comanda non trovata!")
                self.menu.wait_for_input()
                return

            self.menu.clear_screen()
            print("\n" + "=" * 60)
            print("📋 GESTIONE COMANDA")
            print("=" * 60)

            # Visualizza i dettagli della comanda
            print(f"\nCodice: {comanda['codice_comanda']}")
            print(f"Tipo: {comanda['tipo_comanda']}")
            print(f"Descrizione: {comanda['descrizione']}")
            print(f"Stato: {comanda['stato']}")
            print(f"Responsabile: {comanda['responsabile']}")
            print(f"Data creazione: {comanda['data_creazione'].strftime('%d/%m/%Y') if comanda['data_creazione'] else 'N/A'}")
            print(f"Data scadenza: {comanda['data_scadenza'].strftime('%d/%m/%Y') if comanda['data_scadenza'] else 'N/A'}")

            # Opzioni disponibili in base allo stato della comanda
            print("\nOpzioni disponibili:")
            print(" 1. Visualizza cavi assegnati")

            if comanda['stato'] == "CREATA":
                print(" 2. Assegna cavi")
                print(" 3. Cambia stato in 'ASSEGNATA'")
                print(" 4. Elimina comanda")
            elif comanda['stato'] == "ASSEGNATA":
                print(" 2. Assegna altri cavi")
                print(" 3. Cambia stato in 'IN_CORSO'")
                print(" 4. Elimina comanda")
            elif comanda['stato'] == "IN_CORSO":
                if comanda['tipo_comanda'] == "POSA":
                    print(" 2. Aggiorna dati di posa")
                else:
                    print(" 2. Aggiorna dati di collegamento")
                print(" 3. Cambia stato in 'COMPLETATA'")

            print(" 9. Torna al menu precedente")

            scelta = input("\nSeleziona un'opzione: ").strip()

            if scelta == "1":
                self.visualizza_cavi_comanda(codice_comanda)
            elif scelta == "2":
                if comanda['stato'] in ["CREATA", "ASSEGNATA"]:
                    self.assegna_cavi_a_comanda(id_cantiere, codice_comanda, comanda['tipo_comanda'])
                elif comanda['stato'] == "IN_CORSO":
                    if comanda['tipo_comanda'] == "POSA":
                        self.aggiorna_dati_posa(codice_comanda)
                    else:
                        self.aggiorna_dati_collegamento(codice_comanda, comanda['tipo_comanda'])
            elif scelta == "3":
                if comanda['stato'] == "CREATA":
                    cambia_stato_comanda(codice_comanda, "ASSEGNATA")
                elif comanda['stato'] == "ASSEGNATA":
                    cambia_stato_comanda(codice_comanda, "IN_CORSO")
                elif comanda['stato'] == "IN_CORSO":
                    cambia_stato_comanda(codice_comanda, "COMPLETATA")
            elif scelta == "4" and comanda['stato'] in ["CREATA", "ASSEGNATA"]:
                conferma = input("\nSei sicuro di voler eliminare questa comanda? (s/n): ").strip().lower()
                if conferma == 's':
                    if elimina_comanda(codice_comanda):
                        print("\n✅ Comanda eliminata con successo!")
                        self.menu.wait_for_input()
                        return
                    else:
                        print("\n❌ Errore nell'eliminazione della comanda!")
                        self.menu.wait_for_input()
            elif scelta == "9":
                return
            else:
                print("\n❌ Opzione non valida!")
                self.menu.wait_for_input()

    def visualizza_cavi_comanda(self, codice_comanda: str):
        """
        Visualizza i cavi assegnati a una comanda.

        Args:
            codice_comanda: Codice della comanda
        """
        self.menu.clear_screen()
        print("\n" + "=" * 60)
        print("📋 CAVI ASSEGNATI ALLA COMANDA")
        print("=" * 60)
        print(f"\nComanda: {codice_comanda}")

        # Ottieni i cavi assegnati alla comanda
        cavi = ottieni_cavi_comanda(codice_comanda)

        if not cavi:
            print("\n⚠️ Nessun cavo assegnato a questa comanda.")
            self.menu.wait_for_input()
            return

        # Visualizza i cavi
        print(f"\nCavi assegnati ({len(cavi)}):")
        print(f"{'ID Cavo':<15} {'Utility':<15} {'Tipologia':<15} {'Partenza':<20} {'Arrivo':<20}")
        print("-" * 85)

        for cavo in cavi:
            id_cavo = cavo['id_cavo']
            utility = cavo['utility']
            tipologia = cavo['tipologia']
            partenza = cavo['ubicazione_partenza']
            arrivo = cavo['ubicazione_arrivo']

            print(f"{id_cavo:<15} {utility:<15} {tipologia:<15} {partenza:<20} {arrivo:<20}")

        self.menu.wait_for_input()

    def aggiorna_dati_posa(self, codice_comanda: str):
        """
        Aggiorna i dati di posa per i cavi di una comanda.

        Args:
            codice_comanda: Codice della comanda
        """
        self.menu.clear_screen()
        print("\n" + "=" * 60)
        print("📋 AGGIORNA DATI DI POSA")
        print("=" * 60)
        print(f"\nComanda: {codice_comanda}")

        # Ottieni i cavi assegnati alla comanda
        cavi = ottieni_cavi_comanda(codice_comanda)

        if not cavi:
            print("\n⚠️ Nessun cavo assegnato a questa comanda.")
            self.menu.wait_for_input()
            return

        # Visualizza i cavi
        print(f"\nCavi da aggiornare ({len(cavi)}):")
        print(f"{'#':<3} {'ID Cavo':<15} {'Metri teorici':<15} {'Metri reali':<15} {'Stato':<15}")
        print("-" * 63)

        for i, cavo in enumerate(cavi, 1):
            id_cavo = cavo['id_cavo']
            metri_teorici = cavo['metri_teorici']
            metri_reali = cavo['metratura_reale'] or 0
            stato = cavo['stato_installazione']

            print(f"{i:<3} {id_cavo:<15} {metri_teorici:<15} {metri_reali:<15} {stato:<15}")

        print("\nInserisci i dati di posa per i cavi.")
        print("Per ogni cavo, inserisci: metratura reale, responsabile")
        print("Esempio: 100, Mario Rossi")
        print("Lascia vuoto per saltare un cavo.")

        dati_posa = {}

        for cavo in cavi:
            id_cavo = cavo['id_cavo']
            metri_teorici = cavo['metri_teorici']

            print(f"\nCavo: {id_cavo} (Metri teorici: {metri_teorici})")
            input_dati = input("Dati di posa (metratura, responsabile): ").strip()

            if input_dati:
                try:
                    parti = input_dati.split(',')
                    metratura = float(parti[0].strip())
                    responsabile = parti[1].strip() if len(parti) > 1 else ""

                    dati_posa[id_cavo] = {
                        'metratura_reale': metratura,
                        'responsabile_posa': responsabile,
                        'data_posa': date.today()
                    }
                except ValueError:
                    print("❌ Formato non valido! Saltato.")

        if not dati_posa:
            print("\n❌ Nessun dato di posa inserito!")
            self.menu.wait_for_input()
            return

        # Conferma
        print("\nRiepilogo dati di posa:")
        for id_cavo, dati in dati_posa.items():
            print(f"Cavo: {id_cavo}, Metri: {dati['metratura_reale']}, Responsabile: {dati['responsabile_posa']}")

        conferma = input("\nConfermi l'aggiornamento dei dati di posa? (s/n): ").strip().lower()
        if conferma == 's':
            if aggiorna_dati_posa(codice_comanda, dati_posa):
                print("\n✅ Dati di posa aggiornati con successo!")
            else:
                print("\n❌ Errore nell'aggiornamento dei dati di posa!")

        self.menu.wait_for_input()

    def aggiorna_dati_collegamento(self, codice_comanda: str, tipo_comanda: str):
        """
        Aggiorna i dati di collegamento per i cavi di una comanda.

        Args:
            codice_comanda: Codice della comanda
            tipo_comanda: Tipo della comanda
        """
        self.menu.clear_screen()
        print("\n" + "=" * 60)
        print("📋 AGGIORNA DATI DI COLLEGAMENTO")
        print("=" * 60)
        print(f"\nComanda: {codice_comanda}")
        print(f"Tipo: {tipo_comanda}")

        # Ottieni i cavi assegnati alla comanda
        cavi = ottieni_cavi_comanda(codice_comanda)

        if not cavi:
            print("\n⚠️ Nessun cavo assegnato a questa comanda.")
            self.menu.wait_for_input()
            return

        # Visualizza i cavi
        print(f"\nCavi da aggiornare ({len(cavi)}):")
        print(f"{'#':<3} {'ID Cavo':<15} {'Partenza':<20} {'Arrivo':<20} {'Collegamenti':<15}")
        print("-" * 73)

        for i, cavo in enumerate(cavi, 1):
            id_cavo = cavo['id_cavo']
            partenza = cavo['ubicazione_partenza']
            arrivo = cavo['ubicazione_arrivo']
            collegamenti = cavo['collegamenti'] if 'collegamenti' in cavo else 0

            # Converti il valore numerico in una descrizione
            stato_collegamenti = ""
            if collegamenti == 0:
                stato_collegamenti = "Nessuno"
            elif collegamenti == 1:
                stato_collegamenti = "Partenza"
            elif collegamenti == 2:
                stato_collegamenti = "Arrivo"
            elif collegamenti == 3:
                stato_collegamenti = "Entrambi"

            print(f"{i:<3} {id_cavo:<15} {partenza:<20} {arrivo:<20} {stato_collegamenti:<15}")

        print("\nInserisci il responsabile per i collegamenti.")
        print("Esempio: Mario Rossi")
        print("Lascia vuoto per saltare un cavo.")

        dati_collegamento = {}

        for cavo in cavi:
            id_cavo = cavo['id_cavo']

            print(f"\nCavo: {id_cavo}")
            responsabile = input("Responsabile: ").strip()

            if responsabile:
                dati_collegamento[id_cavo] = {
                    'responsabile': responsabile
                }

        if not dati_collegamento:
            print("\n❌ Nessun dato di collegamento inserito!")
            self.menu.wait_for_input()
            return

        # Conferma
        print("\nRiepilogo dati di collegamento:")
        for id_cavo, dati in dati_collegamento.items():
            print(f"Cavo: {id_cavo}, Responsabile: {dati['responsabile']}")

        conferma = input("\nConfermi l'aggiornamento dei dati di collegamento? (s/n): ").strip().lower()
        if conferma == 's':
            if aggiorna_dati_collegamento(codice_comanda, dati_collegamento):
                print("\n✅ Dati di collegamento aggiornati con successo!")
            else:
                print("\n❌ Errore nell'aggiornamento dei dati di collegamento!")

        self.menu.wait_for_input()


def menu_comande(id_cantiere: int):
    """Funzione per avviare il menu delle comande."""
    menu = MenuComande()
    menu.menu_principale_comande(id_cantiere)





def menu_login():
    """Menu di login."""
    menu = MenuManager()
    db = Database()
    gestore_utenti = GestoreUtenti(db)
    while True:
        menu.clear_screen()
        print("\n=== LOGIN ===")
        print("1. Login Amministratore")
        print("2. Login Utente Standard")
        print("3. Login Utente Cantieri")
        print("4. Recupero Password Admin")
        print("5. Esci")
        try:
            scelta = input("\nSeleziona un'opzione: ").strip()
            if scelta in ["1", "2", "3"]:
                if scelta == "3":
                    codice_univoco = input("Codice Univoco del Cantiere: ").strip()
                    password = input("Password: ").strip()
                    if not codice_univoco or not password:
                        print("❌ Codice univoco e password non possono essere vuoti")
                        menu.wait_for_input()
                        continue
                    cantiere = gestore_utenti.verifica_credenziali_cantiere(codice_univoco, password)
                    if cantiere is None:
                        print("❌ Credenziali non valide")
                        menu.wait_for_input()
                        continue

                    # Verifica se la funzione restituisce 2 o 3 valori (compatibilità con versioni precedenti)
                    if len(cantiere) == 3:
                        id_cantiere, nome_cantiere, password_corretta = cantiere
                        if not password_corretta:
                            print("⚠️ Accesso effettuato con la password dell'utente proprietario")
                            print("⚠️ Si consiglia di impostare una password specifica per il cantiere")
                            menu.wait_for_input()
                    else:
                        id_cantiere, nome_cantiere = cantiere

                    print(f"✅ Accesso effettuato al cantiere {nome_cantiere}")
                    menu_gestione_cavi(id_cantiere, nome_cantiere, is_cantiere_user=True)
                else:
                    username = input("Username: ").strip()
                    password = input("Password: ").strip()
                    if not username or not password:
                        print("❌ Username e password non possono essere vuoti")
                        menu.wait_for_input()
                        continue
                    utente = gestore_utenti.verifica_credenziali(username, password)
                    if utente is None:
                        print("❌ Credenziali non valide")
                        menu.wait_for_input()
                        continue
                    # Verifica corrispondenza tra scelta e ruolo
                    if scelta == "1" and utente["ruolo"] != "owner":
                        print("❌ Non hai i permessi di amministratore")
                        menu.wait_for_input()
                    elif scelta == "2" and utente["ruolo"] != "user":
                        print("❌ Non hai i permessi di utente standard")
                        menu.wait_for_input()
                    else:
                        # Verifica se l'admin sta usando la password predefinita
                        if scelta == "1" and username == "admin" and "password_default" in utente and utente["password_default"]:
                            print("⚠️ Stai utilizzando la password predefinita per l'amministratore")
                            print("⚠️ Si consiglia vivamente di cambiarla per motivi di sicurezza")
                            cambio_password = input("Vuoi cambiarla ora? (s/n): ").strip().lower()
                            if cambio_password == 's':
                                nuova_password = input("Inserisci la nuova password: ").strip()
                                conferma_password = input("Conferma la nuova password: ").strip()
                                if nuova_password == conferma_password and nuova_password:
                                    successo, messaggio = gestore_utenti.aggiorna_password(username, nuova_password)
                                    if successo:
                                        print(f"✅ {messaggio}")
                                    else:
                                        print(f"❌ {messaggio}")
                                    menu.wait_for_input()
                                else:
                                    print("❌ Le password non corrispondono o sono vuote")
                                    menu.wait_for_input()

                        # Login riuscito, apri il menu appropriato
                        if scelta == "1":
                            menu_amministratore()
                        elif scelta == "2":
                            menu_user(username)
            elif scelta == "4":
                recupera_password()
            elif scelta == "5":
                print("Arrivederci! 👋")
                break
            else:
                print("❌ Opzione non valida!")
                menu.wait_for_input()
        except Exception as e:
            logging.error(f"❌ Errore nel menu di login: {str(e)}")
            print("\n❌ Si è verificato un errore. Consultare i log per maggiori dettagli.")


def main():
    """Punto di ingresso principale dell'applicazione."""
    try:
        # Configura il logging
        logging.basicConfig(
            filename='app.log',
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s'
        )

        db = Database()  # Già inizializza il database nel costruttore

        # Verifica l'integrità del database
        if not db.verifica_integrita_database():
            logging.error("❌ Problemi di integrità del database rilevati")
            exit(1)

        # Le tabelle per i collegamenti e i test sono ora definite nella classe Database
        # La tabella Comande e i relativi campi sono già definiti nella funzione inizializza_database

        # Verifica/crea admin predefinito
        crea_admin_predefinito()

        # Avvia il menu login
        menu_login()  # Sostituito con menu_login()

    except Exception as e:
        logging.error(f"Errore critico in main: {str(e)}")
        print("\n❌ Si è verificato un errore critico. Consultare i log per maggiori dettagli.")
        exit(1)


if __name__ == "__main__":
    main()
