import { getPickersLocalization } from './utils/getPickersLocalization';
var timeViews = {
  hours: 'گھنٹے',
  minutes: 'منٹ',
  seconds: 'سیکنڈ',
  meridiem: 'میریڈیم'
};
var urPKPickers = {
  // Calendar navigation
  previousMonth: 'پچھلا مہینہ',
  nextMonth: 'اگلا مہینہ',
  // View navigation
  openPreviousView: 'پچھلا ویو کھولیں',
  openNextView: 'اگلا ویو کھولیں',
  calendarViewSwitchingButtonAriaLabel: function calendarViewSwitchingButtonAriaLabel(view) {
    return view === 'year' ? 'سال والا ویو کھلا ہے۔ کیلنڈر والا ویو کھولیں' : 'کیلنڈر والا ویو کھلا ہے۔ سال والا ویو کھولیں';
  },
  // DateRange placeholders
  start: 'شروع',
  end: 'ختم',
  // Action bar
  cancelButtonLabel: 'کینسل',
  clearButtonLabel: 'کلئیر',
  okButtonLabel: 'اوکے',
  todayButtonLabel: 'آج',
  // Toolbar titles
  datePickerToolbarTitle: 'تاریخ منتخب کریں',
  dateTimePickerToolbarTitle: 'تاریخ اور وقت منتخب کریں',
  timePickerToolbarTitle: 'وقت منتخب کریں',
  dateRangePickerToolbarTitle: 'تاریخوں کی رینج منتخب کریں',
  // Clock labels
  clockLabelText: function clockLabelText(view, time, adapter) {
    return "".concat(timeViews[view], " \u0645\u0646\u062A\u062E\u0628 \u06A9\u0631\u06CC\u06BA ").concat(time === null ? 'کوئی وقت منتخب نہیں' : "\u0645\u0646\u062A\u062E\u0628 \u0648\u0642\u062A \u06C1\u06D2 ".concat(adapter.format(time, 'fullTime')));
  },
  hoursClockNumberText: function hoursClockNumberText(hours) {
    return "".concat(hours, " \u06AF\u06BE\u0646\u0679\u06D2");
  },
  minutesClockNumberText: function minutesClockNumberText(minutes) {
    return "".concat(minutes, " \u0645\u0646\u0679");
  },
  secondsClockNumberText: function secondsClockNumberText(seconds) {
    return "".concat(seconds, " \u0633\u06CC\u06A9\u0646\u0688");
  },
  // Digital clock labels
  selectViewText: function selectViewText(view) {
    return "".concat(timeViews[view], " \u0645\u0646\u062A\u062E\u0628 \u06A9\u0631\u06CC\u06BA");
  },
  // Calendar labels
  calendarWeekNumberHeaderLabel: 'ہفتہ نمبر',
  calendarWeekNumberHeaderText: 'نمبر',
  calendarWeekNumberAriaLabelText: function calendarWeekNumberAriaLabelText(weekNumber) {
    return "\u06C1\u0641\u062A\u06C1 ".concat(weekNumber);
  },
  calendarWeekNumberText: function calendarWeekNumberText(weekNumber) {
    return "".concat(weekNumber);
  },
  // Open picker labels
  openDatePickerDialogue: function openDatePickerDialogue(value, utils) {
    return value !== null && utils.isValid(value) ? "\u062A\u0627\u0631\u06CC\u062E \u0645\u0646\u062A\u062E\u0628 \u06A9\u0631\u06CC\u06BA\u060C \u0645\u0646\u062A\u062E\u0628 \u0634\u062F\u06C1 \u062A\u0627\u0631\u06CC\u062E \u06C1\u06D2 ".concat(utils.format(value, 'fullDate')) : 'تاریخ منتخب کریں';
  },
  openTimePickerDialogue: function openTimePickerDialogue(value, utils) {
    return value !== null && utils.isValid(value) ? "\u0648\u0642\u062A \u0645\u0646\u062A\u062E\u0628 \u06A9\u0631\u06CC\u06BA\u060C \u0645\u0646\u062A\u062E\u0628 \u0634\u062F\u06C1 \u0648\u0642\u062A \u06C1\u06D2 ".concat(utils.format(value, 'fullTime')) : 'وقت منتخب کریں';
  },
  // fieldClearLabel: 'Clear value',

  // Table labels
  timeTableLabel: 'وقت منتخب کریں',
  dateTableLabel: 'تاریخ منتخب کریں'

  // Field section placeholders
  // fieldYearPlaceholder: params => 'Y'.repeat(params.digitAmount),
  // fieldMonthPlaceholder: params => params.contentType === 'letter' ? 'MMMM' : 'MM',
  // fieldDayPlaceholder: () => 'DD',
  // fieldWeekDayPlaceholder: params => params.contentType === 'letter' ? 'EEEE' : 'EE',
  // fieldHoursPlaceholder: () => 'hh',
  // fieldMinutesPlaceholder: () => 'mm',
  // fieldSecondsPlaceholder: () => 'ss',
  // fieldMeridiemPlaceholder: () => 'aa',
};
export var urPK = getPickersLocalization(urPKPickers);