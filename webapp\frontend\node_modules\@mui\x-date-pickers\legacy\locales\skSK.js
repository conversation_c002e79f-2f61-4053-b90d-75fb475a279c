import { getPickersLocalization } from './utils/getPickersLocalization';
// maps TimeView to its translation
var timeViews = {
  hours: 'Hodiny',
  minutes: 'Min<PERSON><PERSON>',
  seconds: 'Sekundy',
  meridiem: '<PERSON><PERSON><PERSON><PERSON>'
};
var skSKPickers = {
  // Calendar navigation
  previousMonth: 'Ďalší mesiac',
  nextMonth: 'Predchádzajúci mesiac',
  // View navigation
  openPreviousView: 'otvoriť predchádzajúce zobrazenie',
  openNextView: 'otvoriť ďalšie zobrazenie',
  calendarViewSwitchingButtonAriaLabel: function calendarViewSwitchingButtonAriaLabel(view) {
    return view === 'year' ? 'ročné zobrazenie otvorené, prepnite do zobrazenia kalendára' : 'zobrazenie kalendára otvorené, prepnite do zobrazenia roka';
  },
  // DateRange placeholders
  start: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
  end: '<PERSON><PERSON><PERSON>',
  // Action bar
  cancelButtonLabel: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
  clearButtonLabel: 'Vymaza<PERSON>',
  okButtonLabel: 'Potvrdiť',
  todayButtonLabel: 'Dnes',
  // Toolbar titles
  datePickerToolbarTitle: 'Vyberte dátum',
  dateTimePickerToolbarTitle: 'Vyberte dátum a čas',
  timePickerToolbarTitle: 'Vyberte čas',
  dateRangePickerToolbarTitle: 'Vyberete rozmedzie dátumov',
  // Clock labels
  clockLabelText: function clockLabelText(view, time, adapter) {
    var _timeViews$view;
    return "".concat((_timeViews$view = timeViews[view]) != null ? _timeViews$view : view, " vybran\xFD. ").concat(time === null ? 'Nie je vybraný čas' : "Vybran\xFD \u010Das je ".concat(adapter.format(time, 'fullTime')));
  },
  hoursClockNumberText: function hoursClockNumberText(hours) {
    return "".concat(hours, " hod\xEDn");
  },
  minutesClockNumberText: function minutesClockNumberText(minutes) {
    return "".concat(minutes, " min\xFAt");
  },
  secondsClockNumberText: function secondsClockNumberText(seconds) {
    return "".concat(seconds, " sek\xFAnd");
  },
  // Digital clock labels
  selectViewText: function selectViewText(view) {
    return "Vyberte ".concat(timeViews[view]);
  },
  // Calendar labels
  calendarWeekNumberHeaderLabel: 'Týždeň v roku',
  calendarWeekNumberHeaderText: '#',
  calendarWeekNumberAriaLabelText: function calendarWeekNumberAriaLabelText(weekNumber) {
    return "".concat(weekNumber, " t\xFD\u017Ede\u0148 v roku");
  },
  calendarWeekNumberText: function calendarWeekNumberText(weekNumber) {
    return "".concat(weekNumber);
  },
  // Open picker labels
  openDatePickerDialogue: function openDatePickerDialogue(value, utils) {
    return value !== null && utils.isValid(value) ? "Vyberte d\xE1tum, vybran\xFD d\xE1tum je ".concat(utils.format(value, 'fullDate')) : 'Vyberte dátum';
  },
  openTimePickerDialogue: function openTimePickerDialogue(value, utils) {
    return value !== null && utils.isValid(value) ? "Vyberte \u010Das, vybran\xFD \u010Das je ".concat(utils.format(value, 'fullTime')) : 'Vyberte čas';
  },
  // fieldClearLabel: 'Clear value',

  // Table labels
  timeTableLabel: 'vyberte čas',
  dateTableLabel: 'vyberte dátum',
  // Field section placeholders
  fieldYearPlaceholder: function fieldYearPlaceholder(params) {
    return 'Y'.repeat(params.digitAmount);
  },
  fieldMonthPlaceholder: function fieldMonthPlaceholder(params) {
    return params.contentType === 'letter' ? 'MMMM' : 'MM';
  },
  fieldDayPlaceholder: function fieldDayPlaceholder() {
    return 'DD';
  },
  // fieldWeekDayPlaceholder: params => params.contentType === 'letter' ? 'EEEE' : 'EE',
  fieldHoursPlaceholder: function fieldHoursPlaceholder() {
    return 'hh';
  },
  fieldMinutesPlaceholder: function fieldMinutesPlaceholder() {
    return 'mm';
  },
  fieldSecondsPlaceholder: function fieldSecondsPlaceholder() {
    return 'ss';
  },
  fieldMeridiemPlaceholder: function fieldMeridiemPlaceholder() {
    return 'aa';
  }
};
export var skSK = getPickersLocalization(skSKPickers);