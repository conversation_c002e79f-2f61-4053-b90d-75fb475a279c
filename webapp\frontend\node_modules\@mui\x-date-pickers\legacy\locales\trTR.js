import { getPickersLocalization } from './utils/getPickersLocalization';
var timeViews = {
  hours: 'saat',
  minutes: 'dakika',
  seconds: 'saniye',
  meridiem: 'öğleden sonra'
};
var trTRPickers = {
  // Calendar navigation
  previousMonth: 'Önceki ay',
  nextMonth: 'Sonraki ay',
  // View navigation
  openPreviousView: 'sonraki görünüm',
  openNextView: 'önce<PERSON> görünüm',
  calendarViewSwitchingButtonAriaLabel: function calendarViewSwitchingButtonAriaLabel(view) {
    return view === 'year' ? 'yıl görünümü açık, takvim görünümüne geç' : 'takvim görünümü açık, yıl görünümüne geç';
  },
  // DateRange placeholders
  start: 'Başlangı<PERSON>',
  end: 'Biti<PERSON>',
  // Action bar
  cancelButtonLabel: 'iptal',
  clearButtonLabel: 'Te<PERSON><PERSON>',
  okButtonLabel: 'Tamam',
  todayButtonLabel: 'Bugün',
  // Toolbar titles
  datePickerToolbarTitle: 'Ta<PERSON>h Seç',
  dateTimePickerToolbarTitle: 'Tarih & Saat seç',
  timePickerToolbarTitle: 'Saat seç',
  dateRangePickerToolbarTitle: 'Tarih aralığı seçin',
  // Clock labels
  clockLabelText: function clockLabelText(view, time, adapter) {
    return "".concat(timeViews[view], " se\xE7.  ").concat(time === null ? 'Zaman seçilmedi' : "Se\xE7ilen zaman: ".concat(adapter.format(time, 'fullTime')));
  },
  hoursClockNumberText: function hoursClockNumberText(hours) {
    return "".concat(hours, " saat");
  },
  minutesClockNumberText: function minutesClockNumberText(minutes) {
    return "".concat(minutes, " dakika");
  },
  secondsClockNumberText: function secondsClockNumberText(seconds) {
    return "".concat(seconds, " saniye");
  },
  // Digital clock labels
  selectViewText: function selectViewText(view) {
    return "Se\xE7 ".concat(timeViews[view]);
  },
  // Calendar labels
  calendarWeekNumberHeaderLabel: 'Hafta numarası',
  calendarWeekNumberHeaderText: '#',
  calendarWeekNumberAriaLabelText: function calendarWeekNumberAriaLabelText(weekNumber) {
    return "Hafta ".concat(weekNumber);
  },
  calendarWeekNumberText: function calendarWeekNumberText(weekNumber) {
    return "".concat(weekNumber);
  },
  // Open picker labels
  openDatePickerDialogue: function openDatePickerDialogue(value, utils) {
    return value !== null && utils.isValid(value) ? "Tarih se\xE7in, se\xE7ilen tarih: ".concat(utils.format(value, 'fullDate')) : 'Tarih seç';
  },
  openTimePickerDialogue: function openTimePickerDialogue(value, utils) {
    return value !== null && utils.isValid(value) ? "Saat se\xE7in, se\xE7ilen saat: ".concat(utils.format(value, 'fullTime')) : 'Saat seç';
  },
  // fieldClearLabel: 'Clear value',

  // Table labels
  timeTableLabel: 'saat seç',
  dateTableLabel: 'tarih seç',
  // Field section placeholders
  fieldYearPlaceholder: function fieldYearPlaceholder(params) {
    return 'Y'.repeat(params.digitAmount);
  },
  fieldMonthPlaceholder: function fieldMonthPlaceholder(params) {
    return params.contentType === 'letter' ? 'AAA' : 'AA';
  },
  fieldDayPlaceholder: function fieldDayPlaceholder() {
    return 'GG';
  },
  fieldWeekDayPlaceholder: function fieldWeekDayPlaceholder(params) {
    return params.contentType === 'letter' ? 'HHH' : 'HH';
  },
  fieldHoursPlaceholder: function fieldHoursPlaceholder() {
    return 'ss';
  },
  fieldMinutesPlaceholder: function fieldMinutesPlaceholder() {
    return 'dd';
  },
  fieldSecondsPlaceholder: function fieldSecondsPlaceholder() {
    return 'ss';
  },
  fieldMeridiemPlaceholder: function fieldMeridiemPlaceholder() {
    return 'aa';
  }
};
export var trTR = getPickersLocalization(trTRPickers);