import { PolymorphicComponent } from '../utils/PolymorphicComponent';
import { SwitchTypeMap } from './Switch.types';
/**
 * The foundation for building custom-styled switches.
 *
 * Demos:
 *
 * - [Switch](https://mui.com/base-ui/react-switch/)
 *
 * API:
 *
 * - [Switch API](https://mui.com/base-ui/react-switch/components-api/#switch)
 */
declare const Switch: PolymorphicComponent<SwitchTypeMap<{}, "span">>;
export { Switch };
