{"ast": null, "code": "var _jsxFileName = \"C:\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\cavi\\\\CertificazioneCaviImproved.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, forwardRef, useImperativeHandle } from 'react';\nimport { Box, Typography, Button, Paper, Grid, Card, CardContent, Dialog, DialogTitle, DialogContent, DialogActions, TextField, FormControl, InputLabel, Select, MenuItem, Autocomplete, CircularProgress, Alert, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, IconButton, Tabs, Tab, Pagination, InputAdornment, Divider, Stack, Chip, Tooltip, Badge, LinearProgress, Collapse, List, ListItem, ListItemText, ListItemIcon, Snackbar, AppBar, Toolbar, Container, Fab, SpeedDial, SpeedDialAction, SpeedDialIcon } from '@mui/material';\nimport { Add as AddIcon, Search as SearchIcon, FilterList as FilterIcon, PictureAsPdf as PdfIcon, Download as DownloadIcon, Visibility as ViewIcon, Delete as DeleteIcon, Edit as EditIcon, Save as SaveIcon, Clear as ClearIcon, Build as BuildIcon, CheckCircle as CheckIcon, Warning as WarningIcon, GetApp as ExportIcon, Print as PrintIcon, Email as EmailIcon, CloudUpload as UploadIcon, Assessment as ReportIcon, Settings as SettingsIcon, Refresh as RefreshIcon, ExpandMore as ExpandMoreIcon, ExpandLess as ExpandLessIcon, Info as InfoIcon, Error as ErrorIcon, Schedule as ScheduleIcon, Person as PersonIcon, Cable as CableIcon, Science as ScienceIcon, Block as BlockIcon } from '@mui/icons-material';\nimport certificazioneService from '../../services/certificazioneService';\nimport caviService from '../../services/caviService';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst CertificazioneCaviImproved = /*#__PURE__*/_s(/*#__PURE__*/forwardRef(_c = _s(({\n  cantiereId,\n  onSuccess,\n  onError\n}, ref) => {\n  _s();\n  // Stati principali\n  const [loading, setLoading] = useState(false);\n  const [activeTab, setActiveTab] = useState(0);\n  const [certificazioni, setCertificazioni] = useState([]);\n  const [cavi, setCavi] = useState([]);\n  const [strumenti, setStrumenti] = useState([]);\n\n  // Stati per ricerca e filtri avanzati\n  const [searchTerm, setSearchTerm] = useState('');\n  const [filteredCavi, setFilteredCavi] = useState([]);\n  const [filteredCertificazioni, setFilteredCertificazioni] = useState([]);\n  const [advancedFiltersOpen, setAdvancedFiltersOpen] = useState(false);\n  const [filters, setFilters] = useState({\n    stato: '',\n    tipologia: '',\n    operatore: '',\n    dataInizio: '',\n    dataFine: '',\n    valoreIsolamento: '',\n    risultatoTest: '',\n    strumento: '',\n    certificazione: ''\n  });\n\n  // Stati per paginazione e ordinamento\n  const [currentPage, setCurrentPage] = useState(1);\n  const [itemsPerPage, setItemsPerPage] = useState(10);\n  const [sortBy, setSortBy] = useState('data_certificazione');\n  const [sortOrder, setSortOrder] = useState('desc');\n\n  // Stati per dialogs e modali\n  const [openDialog, setOpenDialog] = useState(false);\n  const [dialogType, setDialogType] = useState('');\n  const [selectedItem, setSelectedItem] = useState(null);\n  const [bulkSelection, setBulkSelection] = useState([]);\n  const [bulkMode, setBulkMode] = useState(false);\n\n  // Stati per notifiche e feedback\n  const [snackbar, setSnackbar] = useState({\n    open: false,\n    message: '',\n    severity: 'info'\n  });\n  const [progress, setProgress] = useState(0);\n  const [operationInProgress, setOperationInProgress] = useState(false);\n\n  // Stati per form certificazione avanzato\n  const [formData, setFormData] = useState({\n    id_cavo: '',\n    id_operatore: '',\n    id_strumento: '',\n    lunghezza_misurata: '',\n    valore_continuita: 'OK',\n    valore_isolamento: '',\n    valore_resistenza: 'OK',\n    note: '',\n    temperatura_ambiente: '',\n    umidita: '',\n    tensione_prova: '',\n    durata_prova: '',\n    risultato_finale: 'CONFORME'\n  });\n\n  // Stati per statistiche e dashboard\n  const [statistics, setStatistics] = useState({\n    totaleCavi: 0,\n    caviCertificati: 0,\n    caviNonCertificati: 0,\n    percentualeCompletamento: 0,\n    certificazioniOggi: 0,\n    certificazioniSettimana: 0\n  });\n\n  // Carica dati iniziali\n  useEffect(() => {\n    loadInitialData();\n  }, [cantiereId]);\n\n  // Filtra cavi in base alla ricerca\n  useEffect(() => {\n    filterCavi();\n  }, [cavi, searchTerm, filters, sortBy, sortOrder]);\n\n  // Filtra certificazioni\n  useEffect(() => {\n    filterCertificazioni();\n  }, [certificazioni, searchTerm, filters, sortBy, sortOrder]);\n\n  // Ricalcola statistiche quando cambiano i dati\n  useEffect(() => {\n    calculateStatistics();\n  }, [cavi, certificazioni]);\n  const loadInitialData = async () => {\n    try {\n      setLoading(true);\n      setProgress(0);\n\n      // Carica dati in sequenza con progress\n      setProgress(25);\n      await loadCavi();\n      setProgress(50);\n      await loadCertificazioni();\n      setProgress(75);\n      await loadStrumenti();\n      setProgress(100);\n      calculateStatistics();\n    } catch (error) {\n      showSnackbar('Errore nel caricamento dei dati iniziali', 'error');\n      onError('Errore nel caricamento dei dati iniziali');\n    } finally {\n      setLoading(false);\n      setProgress(0);\n    }\n  };\n  const loadCertificazioni = async () => {\n    try {\n      const data = await certificazioneService.getCertificazioni(cantiereId);\n      setCertificazioni(data);\n      return data;\n    } catch (error) {\n      console.error('Errore nel caricamento delle certificazioni:', error);\n      throw error;\n    }\n  };\n  const loadCavi = async () => {\n    try {\n      const data = await caviService.getCavi(cantiereId);\n      setCavi(data);\n      return data;\n    } catch (error) {\n      console.error('Errore nel caricamento dei cavi:', error);\n      throw error;\n    }\n  };\n  const loadStrumenti = async () => {\n    try {\n      const data = await certificazioneService.getStrumenti(cantiereId);\n      setStrumenti(data);\n      return data;\n    } catch (error) {\n      console.error('Errore nel caricamento degli strumenti:', error);\n      throw error;\n    }\n  };\n\n  // Calcola statistiche avanzate\n  const calculateStatistics = () => {\n    const totaleCavi = cavi.length;\n    const caviCertificati = certificazioni.length;\n    const caviNonCertificati = totaleCavi - caviCertificati;\n    const percentualeCompletamento = totaleCavi > 0 ? Math.round(caviCertificati / totaleCavi * 100) : 0;\n\n    // Calcola certificazioni di oggi\n    const oggi = new Date().toDateString();\n    const certificazioniOggi = certificazioni.filter(cert => new Date(cert.data_certificazione).toDateString() === oggi).length;\n\n    // Calcola certificazioni della settimana\n    const unaSettimanaFa = new Date();\n    unaSettimanaFa.setDate(unaSettimanaFa.getDate() - 7);\n    const certificazioniSettimana = certificazioni.filter(cert => new Date(cert.data_certificazione) >= unaSettimanaFa).length;\n\n    // Calcola cavi certificabili (solo posati/installati)\n    const caviCertificabili = cavi.filter(cavo => puoEssereCertificato(cavo)).length;\n    const caviNonCertificabili = totaleCavi - caviCertificabili;\n\n    // Calcola cavi collegati completamente\n    const caviCollegati = cavi.filter(cavo => isCavoCollegato(cavo)).length;\n    setStatistics({\n      totaleCavi,\n      caviCertificati,\n      caviNonCertificati,\n      caviCertificabili,\n      caviNonCertificabili,\n      caviCollegati,\n      percentualeCompletamento,\n      certificazioniOggi,\n      certificazioniSettimana\n    });\n  };\n\n  // Gestione snackbar\n  const showSnackbar = (message, severity = 'info') => {\n    setSnackbar({\n      open: true,\n      message,\n      severity\n    });\n  };\n  const closeSnackbar = () => {\n    setSnackbar({\n      ...snackbar,\n      open: false\n    });\n  };\n  const filterCavi = () => {\n    let filtered = cavi;\n\n    // Filtro per ricerca testuale avanzata\n    if (searchTerm) {\n      const searchLower = searchTerm.toLowerCase();\n      filtered = filtered.filter(cavo => {\n        var _cavo$tipologia, _cavo$ubicazione_part, _cavo$ubicazione_arri, _cavo$sezione, _cavo$utility;\n        return cavo.id_cavo.toLowerCase().includes(searchLower) || ((_cavo$tipologia = cavo.tipologia) === null || _cavo$tipologia === void 0 ? void 0 : _cavo$tipologia.toLowerCase().includes(searchLower)) || ((_cavo$ubicazione_part = cavo.ubicazione_partenza) === null || _cavo$ubicazione_part === void 0 ? void 0 : _cavo$ubicazione_part.toLowerCase().includes(searchLower)) || ((_cavo$ubicazione_arri = cavo.ubicazione_arrivo) === null || _cavo$ubicazione_arri === void 0 ? void 0 : _cavo$ubicazione_arri.toLowerCase().includes(searchLower)) || ((_cavo$sezione = cavo.sezione) === null || _cavo$sezione === void 0 ? void 0 : _cavo$sezione.toLowerCase().includes(searchLower)) || ((_cavo$utility = cavo.utility) === null || _cavo$utility === void 0 ? void 0 : _cavo$utility.toLowerCase().includes(searchLower));\n      });\n    }\n\n    // Filtri specifici avanzati\n    if (filters.stato) {\n      filtered = filtered.filter(cavo => cavo.stato_installazione === filters.stato);\n    }\n    if (filters.tipologia) {\n      filtered = filtered.filter(cavo => cavo.tipologia === filters.tipologia);\n    }\n\n    // Filtro per stato certificazione\n    if (filters.certificazione) {\n      if (filters.certificazione === 'CERTIFICATO') {\n        filtered = filtered.filter(cavo => certificazioni.some(cert => cert.id_cavo === cavo.id_cavo));\n      } else if (filters.certificazione === 'NON_CERTIFICATO') {\n        filtered = filtered.filter(cavo => !certificazioni.some(cert => cert.id_cavo === cavo.id_cavo));\n      }\n    }\n\n    // Ordinamento\n    filtered.sort((a, b) => {\n      let aValue = a[sortBy];\n      let bValue = b[sortBy];\n      if (typeof aValue === 'string') {\n        aValue = aValue.toLowerCase();\n        bValue = bValue.toLowerCase();\n      }\n      if (sortOrder === 'asc') {\n        return aValue > bValue ? 1 : -1;\n      } else {\n        return aValue < bValue ? 1 : -1;\n      }\n    });\n    setFilteredCavi(filtered);\n  };\n  const filterCertificazioni = () => {\n    let filtered = certificazioni;\n\n    // Ricerca testuale avanzata\n    if (searchTerm) {\n      const searchLower = searchTerm.toLowerCase();\n      filtered = filtered.filter(cert => {\n        var _cert$operatore, _cert$numero_certific, _cert$note;\n        return cert.id_cavo.toLowerCase().includes(searchLower) || ((_cert$operatore = cert.operatore) === null || _cert$operatore === void 0 ? void 0 : _cert$operatore.toLowerCase().includes(searchLower)) || ((_cert$numero_certific = cert.numero_certificato) === null || _cert$numero_certific === void 0 ? void 0 : _cert$numero_certific.toLowerCase().includes(searchLower)) || ((_cert$note = cert.note) === null || _cert$note === void 0 ? void 0 : _cert$note.toLowerCase().includes(searchLower));\n      });\n    }\n\n    // Filtri avanzati\n    if (filters.operatore) {\n      filtered = filtered.filter(cert => cert.operatore === filters.operatore);\n    }\n    if (filters.strumento) {\n      filtered = filtered.filter(cert => cert.strumento === filters.strumento);\n    }\n    if (filters.risultatoTest) {\n      filtered = filtered.filter(cert => cert.risultato_finale === filters.risultatoTest);\n    }\n    if (filters.dataInizio) {\n      filtered = filtered.filter(cert => new Date(cert.data_certificazione) >= new Date(filters.dataInizio));\n    }\n    if (filters.dataFine) {\n      filtered = filtered.filter(cert => new Date(cert.data_certificazione) <= new Date(filters.dataFine));\n    }\n    if (filters.valoreIsolamento) {\n      const valore = parseFloat(filters.valoreIsolamento);\n      filtered = filtered.filter(cert => parseFloat(cert.valore_isolamento) >= valore);\n    }\n\n    // Ordinamento\n    filtered.sort((a, b) => {\n      let aValue = a[sortBy];\n      let bValue = b[sortBy];\n      if (sortBy === 'data_certificazione') {\n        aValue = new Date(aValue);\n        bValue = new Date(bValue);\n      } else if (typeof aValue === 'string') {\n        aValue = aValue.toLowerCase();\n        bValue = bValue.toLowerCase();\n      }\n      if (sortOrder === 'asc') {\n        return aValue > bValue ? 1 : -1;\n      } else {\n        return aValue < bValue ? 1 : -1;\n      }\n    });\n    setFilteredCertificazioni(filtered);\n  };\n\n  // Gestione selezione multipla - SOLO per certificazioni\n  const toggleBulkMode = () => {\n    if (activeTab !== 1) {\n      showSnackbar('La selezione multipla è disponibile solo per le certificazioni', 'warning');\n      return;\n    }\n    setBulkMode(!bulkMode);\n    setBulkSelection([]);\n    showSnackbar(!bulkMode ? 'Modalità selezione attivata - Clicca sulle certificazioni per selezionarle' : 'Modalità selezione disattivata', 'info');\n  };\n  const toggleItemSelection = itemId => {\n    setBulkSelection(prev => {\n      const newSelection = prev.includes(itemId) ? prev.filter(id => id !== itemId) : [...prev, itemId];\n      showSnackbar(`${newSelection.length} certificazioni selezionate`, 'info');\n      return newSelection;\n    });\n  };\n  const selectAllItems = () => {\n    if (activeTab !== 1) return;\n    const allIds = filteredCertificazioni.map(cert => cert.id_certificazione);\n    setBulkSelection(allIds);\n    showSnackbar(`Tutte le ${allIds.length} certificazioni selezionate`, 'success');\n  };\n  const clearSelection = () => {\n    setBulkSelection([]);\n    showSnackbar('Selezione cancellata', 'info');\n  };\n\n  // Funzione per verificare se un cavo è certificato\n  const isCavoCertificato = idCavo => {\n    return certificazioni.some(cert => cert.id_cavo === idCavo);\n  };\n\n  // Funzione per verificare se un cavo può essere certificato\n  const puoEssereCertificato = cavo => {\n    // Verifica che il cavo sia installato/posato\n    const isInstallato = cavo.stato_installazione === 'Installato' || cavo.stato_installazione === 'INSTALLATO' || cavo.stato_installazione === 'POSATO';\n\n    // Per la certificazione basta che sia posato\n    // Il collegamento può essere gestito durante la certificazione\n    return isInstallato;\n  };\n\n  // Funzione per verificare se un cavo è completamente collegato\n  const isCavoCollegato = cavo => {\n    const isCollegato = cavo.collegamenti === 3;\n    const hasResponsabili = cavo.responsabile_partenza && cavo.responsabile_arrivo;\n    return isCollegato && hasResponsabili;\n  };\n\n  // Funzione per ottenere il messaggio di errore per cavi non certificabili\n  const getMessaggioErroreCertificazione = cavo => {\n    const isInstallato = cavo.stato_installazione === 'Installato' || cavo.stato_installazione === 'INSTALLATO' || cavo.stato_installazione === 'POSATO';\n    if (!isInstallato) {\n      return 'Il cavo deve essere posato/installato prima di poter essere certificato';\n    }\n    return 'Cavo non certificabile per motivi sconosciuti';\n  };\n\n  // Gestione tabs\n  const handleTabChange = (event, newValue) => {\n    setActiveTab(newValue);\n    setCurrentPage(1);\n    setSearchTerm('');\n    setFilters({\n      stato: '',\n      tipologia: '',\n      operatore: ''\n    });\n  };\n\n  // Gestione dialogs\n  const openCreateDialog = (cavoPreselezionato = null) => {\n    setDialogType('create');\n    setSelectedItem(null);\n\n    // Se viene passato un cavo, precompila il form\n    if (cavoPreselezionato) {\n      setFormData({\n        id_cavo: cavoPreselezionato.id_cavo,\n        id_operatore: '',\n        id_strumento: '',\n        lunghezza_misurata: cavoPreselezionato.metratura_reale || cavoPreselezionato.metri_teorici || '',\n        valore_continuita: 'OK',\n        valore_isolamento: '',\n        valore_resistenza: 'OK',\n        note: '',\n        temperatura_ambiente: '',\n        umidita: '',\n        tensione_prova: '',\n        durata_prova: '',\n        risultato_finale: 'CONFORME'\n      });\n      showSnackbar(`Cavo ${cavoPreselezionato.id_cavo} selezionato automaticamente`, 'success');\n    } else {\n      // Reset form per nuova certificazione generica\n      setFormData({\n        id_cavo: '',\n        id_operatore: '',\n        id_strumento: '',\n        lunghezza_misurata: '',\n        valore_continuita: 'OK',\n        valore_isolamento: '',\n        valore_resistenza: 'OK',\n        note: '',\n        temperatura_ambiente: '',\n        umidita: '',\n        tensione_prova: '',\n        durata_prova: '',\n        risultato_finale: 'CONFORME'\n      });\n    }\n    setOpenDialog(true);\n  };\n  const closeDialog = () => {\n    setOpenDialog(false);\n    setSelectedItem(null);\n    setDialogType('');\n  };\n\n  // Gestione form\n  const handleFormChange = (field, value) => {\n    setFormData(prev => ({\n      ...prev,\n      [field]: value\n    }));\n  };\n  const handleCavoSelect = cavo => {\n    setFormData(prev => ({\n      ...prev,\n      id_cavo: cavo.id_cavo,\n      lunghezza_misurata: cavo.metratura_reale || cavo.metri_teorici || ''\n    }));\n  };\n\n  // Operazioni CRUD avanzate\n  const handleCreateCertificazione = async () => {\n    try {\n      if (!formData.id_cavo || !formData.id_operatore || !formData.id_strumento || !formData.valore_isolamento) {\n        showSnackbar('Compila tutti i campi obbligatori', 'warning');\n        return;\n      }\n\n      // Verifica che il cavo possa essere certificato\n      const cavo = cavi.find(c => c.id_cavo === formData.id_cavo);\n      if (!cavo) {\n        showSnackbar('Cavo non trovato', 'error');\n        return;\n      }\n      if (!puoEssereCertificato(cavo)) {\n        const messaggio = getMessaggioErroreCertificazione(cavo);\n        showSnackbar(`Impossibile certificare il cavo: ${messaggio}`, 'error');\n        return;\n      }\n\n      // Verifica che il cavo non sia già certificato\n      if (isCavoCertificato(formData.id_cavo)) {\n        showSnackbar('Il cavo è già stato certificato', 'warning');\n        return;\n      }\n\n      // Verifica se il cavo è collegato, altrimenti chiedi conferma\n      if (!isCavoCollegato(cavo)) {\n        const conferma = window.confirm(`ATTENZIONE: Il cavo ${cavo.id_cavo} non risulta completamente collegato.\\n\\n` + `Stato collegamenti: ${cavo.collegamenti === 0 ? 'Non collegato' : cavo.collegamenti === 1 ? 'Solo partenza collegata' : cavo.collegamenti === 2 ? 'Solo arrivo collegato' : 'Stato sconosciuto'}\\n\\n` + `Vuoi procedere comunque con la certificazione?\\n` + `(Ricorda di completare i collegamenti prima della messa in servizio)`);\n        if (!conferma) {\n          return;\n        }\n      }\n      setOperationInProgress(true);\n      await certificazioneService.createCertificazione(cantiereId, formData);\n      showSnackbar('Certificazione creata con successo', 'success');\n      closeDialog();\n      await loadCertificazioni();\n      calculateStatistics();\n    } catch (error) {\n      showSnackbar('Errore nella creazione della certificazione: ' + (error.message || 'Errore sconosciuto'), 'error');\n    } finally {\n      setOperationInProgress(false);\n    }\n  };\n  const handleGeneratePdf = async certificazione => {\n    try {\n      setOperationInProgress(true);\n      showSnackbar('Generazione PDF in corso...', 'info');\n      const response = await certificazioneService.generatePdf(cantiereId, certificazione.id_certificazione);\n      if (response.file_url) {\n        // Apri il PDF in una nuova finestra\n        const newWindow = window.open(response.file_url, '_blank');\n        if (newWindow) {\n          showSnackbar('PDF generato e aperto in una nuova finestra', 'success');\n        } else {\n          // Se il popup è bloccato, offri il download diretto\n          const link = document.createElement('a');\n          link.href = response.file_url;\n          link.download = `certificazione_${certificazione.numero_certificato}.pdf`;\n          document.body.appendChild(link);\n          link.click();\n          document.body.removeChild(link);\n          showSnackbar('PDF scaricato nella cartella Download', 'success');\n        }\n      } else if (response.pdf_content) {\n        // Se il PDF viene restituito come contenuto base64\n        const blob = new Blob([atob(response.pdf_content)], {\n          type: 'application/pdf'\n        });\n        const url = URL.createObjectURL(blob);\n        const link = document.createElement('a');\n        link.href = url;\n        link.download = `certificazione_${certificazione.numero_certificato}.pdf`;\n        document.body.appendChild(link);\n        link.click();\n        document.body.removeChild(link);\n        URL.revokeObjectURL(url);\n        showSnackbar('PDF scaricato con successo', 'success');\n      } else {\n        showSnackbar('Errore: Formato PDF non riconosciuto', 'error');\n      }\n    } catch (error) {\n      console.error('Errore generazione PDF:', error);\n      showSnackbar('Errore nella generazione del PDF: ' + (error.message || 'Errore sconosciuto'), 'error');\n    } finally {\n      setOperationInProgress(false);\n    }\n  };\n  const handleDeleteCertificazione = async certificazione => {\n    if (window.confirm(`Sei sicuro di voler eliminare la certificazione ${certificazione.numero_certificato}?`)) {\n      try {\n        setOperationInProgress(true);\n        await certificazioneService.deleteCertificazione(cantiereId, certificazione.id_certificazione);\n        showSnackbar('Certificazione eliminata con successo', 'success');\n        await loadCertificazioni();\n        calculateStatistics();\n      } catch (error) {\n        showSnackbar('Errore nell\\'eliminazione della certificazione: ' + (error.message || 'Errore sconosciuto'), 'error');\n      } finally {\n        setOperationInProgress(false);\n      }\n    }\n  };\n\n  // Operazioni bulk\n  const handleBulkDelete = async () => {\n    if (bulkSelection.length === 0) {\n      showSnackbar('Seleziona almeno un elemento', 'warning');\n      return;\n    }\n    if (window.confirm(`Sei sicuro di voler eliminare ${bulkSelection.length} certificazioni?`)) {\n      try {\n        setOperationInProgress(true);\n        for (const id of bulkSelection) {\n          await certificazioneService.deleteCertificazione(cantiereId, id);\n        }\n        showSnackbar(`${bulkSelection.length} certificazioni eliminate con successo`, 'success');\n        setBulkSelection([]);\n        await loadCertificazioni();\n        calculateStatistics();\n      } catch (error) {\n        showSnackbar('Errore nell\\'eliminazione delle certificazioni', 'error');\n      } finally {\n        setOperationInProgress(false);\n      }\n    }\n  };\n  const handleBulkExport = async () => {\n    if (bulkSelection.length === 0) {\n      showSnackbar('Seleziona almeno un elemento', 'warning');\n      return;\n    }\n    try {\n      setOperationInProgress(true);\n      // Implementa export bulk\n      const selectedCerts = certificazioni.filter(cert => bulkSelection.includes(cert.id_certificazione));\n\n      // Crea CSV\n      const csvContent = generateCSV(selectedCerts);\n      downloadCSV(csvContent, `certificazioni_${new Date().toISOString().split('T')[0]}.csv`);\n      showSnackbar(`${bulkSelection.length} certificazioni esportate`, 'success');\n    } catch (error) {\n      showSnackbar('Errore nell\\'esportazione', 'error');\n    } finally {\n      setOperationInProgress(false);\n    }\n  };\n\n  // Funzioni di export\n  const generateCSV = data => {\n    const headers = ['ID Cavo', 'Numero Certificato', 'Data', 'Operatore', 'Strumento', 'Lunghezza', 'Isolamento', 'Risultato'];\n    const rows = data.map(cert => [cert.id_cavo, cert.numero_certificato, new Date(cert.data_certificazione).toLocaleDateString(), cert.operatore, cert.strumento, cert.lunghezza_misurata, cert.valore_isolamento, cert.risultato_finale]);\n    return [headers, ...rows].map(row => row.join(',')).join('\\n');\n  };\n  const downloadCSV = (content, filename) => {\n    const blob = new Blob([content], {\n      type: 'text/csv;charset=utf-8;'\n    });\n    const link = document.createElement('a');\n    if (link.download !== undefined) {\n      const url = URL.createObjectURL(blob);\n      link.setAttribute('href', url);\n      link.setAttribute('download', filename);\n      link.style.visibility = 'hidden';\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n    }\n  };\n  const handleExportAll = () => {\n    const csvContent = generateCSV(filteredCertificazioni);\n    downloadCSV(csvContent, `tutte_certificazioni_${new Date().toISOString().split('T')[0]}.csv`);\n    showSnackbar('Esportazione completata', 'success');\n  };\n\n  // Espone metodi tramite ref\n  useImperativeHandle(ref, () => ({\n    handleOptionSelect: option => {\n      if (option === 'creaCertificazione') {\n        openCreateDialog();\n      } else if (option === 'visualizzaCertificazioni') {\n        setActiveTab(1);\n      }\n    }\n  }));\n\n  // Calcola elementi per paginazione\n  const getCurrentPageItems = items => {\n    const startIndex = (currentPage - 1) * itemsPerPage;\n    const endIndex = startIndex + itemsPerPage;\n    return items.slice(startIndex, endIndex);\n  };\n  const getTotalPages = items => Math.ceil(items.length / itemsPerPage);\n\n  // Ottieni opzioni uniche per filtri\n  const getUniqueValues = (array, field) => {\n    return [...new Set(array.map(item => item[field]).filter(Boolean))];\n  };\n\n  // Componente Dashboard con statistiche\n  const renderDashboard = () => /*#__PURE__*/_jsxDEV(Grid, {\n    container: true,\n    spacing: 3,\n    sx: {\n      mb: 3\n    },\n    children: [/*#__PURE__*/_jsxDEV(Grid, {\n      item: true,\n      xs: 12,\n      md: 2,\n      children: /*#__PURE__*/_jsxDEV(Card, {\n        sx: {\n          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n          color: 'white'\n        },\n        children: /*#__PURE__*/_jsxDEV(CardContent, {\n          sx: {\n            textAlign: 'center',\n            py: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(CableIcon, {\n            sx: {\n              fontSize: 40,\n              mb: 1\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 799,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h4\",\n            fontWeight: \"bold\",\n            children: statistics.totaleCavi\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 800,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            children: \"Totale Cavi\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 803,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 798,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 797,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 796,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      item: true,\n      xs: 12,\n      md: 2,\n      children: /*#__PURE__*/_jsxDEV(Card, {\n        sx: {\n          background: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',\n          color: 'white'\n        },\n        children: /*#__PURE__*/_jsxDEV(CardContent, {\n          sx: {\n            textAlign: 'center',\n            py: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(CheckIcon, {\n            sx: {\n              fontSize: 40,\n              mb: 1\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 813,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h4\",\n            fontWeight: \"bold\",\n            children: statistics.caviCertificati\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 814,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            children: \"Certificati\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 817,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 812,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 811,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 810,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      item: true,\n      xs: 12,\n      md: 2,\n      children: /*#__PURE__*/_jsxDEV(Card, {\n        sx: {\n          background: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',\n          color: 'white'\n        },\n        children: /*#__PURE__*/_jsxDEV(CardContent, {\n          sx: {\n            textAlign: 'center',\n            py: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(BuildIcon, {\n            sx: {\n              fontSize: 40,\n              mb: 1\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 827,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h4\",\n            fontWeight: \"bold\",\n            children: statistics.caviCertificabili\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 828,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            children: \"Certificabili\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 831,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 826,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 825,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 824,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      item: true,\n      xs: 12,\n      md: 2,\n      children: /*#__PURE__*/_jsxDEV(Card, {\n        sx: {\n          background: 'linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%)',\n          color: '#333'\n        },\n        children: /*#__PURE__*/_jsxDEV(CardContent, {\n          sx: {\n            textAlign: 'center',\n            py: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(CheckIcon, {\n            sx: {\n              fontSize: 40,\n              mb: 1\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 841,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h4\",\n            fontWeight: \"bold\",\n            children: statistics.caviCollegati\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 842,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            children: \"Collegati\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 845,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 840,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 839,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 838,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      item: true,\n      xs: 12,\n      md: 2,\n      children: /*#__PURE__*/_jsxDEV(Card, {\n        sx: {\n          background: 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',\n          color: 'white'\n        },\n        children: /*#__PURE__*/_jsxDEV(CardContent, {\n          sx: {\n            textAlign: 'center',\n            py: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(ReportIcon, {\n            sx: {\n              fontSize: 40,\n              mb: 1\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 855,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h4\",\n            fontWeight: \"bold\",\n            children: [statistics.percentualeCompletamento, \"%\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 856,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            children: \"Completamento\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 859,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 854,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 853,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 852,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      item: true,\n      xs: 12,\n      md: 2,\n      children: /*#__PURE__*/_jsxDEV(Card, {\n        sx: {\n          background: 'linear-gradient(135deg, #fa709a 0%, #fee140 100%)',\n          color: 'white'\n        },\n        children: /*#__PURE__*/_jsxDEV(CardContent, {\n          sx: {\n            textAlign: 'center',\n            py: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(ScheduleIcon, {\n            sx: {\n              fontSize: 40,\n              mb: 1\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 869,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h4\",\n            fontWeight: \"bold\",\n            children: statistics.certificazioniOggi\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 870,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            children: \"Oggi\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 873,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 868,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 867,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 866,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      item: true,\n      xs: 12,\n      md: 2,\n      children: /*#__PURE__*/_jsxDEV(Card, {\n        sx: {\n          background: 'linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)',\n          color: '#333'\n        },\n        children: /*#__PURE__*/_jsxDEV(CardContent, {\n          sx: {\n            textAlign: 'center',\n            py: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(ScienceIcon, {\n            sx: {\n              fontSize: 40,\n              mb: 1\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 883,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h4\",\n            fontWeight: \"bold\",\n            children: statistics.certificazioniSettimana\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 884,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            children: \"Questa Settimana\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 887,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 882,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 881,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 880,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 795,\n    columnNumber: 5\n  }, this);\n\n  // Componente barra di ricerca avanzata\n  const renderSearchAndFilters = () => /*#__PURE__*/_jsxDEV(Paper, {\n    sx: {\n      p: 2,\n      mb: 3\n    },\n    children: [/*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 2,\n      alignItems: \"center\",\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 4,\n        children: /*#__PURE__*/_jsxDEV(TextField, {\n          fullWidth: true,\n          placeholder: \"Cerca cavi, certificazioni, operatori...\",\n          value: searchTerm,\n          onChange: e => setSearchTerm(e.target.value),\n          InputProps: {\n            startAdornment: /*#__PURE__*/_jsxDEV(InputAdornment, {\n              position: \"start\",\n              children: /*#__PURE__*/_jsxDEV(SearchIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 909,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 908,\n              columnNumber: 17\n            }, this),\n            endAdornment: searchTerm && /*#__PURE__*/_jsxDEV(InputAdornment, {\n              position: \"end\",\n              children: /*#__PURE__*/_jsxDEV(IconButton, {\n                onClick: () => setSearchTerm(''),\n                size: \"small\",\n                children: /*#__PURE__*/_jsxDEV(ClearIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 915,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 914,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 913,\n              columnNumber: 17\n            }, this)\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 901,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 900,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 2,\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          fullWidth: true,\n          variant: \"outlined\",\n          startIcon: /*#__PURE__*/_jsxDEV(FilterIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 927,\n            columnNumber: 24\n          }, this),\n          onClick: () => setAdvancedFiltersOpen(!advancedFiltersOpen),\n          color: Object.values(filters).some(f => f) ? 'primary' : 'inherit',\n          children: [\"Filtri \", Object.values(filters).filter(f => f).length > 0 && `(${Object.values(filters).filter(f => f).length})`]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 924,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 923,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 2,\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          fullWidth: true,\n          variant: \"outlined\",\n          startIcon: bulkMode ? /*#__PURE__*/_jsxDEV(ClearIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 939,\n            columnNumber: 35\n          }, this) : /*#__PURE__*/_jsxDEV(CheckIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 939,\n            columnNumber: 51\n          }, this),\n          onClick: toggleBulkMode,\n          color: bulkMode ? 'secondary' : 'inherit',\n          disabled: activeTab === 0,\n          children: bulkMode ? 'Esci Selezione' : 'Selezione Multipla'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 936,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 935,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 2,\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          fullWidth: true,\n          variant: \"outlined\",\n          startIcon: /*#__PURE__*/_jsxDEV(ExportIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 952,\n            columnNumber: 24\n          }, this),\n          onClick: handleExportAll,\n          disabled: activeTab === 0 || filteredCertificazioni.length === 0,\n          children: activeTab === 0 ? 'Export (solo certificazioni)' : 'Esporta Certificazioni'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 949,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 948,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 2,\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          fullWidth: true,\n          variant: \"contained\",\n          startIcon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 964,\n            columnNumber: 24\n          }, this),\n          onClick: openCreateDialog,\n          children: \"Nuova Certificazione\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 961,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 960,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 899,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Collapse, {\n      in: advancedFiltersOpen,\n      children: [/*#__PURE__*/_jsxDEV(Divider, {\n        sx: {\n          my: 2\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 974,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body2\",\n        color: \"text.secondary\",\n        sx: {\n          mb: 2\n        },\n        children: activeTab === 0 ? 'Filtri per Cavi' : 'Filtri per Certificazioni'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 975,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 2,\n        children: [activeTab === 0 && /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(FormControl, {\n              fullWidth: true,\n              size: \"small\",\n              children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                children: \"Stato Installazione\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 985,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Select, {\n                value: filters.stato,\n                onChange: e => setFilters({\n                  ...filters,\n                  stato: e.target.value\n                }),\n                children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"\",\n                  children: \"Tutti\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 990,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"INSTALLATO\",\n                  children: \"Installato\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 991,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"NON_INSTALLATO\",\n                  children: \"Non Installato\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 992,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"IN_CORSO\",\n                  children: \"In Corso\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 993,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 986,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 984,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 983,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(FormControl, {\n              fullWidth: true,\n              size: \"small\",\n              children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                children: \"Tipologia\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1000,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Select, {\n                value: filters.tipologia,\n                onChange: e => setFilters({\n                  ...filters,\n                  tipologia: e.target.value\n                }),\n                children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"\",\n                  children: \"Tutte\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1005,\n                  columnNumber: 21\n                }, this), [...new Set(cavi.map(c => c.tipologia))].filter(Boolean).map(tip => /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: tip,\n                  children: tip\n                }, tip, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1007,\n                  columnNumber: 23\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1001,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 999,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 998,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(FormControl, {\n              fullWidth: true,\n              size: \"small\",\n              children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                children: \"Stato Certificazione\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1015,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Select, {\n                value: filters.certificazione,\n                onChange: e => setFilters({\n                  ...filters,\n                  certificazione: e.target.value\n                }),\n                children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"\",\n                  children: \"Tutti\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1020,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"CERTIFICATO\",\n                  children: \"Certificato\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1021,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"NON_CERTIFICATO\",\n                  children: \"Non Certificato\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1022,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1016,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1014,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1013,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true), activeTab === 1 && /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(FormControl, {\n              fullWidth: true,\n              size: \"small\",\n              children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                children: \"Operatore\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1034,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Select, {\n                value: filters.operatore,\n                onChange: e => setFilters({\n                  ...filters,\n                  operatore: e.target.value\n                }),\n                children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"\",\n                  children: \"Tutti\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1039,\n                  columnNumber: 21\n                }, this), [...new Set(certificazioni.map(c => c.operatore))].filter(Boolean).map(op => /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: op,\n                  children: op\n                }, op, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1041,\n                  columnNumber: 23\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1035,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1033,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1032,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(FormControl, {\n              fullWidth: true,\n              size: \"small\",\n              children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                children: \"Risultato Test\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1049,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Select, {\n                value: filters.risultatoTest,\n                onChange: e => setFilters({\n                  ...filters,\n                  risultatoTest: e.target.value\n                }),\n                children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"\",\n                  children: \"Tutti\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1054,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"CONFORME\",\n                  children: \"Conforme\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1055,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"NON_CONFORME\",\n                  children: \"Non Conforme\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1056,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"DA_VERIFICARE\",\n                  children: \"Da Verificare\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1057,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1050,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1048,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1047,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              size: \"small\",\n              label: \"Isolamento Min (M\\u03A9)\",\n              type: \"number\",\n              value: filters.valoreIsolamento,\n              onChange: e => setFilters({\n                ...filters,\n                valoreIsolamento: e.target.value\n              }),\n              placeholder: \"es. 500\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1063,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1062,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              size: \"small\",\n              label: \"Data Inizio\",\n              type: \"date\",\n              value: filters.dataInizio,\n              onChange: e => setFilters({\n                ...filters,\n                dataInizio: e.target.value\n              }),\n              InputLabelProps: {\n                shrink: true\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1075,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1074,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              size: \"small\",\n              label: \"Data Fine\",\n              type: \"date\",\n              value: filters.dataFine,\n              onChange: e => setFilters({\n                ...filters,\n                dataFine: e.target.value\n              }),\n              InputLabelProps: {\n                shrink: true\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1087,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1086,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          children: /*#__PURE__*/_jsxDEV(Stack, {\n            direction: \"row\",\n            spacing: 1,\n            justifyContent: \"flex-end\",\n            children: /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"outlined\",\n              size: \"small\",\n              onClick: () => setFilters({\n                stato: '',\n                tipologia: '',\n                operatore: '',\n                dataInizio: '',\n                dataFine: '',\n                valoreIsolamento: '',\n                risultatoTest: '',\n                strumento: '',\n                certificazione: ''\n              }),\n              children: \"Pulisci Tutti i Filtri\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1102,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1101,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1100,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 979,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 973,\n      columnNumber: 7\n    }, this), bulkMode && bulkSelection.length > 0 && /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(Divider, {\n        sx: {\n          my: 2\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1121,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Stack, {\n        direction: \"row\",\n        spacing: 2,\n        alignItems: \"center\",\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          children: [bulkSelection.length, \" elementi selezionati\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1123,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          size: \"small\",\n          variant: \"outlined\",\n          onClick: selectAllItems,\n          children: \"Seleziona Tutto\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1126,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          size: \"small\",\n          variant: \"outlined\",\n          onClick: clearSelection,\n          children: \"Deseleziona\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1133,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          size: \"small\",\n          variant: \"outlined\",\n          startIcon: /*#__PURE__*/_jsxDEV(ExportIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1143,\n            columnNumber: 26\n          }, this),\n          onClick: handleBulkExport,\n          children: \"Esporta Selezionati\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1140,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          size: \"small\",\n          variant: \"outlined\",\n          color: \"error\",\n          startIcon: /*#__PURE__*/_jsxDEV(DeleteIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1152,\n            columnNumber: 26\n          }, this),\n          onClick: handleBulkDelete,\n          children: \"Elimina Selezionati\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1148,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1122,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 898,\n    columnNumber: 5\n  }, this);\n\n  // Renderizza la tabella dei cavi\n  const renderCaviTable = () => {\n    const currentItems = getCurrentPageItems(filteredCavi);\n    if (filteredCavi.length === 0) {\n      return /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"info\",\n        children: searchTerm || filters.stato || filters.tipologia ? 'Nessun cavo trovato con i filtri applicati' : 'Nessun cavo disponibile'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1169,\n        columnNumber: 9\n      }, this);\n    }\n    return /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(TableContainer, {\n        component: Paper,\n        children: /*#__PURE__*/_jsxDEV(Table, {\n          size: \"small\",\n          children: [/*#__PURE__*/_jsxDEV(TableHead, {\n            children: /*#__PURE__*/_jsxDEV(TableRow, {\n              children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"ID Cavo\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1183,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Tipologia\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1184,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Sezione\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1185,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Partenza\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1186,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Arrivo\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1187,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Metri\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1188,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Stato\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1189,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Collegamenti\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1190,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Certificato\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1191,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Azioni\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1192,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1182,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1181,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n            children: currentItems.map(cavo => {\n              const isCertificato = isCavoCertificato(cavo.id_cavo);\n              const puoCertificare = puoEssereCertificato(cavo);\n              const messaggioErrore = !puoCertificare ? getMessaggioErroreCertificazione(cavo) : '';\n              return /*#__PURE__*/_jsxDEV(TableRow, {\n                children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                  children: /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    fontWeight: \"medium\",\n                    children: cavo.id_cavo\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1204,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1203,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: cavo.tipologia\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1208,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: cavo.sezione\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1209,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: cavo.ubicazione_partenza\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1210,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: cavo.ubicazione_arrivo\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1211,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: [cavo.metratura_reale || cavo.metri_teorici, \" m\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1212,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: /*#__PURE__*/_jsxDEV(Chip, {\n                    size: \"small\",\n                    label: cavo.stato_installazione,\n                    color: cavo.stato_installazione === 'INSTALLATO' ? 'success' : 'default'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1214,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1213,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: (() => {\n                    const collegamenti = cavo.collegamenti || 0;\n                    const statoCollegamento = collegamenti === 0 ? 'Non collegato' : collegamenti === 1 ? 'Solo partenza' : collegamenti === 2 ? 'Solo arrivo' : collegamenti === 3 ? 'Completo' : 'Sconosciuto';\n                    const colore = collegamenti === 3 ? 'success' : collegamenti === 0 ? 'error' : 'warning';\n                    return /*#__PURE__*/_jsxDEV(Tooltip, {\n                      title: `Partenza: ${cavo.responsabile_partenza || 'Non collegato'} | Arrivo: ${cavo.responsabile_arrivo || 'Non collegato'}`,\n                      children: /*#__PURE__*/_jsxDEV(Chip, {\n                        size: \"small\",\n                        label: statoCollegamento,\n                        color: colore,\n                        icon: collegamenti === 3 ? /*#__PURE__*/_jsxDEV(CheckIcon, {}, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1237,\n                          columnNumber: 58\n                        }, this) : /*#__PURE__*/_jsxDEV(WarningIcon, {}, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1237,\n                          columnNumber: 74\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1233,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1232,\n                      columnNumber: 27\n                    }, this);\n                  })()\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1220,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: isCertificato ? /*#__PURE__*/_jsxDEV(Chip, {\n                    size: \"small\",\n                    icon: /*#__PURE__*/_jsxDEV(CheckIcon, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1247,\n                      columnNumber: 33\n                    }, this),\n                    label: \"Certificato\",\n                    color: \"success\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1245,\n                    columnNumber: 25\n                  }, this) : /*#__PURE__*/_jsxDEV(Chip, {\n                    size: \"small\",\n                    icon: /*#__PURE__*/_jsxDEV(WarningIcon, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1254,\n                      columnNumber: 33\n                    }, this),\n                    label: \"Non certificato\",\n                    color: \"warning\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1252,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1243,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: isCertificato ? /*#__PURE__*/_jsxDEV(Tooltip, {\n                    title: \"Cavo gi\\xE0 certificato\",\n                    children: /*#__PURE__*/_jsxDEV(Chip, {\n                      icon: /*#__PURE__*/_jsxDEV(CheckIcon, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1264,\n                        columnNumber: 35\n                      }, this),\n                      label: \"Certificato\",\n                      color: \"success\",\n                      size: \"small\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1263,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1262,\n                    columnNumber: 25\n                  }, this) : puoCertificare ? /*#__PURE__*/_jsxDEV(Tooltip, {\n                    title: \"Crea certificazione per questo cavo\",\n                    children: /*#__PURE__*/_jsxDEV(IconButton, {\n                      size: \"small\",\n                      onClick: () => openCreateDialog(cavo),\n                      color: \"primary\",\n                      children: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1277,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1272,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1271,\n                    columnNumber: 25\n                  }, this) : /*#__PURE__*/_jsxDEV(Tooltip, {\n                    title: messaggioErrore,\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: /*#__PURE__*/_jsxDEV(IconButton, {\n                        size: \"small\",\n                        disabled: true,\n                        onClick: () => showSnackbar(messaggioErrore, 'warning'),\n                        children: /*#__PURE__*/_jsxDEV(BlockIcon, {}, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1288,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1283,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1282,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1281,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1260,\n                  columnNumber: 21\n                }, this)]\n              }, cavo.id_cavo, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1202,\n                columnNumber: 19\n              }, this);\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1195,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1180,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1179,\n        columnNumber: 9\n      }, this), getTotalPages(filteredCavi) > 1 && /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          justifyContent: 'center',\n          mt: 2\n        },\n        children: /*#__PURE__*/_jsxDEV(Pagination, {\n          count: getTotalPages(filteredCavi),\n          page: currentPage,\n          onChange: (event, value) => setCurrentPage(value),\n          color: \"primary\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1303,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1302,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true);\n  };\n\n  // Renderizza la tabella delle certificazioni\n  const renderCertificazioniTable = () => {\n    const currentItems = getCurrentPageItems(filteredCertificazioni);\n    if (filteredCertificazioni.length === 0) {\n      return /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"info\",\n        children: searchTerm || filters.operatore ? 'Nessuna certificazione trovata con i filtri applicati' : 'Nessuna certificazione disponibile'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1321,\n        columnNumber: 9\n      }, this);\n    }\n    return /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(TableContainer, {\n        component: Paper,\n        children: /*#__PURE__*/_jsxDEV(Table, {\n          size: \"small\",\n          children: [/*#__PURE__*/_jsxDEV(TableHead, {\n            children: /*#__PURE__*/_jsxDEV(TableRow, {\n              children: [bulkMode && /*#__PURE__*/_jsxDEV(TableCell, {\n                padding: \"checkbox\",\n                children: /*#__PURE__*/_jsxDEV(IconButton, {\n                  size: \"small\",\n                  onClick: bulkSelection.length === filteredCertificazioni.length ? clearSelection : selectAllItems,\n                  children: bulkSelection.length === filteredCertificazioni.length ? /*#__PURE__*/_jsxDEV(ClearIcon, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1341,\n                    columnNumber: 81\n                  }, this) : /*#__PURE__*/_jsxDEV(CheckIcon, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1341,\n                    columnNumber: 97\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1337,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1336,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: /*#__PURE__*/_jsxDEV(Stack, {\n                  direction: \"row\",\n                  alignItems: \"center\",\n                  spacing: 1,\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    fontWeight: \"bold\",\n                    children: \"N\\xB0 Certificato\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1347,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n                    size: \"small\",\n                    onClick: () => {\n                      setSortBy('numero_certificato');\n                      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');\n                    },\n                    children: sortBy === 'numero_certificato' ? sortOrder === 'asc' ? /*#__PURE__*/_jsxDEV(ExpandLessIcon, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1352,\n                      columnNumber: 81\n                    }, this) : /*#__PURE__*/_jsxDEV(ExpandMoreIcon, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1352,\n                      columnNumber: 102\n                    }, this) : /*#__PURE__*/_jsxDEV(ExpandMoreIcon, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1352,\n                      columnNumber: 124\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1348,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1346,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1345,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"ID Cavo\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1356,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: /*#__PURE__*/_jsxDEV(Stack, {\n                  direction: \"row\",\n                  alignItems: \"center\",\n                  spacing: 1,\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    fontWeight: \"bold\",\n                    children: \"Data\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1359,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n                    size: \"small\",\n                    onClick: () => {\n                      setSortBy('data_certificazione');\n                      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');\n                    },\n                    children: sortBy === 'data_certificazione' ? sortOrder === 'asc' ? /*#__PURE__*/_jsxDEV(ExpandLessIcon, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1364,\n                      columnNumber: 82\n                    }, this) : /*#__PURE__*/_jsxDEV(ExpandMoreIcon, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1364,\n                      columnNumber: 103\n                    }, this) : /*#__PURE__*/_jsxDEV(ExpandMoreIcon, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1364,\n                      columnNumber: 125\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1360,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1358,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1357,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Operatore\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1368,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Strumento\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1369,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Lunghezza\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1370,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Isolamento\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1371,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Risultato\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1372,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Azioni\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1373,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1334,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1333,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n            children: currentItems.map(cert => /*#__PURE__*/_jsxDEV(TableRow, {\n              selected: bulkSelection.includes(cert.id_certificazione),\n              hover: true,\n              children: [bulkMode && /*#__PURE__*/_jsxDEV(TableCell, {\n                padding: \"checkbox\",\n                children: /*#__PURE__*/_jsxDEV(IconButton, {\n                  size: \"small\",\n                  onClick: () => toggleItemSelection(cert.id_certificazione),\n                  color: bulkSelection.includes(cert.id_certificazione) ? 'primary' : 'default',\n                  children: bulkSelection.includes(cert.id_certificazione) ? /*#__PURE__*/_jsxDEV(CheckIcon, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1390,\n                    columnNumber: 75\n                  }, this) : /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1390,\n                    columnNumber: 91\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1385,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1384,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  fontWeight: \"medium\",\n                  children: cert.numero_certificato\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1395,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1394,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: /*#__PURE__*/_jsxDEV(Chip, {\n                  size: \"small\",\n                  label: cert.id_cavo,\n                  variant: \"outlined\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1400,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1399,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: new Date(cert.data_certificazione).toLocaleDateString()\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1402,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: /*#__PURE__*/_jsxDEV(Stack, {\n                  direction: \"row\",\n                  alignItems: \"center\",\n                  spacing: 1,\n                  children: [/*#__PURE__*/_jsxDEV(PersonIcon, {\n                    fontSize: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1405,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    children: cert.operatore || cert.id_operatore\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1406,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1404,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1403,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  children: cert.id_strumento ? (() => {\n                    const strumento = strumenti.find(s => s.id_strumento === cert.id_strumento);\n                    return strumento ? `${strumento.nome} - ${strumento.marca}` : 'Strumento non trovato';\n                  })() : cert.strumento_utilizzato || 'N/A'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1410,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1409,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  children: [cert.lunghezza_misurata, \" m\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1421,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1420,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: /*#__PURE__*/_jsxDEV(Chip, {\n                  size: \"small\",\n                  label: `${cert.valore_isolamento} MΩ`,\n                  color: parseFloat(cert.valore_isolamento) >= 500 ? 'success' : 'warning',\n                  icon: parseFloat(cert.valore_isolamento) >= 500 ? /*#__PURE__*/_jsxDEV(CheckIcon, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1428,\n                    columnNumber: 73\n                  }, this) : /*#__PURE__*/_jsxDEV(WarningIcon, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1428,\n                    columnNumber: 89\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1424,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1423,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: /*#__PURE__*/_jsxDEV(Chip, {\n                  size: \"small\",\n                  label: cert.risultato_finale || 'CONFORME',\n                  color: cert.risultato_finale === 'CONFORME' ? 'success' : cert.risultato_finale === 'NON_CONFORME' ? 'error' : 'warning'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1432,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1431,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: /*#__PURE__*/_jsxDEV(Stack, {\n                  direction: \"row\",\n                  spacing: 0.5,\n                  children: [/*#__PURE__*/_jsxDEV(Tooltip, {\n                    title: \"Visualizza dettagli\",\n                    children: /*#__PURE__*/_jsxDEV(IconButton, {\n                      size: \"small\",\n                      onClick: () => {\n                        setSelectedItem(cert);\n                        setDialogType('view');\n                        setOpenDialog(true);\n                      },\n                      children: /*#__PURE__*/_jsxDEV(ViewIcon, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1449,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1441,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1440,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                    title: \"Genera PDF\",\n                    children: /*#__PURE__*/_jsxDEV(IconButton, {\n                      size: \"small\",\n                      onClick: () => handleGeneratePdf(cert),\n                      disabled: operationInProgress,\n                      children: /*#__PURE__*/_jsxDEV(PdfIcon, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1458,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1453,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1452,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                    title: \"Elimina\",\n                    children: /*#__PURE__*/_jsxDEV(IconButton, {\n                      size: \"small\",\n                      color: \"error\",\n                      onClick: () => handleDeleteCertificazione(cert),\n                      disabled: operationInProgress,\n                      children: /*#__PURE__*/_jsxDEV(DeleteIcon, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1468,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1462,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1461,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1439,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1438,\n                columnNumber: 19\n              }, this)]\n            }, cert.id_certificazione, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1378,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1376,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1332,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1331,\n        columnNumber: 9\n      }, this), getTotalPages(filteredCertificazioni) > 1 && /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          justifyContent: 'center',\n          mt: 2\n        },\n        children: /*#__PURE__*/_jsxDEV(Pagination, {\n          count: getTotalPages(filteredCertificazioni),\n          page: currentPage,\n          onChange: (event, value) => setCurrentPage(value),\n          color: \"primary\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1481,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1480,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true);\n  };\n\n  // Renderizza il dialog per creare/modificare certificazione\n  const renderCertificazioneDialog = () => {\n    if (dialogType !== 'create' && dialogType !== 'edit') return null;\n    return /*#__PURE__*/_jsxDEV(Dialog, {\n      open: openDialog,\n      onClose: closeDialog,\n      maxWidth: \"md\",\n      fullWidth: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: dialogType === 'create' ? 'Nuova Certificazione' : 'Modifica Certificazione'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1499,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 2,\n          sx: {\n            mt: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(Autocomplete, {\n              options: cavi.filter(cavo => {\n                // Mostra solo cavi che possono essere certificati o quello già selezionato\n                const isSelected = cavo.id_cavo === formData.id_cavo;\n                const isNotCertified = !certificazioni.some(cert => cert.id_cavo === cavo.id_cavo);\n                const canBeCertified = puoEssereCertificato(cavo);\n                return isSelected || isNotCertified && canBeCertified;\n              }),\n              getOptionLabel: option => `${option.id_cavo} - ${option.tipologia}`,\n              value: cavi.find(c => c.id_cavo === formData.id_cavo) || null,\n              onChange: (event, newValue) => {\n                if (newValue) {\n                  handleCavoSelect(newValue);\n                } else {\n                  setFormData(prev => ({\n                    ...prev,\n                    id_cavo: '',\n                    lunghezza_misurata: ''\n                  }));\n                }\n              },\n              renderInput: params => /*#__PURE__*/_jsxDEV(TextField, {\n                ...params,\n                label: \"Cavo *\",\n                placeholder: \"Seleziona un cavo certificabile\",\n                required: true,\n                helperText: \"Solo cavi installati e completamente collegati\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1524,\n                columnNumber: 19\n              }, this),\n              renderOption: (props, option) => {\n                const canCertify = puoEssereCertificato(option);\n                const collegamenti = option.collegamenti || 0;\n                return /*#__PURE__*/_jsxDEV(Box, {\n                  component: \"li\",\n                  ...props,\n                  children: /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      width: '100%'\n                    },\n                    children: /*#__PURE__*/_jsxDEV(Stack, {\n                      direction: \"row\",\n                      justifyContent: \"space-between\",\n                      alignItems: \"center\",\n                      children: [/*#__PURE__*/_jsxDEV(Box, {\n                        children: [/*#__PURE__*/_jsxDEV(Typography, {\n                          variant: \"body2\",\n                          fontWeight: \"medium\",\n                          children: option.id_cavo\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1541,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                          variant: \"caption\",\n                          color: \"text.secondary\",\n                          children: [option.tipologia, \" - \", option.ubicazione_partenza, \" \\u2192 \", option.ubicazione_arrivo]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1544,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1540,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(Stack, {\n                        direction: \"row\",\n                        spacing: 1,\n                        children: [/*#__PURE__*/_jsxDEV(Chip, {\n                          size: \"small\",\n                          label: option.stato_installazione,\n                          color: option.stato_installazione === 'INSTALLATO' ? 'success' : 'default'\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1549,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                          size: \"small\",\n                          label: collegamenti === 3 ? 'Collegato' : 'Incompleto',\n                          color: collegamenti === 3 ? 'success' : 'warning'\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1554,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1548,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1539,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1538,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1537,\n                  columnNumber: 21\n                }, this);\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1505,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1504,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Operatore *\",\n              value: formData.id_operatore,\n              onChange: e => handleFormChange('id_operatore', e.target.value),\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1569,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1568,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(FormControl, {\n              fullWidth: true,\n              required: true,\n              children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                children: \"Strumento *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1580,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Select, {\n                value: formData.id_strumento,\n                onChange: e => handleFormChange('id_strumento', e.target.value),\n                label: \"Strumento *\",\n                children: strumenti.map(strumento => /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: strumento.id_strumento,\n                  children: [strumento.nome, \" - \", strumento.marca, \" \", strumento.modello, \" (S/N: \", strumento.numero_serie, \")\"]\n                }, strumento.id_strumento, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1587,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1581,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1579,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1578,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Lunghezza Misurata (m) *\",\n              type: \"number\",\n              value: formData.lunghezza_misurata,\n              onChange: e => handleFormChange('lunghezza_misurata', e.target.value),\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1596,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1595,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 4,\n            children: /*#__PURE__*/_jsxDEV(FormControl, {\n              fullWidth: true,\n              children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                children: \"Continuit\\xE0\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1608,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Select, {\n                value: formData.valore_continuita,\n                onChange: e => handleFormChange('valore_continuita', e.target.value),\n                label: \"Continuit\\xE0\",\n                children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"OK\",\n                  children: \"OK\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1614,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"NOK\",\n                  children: \"NOK\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1615,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1609,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1607,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1606,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 4,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Isolamento (M\\u03A9) *\",\n              type: \"number\",\n              value: formData.valore_isolamento,\n              onChange: e => handleFormChange('valore_isolamento', e.target.value),\n              required: true,\n              helperText: \"Valore minimo consigliato: 500 M\\u03A9\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1621,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1620,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 4,\n            children: /*#__PURE__*/_jsxDEV(FormControl, {\n              fullWidth: true,\n              children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                children: \"Resistenza\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1634,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Select, {\n                value: formData.valore_resistenza,\n                onChange: e => handleFormChange('valore_resistenza', e.target.value),\n                label: \"Resistenza\",\n                children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"OK\",\n                  children: \"OK\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1640,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"NOK\",\n                  children: \"NOK\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1641,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1635,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1633,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1632,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            children: /*#__PURE__*/_jsxDEV(Divider, {\n              sx: {\n                my: 2\n              },\n              children: /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                color: \"text.secondary\",\n                children: \"Parametri Ambientali e Test Avanzati\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1649,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1648,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1647,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Temperatura Ambiente (\\xB0C)\",\n              type: \"number\",\n              value: formData.temperatura_ambiente,\n              onChange: e => handleFormChange('temperatura_ambiente', e.target.value),\n              helperText: \"Temperatura durante il test\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1656,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1655,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Umidit\\xE0 (%)\",\n              type: \"number\",\n              value: formData.umidita,\n              onChange: e => handleFormChange('umidita', e.target.value),\n              helperText: \"Umidit\\xE0 relativa\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1667,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1666,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Tensione di Prova (V)\",\n              type: \"number\",\n              value: formData.tensione_prova,\n              onChange: e => handleFormChange('tensione_prova', e.target.value),\n              helperText: \"Tensione applicata per il test\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1678,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1677,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Durata Prova (min)\",\n              type: \"number\",\n              value: formData.durata_prova,\n              onChange: e => handleFormChange('durata_prova', e.target.value),\n              helperText: \"Durata del test in minuti\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1689,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1688,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(FormControl, {\n              fullWidth: true,\n              children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                children: \"Risultato Finale\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1701,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Select, {\n                value: formData.risultato_finale,\n                onChange: e => handleFormChange('risultato_finale', e.target.value),\n                label: \"Risultato Finale\",\n                children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"CONFORME\",\n                  children: /*#__PURE__*/_jsxDEV(Stack, {\n                    direction: \"row\",\n                    alignItems: \"center\",\n                    spacing: 1,\n                    children: [/*#__PURE__*/_jsxDEV(CheckIcon, {\n                      color: \"success\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1709,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      children: \"Conforme\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1710,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1708,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1707,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"NON_CONFORME\",\n                  children: /*#__PURE__*/_jsxDEV(Stack, {\n                    direction: \"row\",\n                    alignItems: \"center\",\n                    spacing: 1,\n                    children: [/*#__PURE__*/_jsxDEV(ErrorIcon, {\n                      color: \"error\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1715,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      children: \"Non Conforme\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1716,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1714,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1713,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"DA_VERIFICARE\",\n                  children: /*#__PURE__*/_jsxDEV(Stack, {\n                    direction: \"row\",\n                    alignItems: \"center\",\n                    spacing: 1,\n                    children: [/*#__PURE__*/_jsxDEV(WarningIcon, {\n                      color: \"warning\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1721,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      children: \"Da Verificare\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1722,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1720,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1719,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1702,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1700,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1699,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Note\",\n              multiline: true,\n              rows: 3,\n              value: formData.note,\n              onChange: e => handleFormChange('note', e.target.value),\n              placeholder: \"Inserisci eventuali note, osservazioni o anomalie riscontrate durante il test...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1730,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1729,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1503,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1502,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: closeDialog,\n          children: \"Annulla\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1743,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleCreateCertificazione,\n          variant: \"contained\",\n          disabled: loading || !formData.id_cavo || !formData.id_operatore || !formData.id_strumento || !formData.valore_isolamento,\n          startIcon: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n            size: 20\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1748,\n            columnNumber: 34\n          }, this) : /*#__PURE__*/_jsxDEV(SaveIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1748,\n            columnNumber: 67\n          }, this),\n          children: dialogType === 'create' ? 'Crea Certificazione' : 'Salva Modifiche'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1744,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1742,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1498,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Renderizza il dialog di visualizzazione dettagli\n  const renderViewDialog = () => {\n    if (dialogType !== 'view' || !selectedItem) return null;\n    return /*#__PURE__*/_jsxDEV(Dialog, {\n      open: openDialog,\n      onClose: closeDialog,\n      maxWidth: \"md\",\n      fullWidth: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: [\"Dettagli Certificazione - \", selectedItem.numero_certificato]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1763,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 2,\n          sx: {\n            mt: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              variant: \"outlined\",\n              children: /*#__PURE__*/_jsxDEV(CardContent, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  gutterBottom: true,\n                  children: \"Informazioni Cavo\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1771,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  children: [\"ID Cavo: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: selectedItem.id_cavo\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1775,\n                    columnNumber: 30\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1774,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  children: [\"Lunghezza Misurata: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: [selectedItem.lunghezza_misurata, \" m\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1778,\n                    columnNumber: 41\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1777,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1770,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1769,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1768,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              variant: \"outlined\",\n              children: /*#__PURE__*/_jsxDEV(CardContent, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  gutterBottom: true,\n                  children: \"Informazioni Certificazione\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1787,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  children: [\"Numero: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: selectedItem.numero_certificato\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1791,\n                    columnNumber: 29\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1790,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  children: [\"Data: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: new Date(selectedItem.data_certificazione).toLocaleDateString()\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1794,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1793,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  children: [\"Operatore: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: selectedItem.operatore || selectedItem.id_operatore\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1797,\n                    columnNumber: 32\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1796,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1786,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1785,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1784,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              variant: \"outlined\",\n              children: /*#__PURE__*/_jsxDEV(CardContent, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  gutterBottom: true,\n                  children: \"Risultati Test\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1806,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                  container: true,\n                  spacing: 2,\n                  children: [/*#__PURE__*/_jsxDEV(Grid, {\n                    item: true,\n                    xs: 4,\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      color: \"text.secondary\",\n                      children: \"Continuit\\xE0\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1811,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                      size: \"small\",\n                      label: selectedItem.valore_continuita,\n                      color: selectedItem.valore_continuita === 'OK' ? 'success' : 'error'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1814,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1810,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                    item: true,\n                    xs: 4,\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      color: \"text.secondary\",\n                      children: \"Isolamento\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1821,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                      size: \"small\",\n                      label: `${selectedItem.valore_isolamento} MΩ`,\n                      color: parseFloat(selectedItem.valore_isolamento) >= 500 ? 'success' : 'warning'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1824,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1820,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                    item: true,\n                    xs: 4,\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      color: \"text.secondary\",\n                      children: \"Resistenza\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1831,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                      size: \"small\",\n                      label: selectedItem.valore_resistenza,\n                      color: selectedItem.valore_resistenza === 'OK' ? 'success' : 'error'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1834,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1830,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1809,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1805,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1804,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1803,\n            columnNumber: 13\n          }, this), selectedItem.note && /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              variant: \"outlined\",\n              children: /*#__PURE__*/_jsxDEV(CardContent, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  gutterBottom: true,\n                  children: \"Note\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1849,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  children: selectedItem.note\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1852,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1848,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1847,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1846,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1767,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1766,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: closeDialog,\n          children: \"Chiudi\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1862,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: () => handleGeneratePdf(selectedItem),\n          variant: \"contained\",\n          startIcon: /*#__PURE__*/_jsxDEV(PdfIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1866,\n            columnNumber: 24\n          }, this),\n          disabled: loading,\n          children: \"Genera PDF\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1863,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1861,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1762,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Renderizza le statistiche\n  const renderStats = () => {\n    const totalCavi = cavi.length;\n    const caviInstallati = cavi.filter(c => c.stato_installazione === 'INSTALLATO').length;\n    const caviCertificati = certificazioni.length;\n    const percentualeCertificazione = totalCavi > 0 ? Math.round(caviCertificati / caviInstallati * 100) : 0;\n    return /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 2,\n      sx: {\n        mb: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              color: \"text.secondary\",\n              gutterBottom: true,\n              children: \"Cavi Totali\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1888,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h4\",\n              children: totalCavi\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1891,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1887,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1886,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1885,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              color: \"text.secondary\",\n              gutterBottom: true,\n              children: \"Cavi Installati\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1900,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h4\",\n              children: caviInstallati\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1903,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1899,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1898,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1897,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              color: \"text.secondary\",\n              gutterBottom: true,\n              children: \"Certificazioni\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1912,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h4\",\n              children: caviCertificati\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1915,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1911,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1910,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1909,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              color: \"text.secondary\",\n              gutterBottom: true,\n              children: \"% Certificazione\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1924,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h4\",\n              color: percentualeCertificazione >= 80 ? 'success.main' : 'warning.main',\n              children: [percentualeCertificazione, \"%\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1927,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1923,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1922,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1921,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1884,\n      columnNumber: 7\n    }, this);\n  };\n  return /*#__PURE__*/_jsxDEV(Container, {\n    maxWidth: \"xl\",\n    sx: {\n      py: 3\n    },\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mb: 4\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h4\",\n        component: \"h1\",\n        gutterBottom: true,\n        fontWeight: \"bold\",\n        children: \"\\uD83D\\uDD0C Sistema di Certificazione Cavi\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1941,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body1\",\n        color: \"text.secondary\",\n        sx: {\n          mb: 2\n        },\n        children: \"Gestione completa delle certificazioni elettriche secondo standard CEI 64-8\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1944,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 2,\n          bgcolor: 'info.light',\n          color: 'info.contrastText',\n          mb: 2\n        },\n        children: /*#__PURE__*/_jsxDEV(Stack, {\n          direction: \"row\",\n          alignItems: \"center\",\n          spacing: 2,\n          children: [/*#__PURE__*/_jsxDEV(InfoIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1951,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              fontWeight: \"bold\",\n              children: \"Come utilizzare il sistema:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1953,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              children: [\"\\u2022 \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Tab \\\"Cavi da Certificare\\\":\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1957,\n                columnNumber: 19\n              }, this), \" Visualizza tutti i cavi posati e clicca sul \\\"+\\\" per certificare \\u2022 \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Certificazione:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1958,\n                columnNumber: 19\n              }, this), \" Richiede solo che il cavo sia posato, il collegamento pu\\xF2 essere gestito al momento \\u2022 \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Tab \\\"Certificazioni Completate\\\":\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1959,\n                columnNumber: 19\n              }, this), \" Gestisci le certificazioni esistenti, attiva la \\\"Selezione\\\" per operazioni multiple \\u2022 \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Filtri:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1960,\n                columnNumber: 19\n              }, this), \" Usa i filtri specifici per ogni tab per trovare rapidamente quello che cerchi\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1956,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1952,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1950,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1949,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1940,\n      columnNumber: 7\n    }, this), renderDashboard(), (loading || operationInProgress) && /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mb: 2\n      },\n      children: [/*#__PURE__*/_jsxDEV(LinearProgress, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1973,\n        columnNumber: 11\n      }, this), progress > 0 && /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"caption\",\n        color: \"text.secondary\",\n        sx: {\n          mt: 1\n        },\n        children: [\"Caricamento... \", progress, \"%\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1975,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1972,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        mb: 3\n      },\n      children: /*#__PURE__*/_jsxDEV(Tabs, {\n        value: activeTab,\n        onChange: handleTabChange,\n        indicatorColor: \"primary\",\n        textColor: \"primary\",\n        variant: \"fullWidth\",\n        children: [/*#__PURE__*/_jsxDEV(Tab, {\n          label: /*#__PURE__*/_jsxDEV(Stack, {\n            direction: \"row\",\n            alignItems: \"center\",\n            spacing: 1,\n            children: [/*#__PURE__*/_jsxDEV(CableIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1994,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                fontWeight: \"bold\",\n                children: \"Cavi da Certificare\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1996,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"caption\",\n                color: \"text.secondary\",\n                children: [filteredCavi.length, \" cavi totali\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1999,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1995,\n              columnNumber: 17\n            }, this), statistics.caviNonCertificati > 0 && /*#__PURE__*/_jsxDEV(Badge, {\n              badgeContent: statistics.caviNonCertificati,\n              color: \"warning\",\n              children: /*#__PURE__*/_jsxDEV(WarningIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2005,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2004,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1993,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1991,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Tab, {\n          label: /*#__PURE__*/_jsxDEV(Stack, {\n            direction: \"row\",\n            alignItems: \"center\",\n            spacing: 1,\n            children: [/*#__PURE__*/_jsxDEV(ScienceIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2014,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                fontWeight: \"bold\",\n                children: \"Certificazioni Completate\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2016,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"caption\",\n                color: \"text.secondary\",\n                children: [filteredCertificazioni.length, \" certificazioni\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2019,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2015,\n              columnNumber: 17\n            }, this), statistics.certificazioniOggi > 0 && /*#__PURE__*/_jsxDEV(Badge, {\n              badgeContent: statistics.certificazioniOggi,\n              color: \"success\",\n              children: /*#__PURE__*/_jsxDEV(CheckIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2025,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2024,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2013,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2011,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1984,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1983,\n      columnNumber: 7\n    }, this), renderSearchAndFilters(), !loading && activeTab === 0 && renderCaviTable(), !loading && activeTab === 1 && renderCertificazioniTable(), renderCertificazioneDialog(), renderViewDialog(), /*#__PURE__*/_jsxDEV(Snackbar, {\n      open: snackbar.open,\n      autoHideDuration: 6000,\n      onClose: closeSnackbar,\n      anchorOrigin: {\n        vertical: 'bottom',\n        horizontal: 'right'\n      },\n      children: /*#__PURE__*/_jsxDEV(Alert, {\n        onClose: closeSnackbar,\n        severity: snackbar.severity,\n        sx: {\n          width: '100%'\n        },\n        children: snackbar.message\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2052,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 2046,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(SpeedDial, {\n      ariaLabel: \"Azioni rapide\",\n      sx: {\n        position: 'fixed',\n        bottom: 16,\n        right: 16\n      },\n      icon: /*#__PURE__*/_jsxDEV(SpeedDialIcon, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2061,\n        columnNumber: 15\n      }, this),\n      children: [/*#__PURE__*/_jsxDEV(SpeedDialAction, {\n        icon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2064,\n          columnNumber: 17\n        }, this),\n        tooltipTitle: \"Nuova Certificazione\",\n        onClick: openCreateDialog\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2063,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(SpeedDialAction, {\n        icon: /*#__PURE__*/_jsxDEV(ExportIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2069,\n          columnNumber: 17\n        }, this),\n        tooltipTitle: \"Esporta Tutto\",\n        onClick: handleExportAll\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2068,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(SpeedDialAction, {\n        icon: /*#__PURE__*/_jsxDEV(RefreshIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2074,\n          columnNumber: 17\n        }, this),\n        tooltipTitle: \"Aggiorna Dati\",\n        onClick: loadInitialData\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2073,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(SpeedDialAction, {\n        icon: /*#__PURE__*/_jsxDEV(ReportIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2079,\n          columnNumber: 17\n        }, this),\n        tooltipTitle: \"Report Avanzato\",\n        onClick: () => showSnackbar('Funzionalità in sviluppo', 'info')\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2078,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 2058,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 1938,\n    columnNumber: 5\n  }, this);\n}, \"Y6H2QcIXYjGgUXj6IRVp5RPodNQ=\")), \"Y6H2QcIXYjGgUXj6IRVp5RPodNQ=\");\n_c2 = CertificazioneCaviImproved;\nexport default CertificazioneCaviImproved;\nvar _c, _c2;\n$RefreshReg$(_c, \"CertificazioneCaviImproved$forwardRef\");\n$RefreshReg$(_c2, \"CertificazioneCaviImproved\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "forwardRef", "useImperativeHandle", "Box", "Typography", "<PERSON><PERSON>", "Paper", "Grid", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "TextField", "FormControl", "InputLabel", "Select", "MenuItem", "Autocomplete", "CircularProgress", "<PERSON><PERSON>", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "IconButton", "Tabs", "Tab", "Pagination", "InputAdornment", "Divider", "<PERSON><PERSON>", "Chip", "<PERSON><PERSON><PERSON>", "Badge", "LinearProgress", "Collapse", "List", "ListItem", "ListItemText", "ListItemIcon", "Snackbar", "AppBar", "<PERSON><PERSON><PERSON>", "Container", "Fab", "SpeedDial", "SpeedDialAction", "SpeedDialIcon", "Add", "AddIcon", "Search", "SearchIcon", "FilterList", "FilterIcon", "PictureAsPdf", "PdfIcon", "Download", "DownloadIcon", "Visibility", "ViewIcon", "Delete", "DeleteIcon", "Edit", "EditIcon", "Save", "SaveIcon", "Clear", "ClearIcon", "Build", "BuildIcon", "CheckCircle", "CheckIcon", "Warning", "WarningIcon", "GetApp", "ExportIcon", "Print", "PrintIcon", "Email", "EmailIcon", "CloudUpload", "UploadIcon", "Assessment", "ReportIcon", "Settings", "SettingsIcon", "Refresh", "RefreshIcon", "ExpandMore", "ExpandMoreIcon", "ExpandLess", "ExpandLessIcon", "Info", "InfoIcon", "Error", "ErrorIcon", "Schedule", "ScheduleIcon", "Person", "PersonIcon", "Cable", "CableIcon", "Science", "ScienceIcon", "Block", "BlockIcon", "certificazioneService", "caviService", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "CertificazioneCaviImproved", "_s", "_c", "cantiereId", "onSuccess", "onError", "ref", "loading", "setLoading", "activeTab", "setActiveTab", "certificazioni", "setCertificazioni", "cavi", "<PERSON><PERSON><PERSON>", "strumenti", "setStrumenti", "searchTerm", "setSearchTerm", "filteredCavi", "setFilteredCavi", "filteredCertificazioni", "setFilteredCertificazioni", "advancedFiltersOpen", "setAdvancedFiltersOpen", "filters", "setFilters", "stato", "tipologia", "operatore", "dataInizio", "dataFine", "valoreIsolamento", "risultatoTest", "strumento", "certificazione", "currentPage", "setCurrentPage", "itemsPerPage", "setItemsPerPage", "sortBy", "setSortBy", "sortOrder", "setSortOrder", "openDialog", "setOpenDialog", "dialogType", "setDialogType", "selectedItem", "setSelectedItem", "bulkSelection", "setBulkSelection", "bulkMode", "setBulkMode", "snackbar", "setSnackbar", "open", "message", "severity", "progress", "setProgress", "operationInProgress", "setOperationInProgress", "formData", "setFormData", "id_cavo", "id_operatore", "id_strumento", "<PERSON><PERSON><PERSON>_misurata", "valore_continuita", "valore_isolamento", "valore_resistenza", "note", "temperatura_ambiente", "<PERSON><PERSON><PERSON>", "tensione_prova", "durata_prova", "risultato_finale", "statistics", "setStatistics", "totaleCavi", "caviCertificati", "caviNonCertificati", "percentualeCompletamento", "certificazioniOggi", "certificazioniSettimana", "loadInitialData", "filterCavi", "filterCertificazioni", "calculateStatistics", "loadCavi", "loadCertificazioni", "loadStrumenti", "error", "showSnackbar", "data", "getCertificazioni", "console", "get<PERSON><PERSON>", "getStrumenti", "length", "Math", "round", "oggi", "Date", "toDateString", "filter", "cert", "data_certificazione", "unaSettimanaFa", "setDate", "getDate", "caviCertificabili", "cavo", "puoEssereCertificato", "caviNonCertificabili", "caviCollegati", "isCavoCollegato", "closeSnackbar", "filtered", "searchLower", "toLowerCase", "_cavo$tipologia", "_cavo$ubicazione_part", "_cavo$ubicazione_arri", "_cavo$sezione", "_cavo$utility", "includes", "ubicazione_partenza", "ubicazione_arrivo", "sezione", "utility", "stato_installazione", "some", "sort", "a", "b", "aValue", "bValue", "_cert$operatore", "_cert$numero_certific", "_cert$note", "numero_certificato", "valore", "parseFloat", "toggleBulkMode", "toggleItemSelection", "itemId", "prev", "newSelection", "id", "selectAllItems", "allIds", "map", "id_certificazione", "clearSelection", "isCavoCertificato", "idCavo", "isInstallato", "isCollegato", "colle<PERSON>nti", "hasResponsabili", "responsabile_partenza", "responsabile_arrivo", "getMessaggioErroreCertificazione", "handleTabChange", "event", "newValue", "openCreateDialog", "cavoPreselezionato", "metratura_reale", "metri_te<PERSON>ci", "closeDialog", "handleFormChange", "field", "value", "handleCavoSelect", "handleCreateCertificazione", "find", "c", "messaggio", "conferma", "window", "confirm", "createCertificazione", "handleGeneratePdf", "response", "generatePdf", "file_url", "newWindow", "link", "document", "createElement", "href", "download", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "pdf_content", "blob", "Blob", "atob", "type", "url", "URL", "createObjectURL", "revokeObjectURL", "handleDeleteCertificazione", "deleteCertificazione", "handleBulkDelete", "handleBulkExport", "<PERSON><PERSON><PERSON><PERSON>", "csv<PERSON><PERSON>nt", "generateCSV", "downloadCSV", "toISOString", "split", "headers", "rows", "toLocaleDateString", "row", "join", "content", "filename", "undefined", "setAttribute", "style", "visibility", "handleExportAll", "handleOptionSelect", "option", "getCurrentPageItems", "items", "startIndex", "endIndex", "slice", "getTotalPages", "ceil", "getUniqueValues", "array", "Set", "item", "Boolean", "renderDashboard", "container", "spacing", "sx", "mb", "children", "xs", "md", "background", "color", "textAlign", "py", "fontSize", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "fontWeight", "renderSearchAndFilters", "p", "alignItems", "fullWidth", "placeholder", "onChange", "e", "target", "InputProps", "startAdornment", "position", "endAdornment", "onClick", "size", "startIcon", "Object", "values", "f", "disabled", "in", "my", "tip", "op", "label", "InputLabelProps", "shrink", "direction", "justifyContent", "renderCaviTable", "currentItems", "component", "isCertificato", "puoCertificare", "messaggioErrore", "statoCollegamento", "colore", "title", "icon", "display", "mt", "count", "page", "renderCertificazioniTable", "padding", "selected", "hover", "s", "nome", "marca", "strumento_utilizzato", "renderCertificazioneDialog", "onClose", "max<PERSON><PERSON><PERSON>", "options", "isSelected", "isNotCertified", "canBeCertified", "getOptionLabel", "renderInput", "params", "required", "helperText", "renderOption", "props", "canCertify", "width", "modello", "numero_serie", "multiline", "renderViewDialog", "gutterBottom", "renderStats", "totalCavi", "caviInstallati", "percentualeCertificazione", "sm", "bgcolor", "indicatorColor", "textColor", "badgeContent", "autoHideDuration", "anchor<PERSON><PERSON><PERSON>", "vertical", "horizontal", "aria<PERSON><PERSON><PERSON>", "bottom", "right", "tooltipTitle", "_c2", "$RefreshReg$"], "sources": ["C:/CMS/webapp/frontend/src/components/cavi/CertificazioneCaviImproved.js"], "sourcesContent": ["import React, { useState, useEffect, forwardRef, useImperativeHandle } from 'react';\nimport {\n  Box,\n  Typo<PERSON>,\n  Button,\n  Paper,\n  Grid,\n  Card,\n  CardContent,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  TextField,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  Autocomplete,\n  CircularProgress,\n  Alert,\n  Table,\n  TableBody,\n  TableCell,\n  TableContainer,\n  TableHead,\n  TableRow,\n  IconButton,\n  Tabs,\n  Tab,\n  Pagination,\n  InputAdornment,\n  Divider,\n  Stack,\n  Chip,\n  Tooltip,\n  Badge,\n  LinearProgress,\n  Collapse,\n  List,\n  ListItem,\n  ListItemText,\n  ListItemIcon,\n  Snackbar,\n  AppBar,\n  Toolbar,\n  Container,\n  Fab,\n  SpeedDial,\n  SpeedDialAction,\n  SpeedDialIcon\n} from '@mui/material';\nimport {\n  Add as AddIcon,\n  Search as SearchIcon,\n  FilterList as FilterIcon,\n  PictureAsPdf as PdfIcon,\n  Download as DownloadIcon,\n  Visibility as ViewIcon,\n  Delete as DeleteIcon,\n  Edit as EditIcon,\n  Save as SaveIcon,\n  Clear as ClearIcon,\n  Build as BuildIcon,\n  CheckCircle as CheckIcon,\n  Warning as WarningIcon,\n  GetApp as ExportIcon,\n  Print as PrintIcon,\n  Email as EmailIcon,\n  CloudUpload as UploadIcon,\n  Assessment as ReportIcon,\n  Settings as SettingsIcon,\n  Refresh as RefreshIcon,\n  ExpandMore as ExpandMoreIcon,\n  ExpandLess as ExpandLessIcon,\n  Info as InfoIcon,\n  Error as ErrorIcon,\n  Schedule as ScheduleIcon,\n  Person as PersonIcon,\n  Cable as CableIcon,\n  Science as ScienceIcon,\n  Block as BlockIcon\n} from '@mui/icons-material';\n\nimport certificazioneService from '../../services/certificazioneService';\nimport caviService from '../../services/caviService';\n\nconst CertificazioneCaviImproved = forwardRef(({ cantiereId, onSuccess, onError }, ref) => {\n  // Stati principali\n  const [loading, setLoading] = useState(false);\n  const [activeTab, setActiveTab] = useState(0);\n  const [certificazioni, setCertificazioni] = useState([]);\n  const [cavi, setCavi] = useState([]);\n  const [strumenti, setStrumenti] = useState([]);\n\n  // Stati per ricerca e filtri avanzati\n  const [searchTerm, setSearchTerm] = useState('');\n  const [filteredCavi, setFilteredCavi] = useState([]);\n  const [filteredCertificazioni, setFilteredCertificazioni] = useState([]);\n  const [advancedFiltersOpen, setAdvancedFiltersOpen] = useState(false);\n  const [filters, setFilters] = useState({\n    stato: '',\n    tipologia: '',\n    operatore: '',\n    dataInizio: '',\n    dataFine: '',\n    valoreIsolamento: '',\n    risultatoTest: '',\n    strumento: '',\n    certificazione: ''\n  });\n\n  // Stati per paginazione e ordinamento\n  const [currentPage, setCurrentPage] = useState(1);\n  const [itemsPerPage, setItemsPerPage] = useState(10);\n  const [sortBy, setSortBy] = useState('data_certificazione');\n  const [sortOrder, setSortOrder] = useState('desc');\n\n  // Stati per dialogs e modali\n  const [openDialog, setOpenDialog] = useState(false);\n  const [dialogType, setDialogType] = useState('');\n  const [selectedItem, setSelectedItem] = useState(null);\n  const [bulkSelection, setBulkSelection] = useState([]);\n  const [bulkMode, setBulkMode] = useState(false);\n\n  // Stati per notifiche e feedback\n  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'info' });\n  const [progress, setProgress] = useState(0);\n  const [operationInProgress, setOperationInProgress] = useState(false);\n\n  // Stati per form certificazione avanzato\n  const [formData, setFormData] = useState({\n    id_cavo: '',\n    id_operatore: '',\n    id_strumento: '',\n    lunghezza_misurata: '',\n    valore_continuita: 'OK',\n    valore_isolamento: '',\n    valore_resistenza: 'OK',\n    note: '',\n    temperatura_ambiente: '',\n    umidita: '',\n    tensione_prova: '',\n    durata_prova: '',\n    risultato_finale: 'CONFORME'\n  });\n\n  // Stati per statistiche e dashboard\n  const [statistics, setStatistics] = useState({\n    totaleCavi: 0,\n    caviCertificati: 0,\n    caviNonCertificati: 0,\n    percentualeCompletamento: 0,\n    certificazioniOggi: 0,\n    certificazioniSettimana: 0\n  });\n\n  // Carica dati iniziali\n  useEffect(() => {\n    loadInitialData();\n  }, [cantiereId]);\n\n  // Filtra cavi in base alla ricerca\n  useEffect(() => {\n    filterCavi();\n  }, [cavi, searchTerm, filters, sortBy, sortOrder]);\n\n  // Filtra certificazioni\n  useEffect(() => {\n    filterCertificazioni();\n  }, [certificazioni, searchTerm, filters, sortBy, sortOrder]);\n\n  // Ricalcola statistiche quando cambiano i dati\n  useEffect(() => {\n    calculateStatistics();\n  }, [cavi, certificazioni]);\n\n  const loadInitialData = async () => {\n    try {\n      setLoading(true);\n      setProgress(0);\n\n      // Carica dati in sequenza con progress\n      setProgress(25);\n      await loadCavi();\n\n      setProgress(50);\n      await loadCertificazioni();\n\n      setProgress(75);\n      await loadStrumenti();\n\n      setProgress(100);\n      calculateStatistics();\n\n    } catch (error) {\n      showSnackbar('Errore nel caricamento dei dati iniziali', 'error');\n      onError('Errore nel caricamento dei dati iniziali');\n    } finally {\n      setLoading(false);\n      setProgress(0);\n    }\n  };\n\n  const loadCertificazioni = async () => {\n    try {\n      const data = await certificazioneService.getCertificazioni(cantiereId);\n      setCertificazioni(data);\n      return data;\n    } catch (error) {\n      console.error('Errore nel caricamento delle certificazioni:', error);\n      throw error;\n    }\n  };\n\n  const loadCavi = async () => {\n    try {\n      const data = await caviService.getCavi(cantiereId);\n      setCavi(data);\n      return data;\n    } catch (error) {\n      console.error('Errore nel caricamento dei cavi:', error);\n      throw error;\n    }\n  };\n\n  const loadStrumenti = async () => {\n    try {\n      const data = await certificazioneService.getStrumenti(cantiereId);\n      setStrumenti(data);\n      return data;\n    } catch (error) {\n      console.error('Errore nel caricamento degli strumenti:', error);\n      throw error;\n    }\n  };\n\n  // Calcola statistiche avanzate\n  const calculateStatistics = () => {\n    const totaleCavi = cavi.length;\n    const caviCertificati = certificazioni.length;\n    const caviNonCertificati = totaleCavi - caviCertificati;\n    const percentualeCompletamento = totaleCavi > 0 ? Math.round((caviCertificati / totaleCavi) * 100) : 0;\n\n    // Calcola certificazioni di oggi\n    const oggi = new Date().toDateString();\n    const certificazioniOggi = certificazioni.filter(cert =>\n      new Date(cert.data_certificazione).toDateString() === oggi\n    ).length;\n\n    // Calcola certificazioni della settimana\n    const unaSettimanaFa = new Date();\n    unaSettimanaFa.setDate(unaSettimanaFa.getDate() - 7);\n    const certificazioniSettimana = certificazioni.filter(cert =>\n      new Date(cert.data_certificazione) >= unaSettimanaFa\n    ).length;\n\n    // Calcola cavi certificabili (solo posati/installati)\n    const caviCertificabili = cavi.filter(cavo => puoEssereCertificato(cavo)).length;\n    const caviNonCertificabili = totaleCavi - caviCertificabili;\n\n    // Calcola cavi collegati completamente\n    const caviCollegati = cavi.filter(cavo => isCavoCollegato(cavo)).length;\n\n    setStatistics({\n      totaleCavi,\n      caviCertificati,\n      caviNonCertificati,\n      caviCertificabili,\n      caviNonCertificabili,\n      caviCollegati,\n      percentualeCompletamento,\n      certificazioniOggi,\n      certificazioniSettimana\n    });\n  };\n\n  // Gestione snackbar\n  const showSnackbar = (message, severity = 'info') => {\n    setSnackbar({ open: true, message, severity });\n  };\n\n  const closeSnackbar = () => {\n    setSnackbar({ ...snackbar, open: false });\n  };\n\n  const filterCavi = () => {\n    let filtered = cavi;\n\n    // Filtro per ricerca testuale avanzata\n    if (searchTerm) {\n      const searchLower = searchTerm.toLowerCase();\n      filtered = filtered.filter(cavo =>\n        cavo.id_cavo.toLowerCase().includes(searchLower) ||\n        cavo.tipologia?.toLowerCase().includes(searchLower) ||\n        cavo.ubicazione_partenza?.toLowerCase().includes(searchLower) ||\n        cavo.ubicazione_arrivo?.toLowerCase().includes(searchLower) ||\n        cavo.sezione?.toLowerCase().includes(searchLower) ||\n        cavo.utility?.toLowerCase().includes(searchLower)\n      );\n    }\n\n    // Filtri specifici avanzati\n    if (filters.stato) {\n      filtered = filtered.filter(cavo => cavo.stato_installazione === filters.stato);\n    }\n    if (filters.tipologia) {\n      filtered = filtered.filter(cavo => cavo.tipologia === filters.tipologia);\n    }\n\n    // Filtro per stato certificazione\n    if (filters.certificazione) {\n      if (filters.certificazione === 'CERTIFICATO') {\n        filtered = filtered.filter(cavo =>\n          certificazioni.some(cert => cert.id_cavo === cavo.id_cavo)\n        );\n      } else if (filters.certificazione === 'NON_CERTIFICATO') {\n        filtered = filtered.filter(cavo =>\n          !certificazioni.some(cert => cert.id_cavo === cavo.id_cavo)\n        );\n      }\n    }\n\n    // Ordinamento\n    filtered.sort((a, b) => {\n      let aValue = a[sortBy];\n      let bValue = b[sortBy];\n\n      if (typeof aValue === 'string') {\n        aValue = aValue.toLowerCase();\n        bValue = bValue.toLowerCase();\n      }\n\n      if (sortOrder === 'asc') {\n        return aValue > bValue ? 1 : -1;\n      } else {\n        return aValue < bValue ? 1 : -1;\n      }\n    });\n\n    setFilteredCavi(filtered);\n  };\n\n  const filterCertificazioni = () => {\n    let filtered = certificazioni;\n\n    // Ricerca testuale avanzata\n    if (searchTerm) {\n      const searchLower = searchTerm.toLowerCase();\n      filtered = filtered.filter(cert =>\n        cert.id_cavo.toLowerCase().includes(searchLower) ||\n        cert.operatore?.toLowerCase().includes(searchLower) ||\n        cert.numero_certificato?.toLowerCase().includes(searchLower) ||\n        cert.note?.toLowerCase().includes(searchLower)\n      );\n    }\n\n    // Filtri avanzati\n    if (filters.operatore) {\n      filtered = filtered.filter(cert => cert.operatore === filters.operatore);\n    }\n    if (filters.strumento) {\n      filtered = filtered.filter(cert => cert.strumento === filters.strumento);\n    }\n    if (filters.risultatoTest) {\n      filtered = filtered.filter(cert => cert.risultato_finale === filters.risultatoTest);\n    }\n    if (filters.dataInizio) {\n      filtered = filtered.filter(cert =>\n        new Date(cert.data_certificazione) >= new Date(filters.dataInizio)\n      );\n    }\n    if (filters.dataFine) {\n      filtered = filtered.filter(cert =>\n        new Date(cert.data_certificazione) <= new Date(filters.dataFine)\n      );\n    }\n    if (filters.valoreIsolamento) {\n      const valore = parseFloat(filters.valoreIsolamento);\n      filtered = filtered.filter(cert =>\n        parseFloat(cert.valore_isolamento) >= valore\n      );\n    }\n\n    // Ordinamento\n    filtered.sort((a, b) => {\n      let aValue = a[sortBy];\n      let bValue = b[sortBy];\n\n      if (sortBy === 'data_certificazione') {\n        aValue = new Date(aValue);\n        bValue = new Date(bValue);\n      } else if (typeof aValue === 'string') {\n        aValue = aValue.toLowerCase();\n        bValue = bValue.toLowerCase();\n      }\n\n      if (sortOrder === 'asc') {\n        return aValue > bValue ? 1 : -1;\n      } else {\n        return aValue < bValue ? 1 : -1;\n      }\n    });\n\n    setFilteredCertificazioni(filtered);\n  };\n\n  // Gestione selezione multipla - SOLO per certificazioni\n  const toggleBulkMode = () => {\n    if (activeTab !== 1) {\n      showSnackbar('La selezione multipla è disponibile solo per le certificazioni', 'warning');\n      return;\n    }\n    setBulkMode(!bulkMode);\n    setBulkSelection([]);\n    showSnackbar(\n      !bulkMode\n        ? 'Modalità selezione attivata - Clicca sulle certificazioni per selezionarle'\n        : 'Modalità selezione disattivata',\n      'info'\n    );\n  };\n\n  const toggleItemSelection = (itemId) => {\n    setBulkSelection(prev => {\n      const newSelection = prev.includes(itemId)\n        ? prev.filter(id => id !== itemId)\n        : [...prev, itemId];\n\n      showSnackbar(\n        `${newSelection.length} certificazioni selezionate`,\n        'info'\n      );\n      return newSelection;\n    });\n  };\n\n  const selectAllItems = () => {\n    if (activeTab !== 1) return;\n\n    const allIds = filteredCertificazioni.map(cert => cert.id_certificazione);\n    setBulkSelection(allIds);\n    showSnackbar(`Tutte le ${allIds.length} certificazioni selezionate`, 'success');\n  };\n\n  const clearSelection = () => {\n    setBulkSelection([]);\n    showSnackbar('Selezione cancellata', 'info');\n  };\n\n  // Funzione per verificare se un cavo è certificato\n  const isCavoCertificato = (idCavo) => {\n    return certificazioni.some(cert => cert.id_cavo === idCavo);\n  };\n\n  // Funzione per verificare se un cavo può essere certificato\n  const puoEssereCertificato = (cavo) => {\n    // Verifica che il cavo sia installato/posato\n    const isInstallato = cavo.stato_installazione === 'Installato' ||\n                        cavo.stato_installazione === 'INSTALLATO' ||\n                        cavo.stato_installazione === 'POSATO';\n\n    // Per la certificazione basta che sia posato\n    // Il collegamento può essere gestito durante la certificazione\n    return isInstallato;\n  };\n\n  // Funzione per verificare se un cavo è completamente collegato\n  const isCavoCollegato = (cavo) => {\n    const isCollegato = cavo.collegamenti === 3;\n    const hasResponsabili = cavo.responsabile_partenza && cavo.responsabile_arrivo;\n    return isCollegato && hasResponsabili;\n  };\n\n  // Funzione per ottenere il messaggio di errore per cavi non certificabili\n  const getMessaggioErroreCertificazione = (cavo) => {\n    const isInstallato = cavo.stato_installazione === 'Installato' ||\n                        cavo.stato_installazione === 'INSTALLATO' ||\n                        cavo.stato_installazione === 'POSATO';\n\n    if (!isInstallato) {\n      return 'Il cavo deve essere posato/installato prima di poter essere certificato';\n    }\n\n    return 'Cavo non certificabile per motivi sconosciuti';\n  };\n\n  // Gestione tabs\n  const handleTabChange = (event, newValue) => {\n    setActiveTab(newValue);\n    setCurrentPage(1);\n    setSearchTerm('');\n    setFilters({ stato: '', tipologia: '', operatore: '' });\n  };\n\n  // Gestione dialogs\n  const openCreateDialog = (cavoPreselezionato = null) => {\n    setDialogType('create');\n    setSelectedItem(null);\n\n    // Se viene passato un cavo, precompila il form\n    if (cavoPreselezionato) {\n      setFormData({\n        id_cavo: cavoPreselezionato.id_cavo,\n        id_operatore: '',\n        id_strumento: '',\n        lunghezza_misurata: cavoPreselezionato.metratura_reale || cavoPreselezionato.metri_teorici || '',\n        valore_continuita: 'OK',\n        valore_isolamento: '',\n        valore_resistenza: 'OK',\n        note: '',\n        temperatura_ambiente: '',\n        umidita: '',\n        tensione_prova: '',\n        durata_prova: '',\n        risultato_finale: 'CONFORME'\n      });\n      showSnackbar(`Cavo ${cavoPreselezionato.id_cavo} selezionato automaticamente`, 'success');\n    } else {\n      // Reset form per nuova certificazione generica\n      setFormData({\n        id_cavo: '',\n        id_operatore: '',\n        id_strumento: '',\n        lunghezza_misurata: '',\n        valore_continuita: 'OK',\n        valore_isolamento: '',\n        valore_resistenza: 'OK',\n        note: '',\n        temperatura_ambiente: '',\n        umidita: '',\n        tensione_prova: '',\n        durata_prova: '',\n        risultato_finale: 'CONFORME'\n      });\n    }\n\n    setOpenDialog(true);\n  };\n\n  const closeDialog = () => {\n    setOpenDialog(false);\n    setSelectedItem(null);\n    setDialogType('');\n  };\n\n  // Gestione form\n  const handleFormChange = (field, value) => {\n    setFormData(prev => ({\n      ...prev,\n      [field]: value\n    }));\n  };\n\n  const handleCavoSelect = (cavo) => {\n    setFormData(prev => ({\n      ...prev,\n      id_cavo: cavo.id_cavo,\n      lunghezza_misurata: cavo.metratura_reale || cavo.metri_teorici || ''\n    }));\n  };\n\n  // Operazioni CRUD avanzate\n  const handleCreateCertificazione = async () => {\n    try {\n      if (!formData.id_cavo || !formData.id_operatore || !formData.id_strumento || !formData.valore_isolamento) {\n        showSnackbar('Compila tutti i campi obbligatori', 'warning');\n        return;\n      }\n\n      // Verifica che il cavo possa essere certificato\n      const cavo = cavi.find(c => c.id_cavo === formData.id_cavo);\n      if (!cavo) {\n        showSnackbar('Cavo non trovato', 'error');\n        return;\n      }\n\n      if (!puoEssereCertificato(cavo)) {\n        const messaggio = getMessaggioErroreCertificazione(cavo);\n        showSnackbar(`Impossibile certificare il cavo: ${messaggio}`, 'error');\n        return;\n      }\n\n      // Verifica che il cavo non sia già certificato\n      if (isCavoCertificato(formData.id_cavo)) {\n        showSnackbar('Il cavo è già stato certificato', 'warning');\n        return;\n      }\n\n      // Verifica se il cavo è collegato, altrimenti chiedi conferma\n      if (!isCavoCollegato(cavo)) {\n        const conferma = window.confirm(\n          `ATTENZIONE: Il cavo ${cavo.id_cavo} non risulta completamente collegato.\\n\\n` +\n          `Stato collegamenti: ${cavo.collegamenti === 0 ? 'Non collegato' :\n                                cavo.collegamenti === 1 ? 'Solo partenza collegata' :\n                                cavo.collegamenti === 2 ? 'Solo arrivo collegato' :\n                                'Stato sconosciuto'}\\n\\n` +\n          `Vuoi procedere comunque con la certificazione?\\n` +\n          `(Ricorda di completare i collegamenti prima della messa in servizio)`\n        );\n\n        if (!conferma) {\n          return;\n        }\n      }\n\n      setOperationInProgress(true);\n      await certificazioneService.createCertificazione(cantiereId, formData);\n      showSnackbar('Certificazione creata con successo', 'success');\n      closeDialog();\n      await loadCertificazioni();\n      calculateStatistics();\n    } catch (error) {\n      showSnackbar('Errore nella creazione della certificazione: ' + (error.message || 'Errore sconosciuto'), 'error');\n    } finally {\n      setOperationInProgress(false);\n    }\n  };\n\n  const handleGeneratePdf = async (certificazione) => {\n    try {\n      setOperationInProgress(true);\n      showSnackbar('Generazione PDF in corso...', 'info');\n\n      const response = await certificazioneService.generatePdf(cantiereId, certificazione.id_certificazione);\n\n      if (response.file_url) {\n        // Apri il PDF in una nuova finestra\n        const newWindow = window.open(response.file_url, '_blank');\n        if (newWindow) {\n          showSnackbar('PDF generato e aperto in una nuova finestra', 'success');\n        } else {\n          // Se il popup è bloccato, offri il download diretto\n          const link = document.createElement('a');\n          link.href = response.file_url;\n          link.download = `certificazione_${certificazione.numero_certificato}.pdf`;\n          document.body.appendChild(link);\n          link.click();\n          document.body.removeChild(link);\n          showSnackbar('PDF scaricato nella cartella Download', 'success');\n        }\n      } else if (response.pdf_content) {\n        // Se il PDF viene restituito come contenuto base64\n        const blob = new Blob([atob(response.pdf_content)], { type: 'application/pdf' });\n        const url = URL.createObjectURL(blob);\n        const link = document.createElement('a');\n        link.href = url;\n        link.download = `certificazione_${certificazione.numero_certificato}.pdf`;\n        document.body.appendChild(link);\n        link.click();\n        document.body.removeChild(link);\n        URL.revokeObjectURL(url);\n        showSnackbar('PDF scaricato con successo', 'success');\n      } else {\n        showSnackbar('Errore: Formato PDF non riconosciuto', 'error');\n      }\n    } catch (error) {\n      console.error('Errore generazione PDF:', error);\n      showSnackbar('Errore nella generazione del PDF: ' + (error.message || 'Errore sconosciuto'), 'error');\n    } finally {\n      setOperationInProgress(false);\n    }\n  };\n\n  const handleDeleteCertificazione = async (certificazione) => {\n    if (window.confirm(`Sei sicuro di voler eliminare la certificazione ${certificazione.numero_certificato}?`)) {\n      try {\n        setOperationInProgress(true);\n        await certificazioneService.deleteCertificazione(cantiereId, certificazione.id_certificazione);\n        showSnackbar('Certificazione eliminata con successo', 'success');\n        await loadCertificazioni();\n        calculateStatistics();\n      } catch (error) {\n        showSnackbar('Errore nell\\'eliminazione della certificazione: ' + (error.message || 'Errore sconosciuto'), 'error');\n      } finally {\n        setOperationInProgress(false);\n      }\n    }\n  };\n\n  // Operazioni bulk\n  const handleBulkDelete = async () => {\n    if (bulkSelection.length === 0) {\n      showSnackbar('Seleziona almeno un elemento', 'warning');\n      return;\n    }\n\n    if (window.confirm(`Sei sicuro di voler eliminare ${bulkSelection.length} certificazioni?`)) {\n      try {\n        setOperationInProgress(true);\n        for (const id of bulkSelection) {\n          await certificazioneService.deleteCertificazione(cantiereId, id);\n        }\n        showSnackbar(`${bulkSelection.length} certificazioni eliminate con successo`, 'success');\n        setBulkSelection([]);\n        await loadCertificazioni();\n        calculateStatistics();\n      } catch (error) {\n        showSnackbar('Errore nell\\'eliminazione delle certificazioni', 'error');\n      } finally {\n        setOperationInProgress(false);\n      }\n    }\n  };\n\n  const handleBulkExport = async () => {\n    if (bulkSelection.length === 0) {\n      showSnackbar('Seleziona almeno un elemento', 'warning');\n      return;\n    }\n\n    try {\n      setOperationInProgress(true);\n      // Implementa export bulk\n      const selectedCerts = certificazioni.filter(cert =>\n        bulkSelection.includes(cert.id_certificazione)\n      );\n\n      // Crea CSV\n      const csvContent = generateCSV(selectedCerts);\n      downloadCSV(csvContent, `certificazioni_${new Date().toISOString().split('T')[0]}.csv`);\n\n      showSnackbar(`${bulkSelection.length} certificazioni esportate`, 'success');\n    } catch (error) {\n      showSnackbar('Errore nell\\'esportazione', 'error');\n    } finally {\n      setOperationInProgress(false);\n    }\n  };\n\n  // Funzioni di export\n  const generateCSV = (data) => {\n    const headers = ['ID Cavo', 'Numero Certificato', 'Data', 'Operatore', 'Strumento', 'Lunghezza', 'Isolamento', 'Risultato'];\n    const rows = data.map(cert => [\n      cert.id_cavo,\n      cert.numero_certificato,\n      new Date(cert.data_certificazione).toLocaleDateString(),\n      cert.operatore,\n      cert.strumento,\n      cert.lunghezza_misurata,\n      cert.valore_isolamento,\n      cert.risultato_finale\n    ]);\n\n    return [headers, ...rows].map(row => row.join(',')).join('\\n');\n  };\n\n  const downloadCSV = (content, filename) => {\n    const blob = new Blob([content], { type: 'text/csv;charset=utf-8;' });\n    const link = document.createElement('a');\n    if (link.download !== undefined) {\n      const url = URL.createObjectURL(blob);\n      link.setAttribute('href', url);\n      link.setAttribute('download', filename);\n      link.style.visibility = 'hidden';\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n    }\n  };\n\n  const handleExportAll = () => {\n    const csvContent = generateCSV(filteredCertificazioni);\n    downloadCSV(csvContent, `tutte_certificazioni_${new Date().toISOString().split('T')[0]}.csv`);\n    showSnackbar('Esportazione completata', 'success');\n  };\n\n  // Espone metodi tramite ref\n  useImperativeHandle(ref, () => ({\n    handleOptionSelect: (option) => {\n      if (option === 'creaCertificazione') {\n        openCreateDialog();\n      } else if (option === 'visualizzaCertificazioni') {\n        setActiveTab(1);\n      }\n    }\n  }));\n\n  // Calcola elementi per paginazione\n  const getCurrentPageItems = (items) => {\n    const startIndex = (currentPage - 1) * itemsPerPage;\n    const endIndex = startIndex + itemsPerPage;\n    return items.slice(startIndex, endIndex);\n  };\n\n  const getTotalPages = (items) => Math.ceil(items.length / itemsPerPage);\n\n  // Ottieni opzioni uniche per filtri\n  const getUniqueValues = (array, field) => {\n    return [...new Set(array.map(item => item[field]).filter(Boolean))];\n  };\n\n  // Componente Dashboard con statistiche\n  const renderDashboard = () => (\n    <Grid container spacing={3} sx={{ mb: 3 }}>\n      <Grid item xs={12} md={2}>\n        <Card sx={{ background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)', color: 'white' }}>\n          <CardContent sx={{ textAlign: 'center', py: 2 }}>\n            <CableIcon sx={{ fontSize: 40, mb: 1 }} />\n            <Typography variant=\"h4\" fontWeight=\"bold\">\n              {statistics.totaleCavi}\n            </Typography>\n            <Typography variant=\"body2\">\n              Totale Cavi\n            </Typography>\n          </CardContent>\n        </Card>\n      </Grid>\n\n      <Grid item xs={12} md={2}>\n        <Card sx={{ background: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)', color: 'white' }}>\n          <CardContent sx={{ textAlign: 'center', py: 2 }}>\n            <CheckIcon sx={{ fontSize: 40, mb: 1 }} />\n            <Typography variant=\"h4\" fontWeight=\"bold\">\n              {statistics.caviCertificati}\n            </Typography>\n            <Typography variant=\"body2\">\n              Certificati\n            </Typography>\n          </CardContent>\n        </Card>\n      </Grid>\n\n      <Grid item xs={12} md={2}>\n        <Card sx={{ background: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)', color: 'white' }}>\n          <CardContent sx={{ textAlign: 'center', py: 2 }}>\n            <BuildIcon sx={{ fontSize: 40, mb: 1 }} />\n            <Typography variant=\"h4\" fontWeight=\"bold\">\n              {statistics.caviCertificabili}\n            </Typography>\n            <Typography variant=\"body2\">\n              Certificabili\n            </Typography>\n          </CardContent>\n        </Card>\n      </Grid>\n\n      <Grid item xs={12} md={2}>\n        <Card sx={{ background: 'linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%)', color: '#333' }}>\n          <CardContent sx={{ textAlign: 'center', py: 2 }}>\n            <CheckIcon sx={{ fontSize: 40, mb: 1 }} />\n            <Typography variant=\"h4\" fontWeight=\"bold\">\n              {statistics.caviCollegati}\n            </Typography>\n            <Typography variant=\"body2\">\n              Collegati\n            </Typography>\n          </CardContent>\n        </Card>\n      </Grid>\n\n      <Grid item xs={12} md={2}>\n        <Card sx={{ background: 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)', color: 'white' }}>\n          <CardContent sx={{ textAlign: 'center', py: 2 }}>\n            <ReportIcon sx={{ fontSize: 40, mb: 1 }} />\n            <Typography variant=\"h4\" fontWeight=\"bold\">\n              {statistics.percentualeCompletamento}%\n            </Typography>\n            <Typography variant=\"body2\">\n              Completamento\n            </Typography>\n          </CardContent>\n        </Card>\n      </Grid>\n\n      <Grid item xs={12} md={2}>\n        <Card sx={{ background: 'linear-gradient(135deg, #fa709a 0%, #fee140 100%)', color: 'white' }}>\n          <CardContent sx={{ textAlign: 'center', py: 2 }}>\n            <ScheduleIcon sx={{ fontSize: 40, mb: 1 }} />\n            <Typography variant=\"h4\" fontWeight=\"bold\">\n              {statistics.certificazioniOggi}\n            </Typography>\n            <Typography variant=\"body2\">\n              Oggi\n            </Typography>\n          </CardContent>\n        </Card>\n      </Grid>\n\n      <Grid item xs={12} md={2}>\n        <Card sx={{ background: 'linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)', color: '#333' }}>\n          <CardContent sx={{ textAlign: 'center', py: 2 }}>\n            <ScienceIcon sx={{ fontSize: 40, mb: 1 }} />\n            <Typography variant=\"h4\" fontWeight=\"bold\">\n              {statistics.certificazioniSettimana}\n            </Typography>\n            <Typography variant=\"body2\">\n              Questa Settimana\n            </Typography>\n          </CardContent>\n        </Card>\n      </Grid>\n    </Grid>\n  );\n\n  // Componente barra di ricerca avanzata\n  const renderSearchAndFilters = () => (\n    <Paper sx={{ p: 2, mb: 3 }}>\n      <Grid container spacing={2} alignItems=\"center\">\n        <Grid item xs={12} md={4}>\n          <TextField\n            fullWidth\n            placeholder=\"Cerca cavi, certificazioni, operatori...\"\n            value={searchTerm}\n            onChange={(e) => setSearchTerm(e.target.value)}\n            InputProps={{\n              startAdornment: (\n                <InputAdornment position=\"start\">\n                  <SearchIcon />\n                </InputAdornment>\n              ),\n              endAdornment: searchTerm && (\n                <InputAdornment position=\"end\">\n                  <IconButton onClick={() => setSearchTerm('')} size=\"small\">\n                    <ClearIcon />\n                  </IconButton>\n                </InputAdornment>\n              )\n            }}\n          />\n        </Grid>\n\n        <Grid item xs={12} md={2}>\n          <Button\n            fullWidth\n            variant=\"outlined\"\n            startIcon={<FilterIcon />}\n            onClick={() => setAdvancedFiltersOpen(!advancedFiltersOpen)}\n            color={Object.values(filters).some(f => f) ? 'primary' : 'inherit'}\n          >\n            Filtri {Object.values(filters).filter(f => f).length > 0 && `(${Object.values(filters).filter(f => f).length})`}\n          </Button>\n        </Grid>\n\n        <Grid item xs={12} md={2}>\n          <Button\n            fullWidth\n            variant=\"outlined\"\n            startIcon={bulkMode ? <ClearIcon /> : <CheckIcon />}\n            onClick={toggleBulkMode}\n            color={bulkMode ? 'secondary' : 'inherit'}\n            disabled={activeTab === 0}\n          >\n            {bulkMode ? 'Esci Selezione' : 'Selezione Multipla'}\n          </Button>\n        </Grid>\n\n        <Grid item xs={12} md={2}>\n          <Button\n            fullWidth\n            variant=\"outlined\"\n            startIcon={<ExportIcon />}\n            onClick={handleExportAll}\n            disabled={activeTab === 0 || filteredCertificazioni.length === 0}\n          >\n            {activeTab === 0 ? 'Export (solo certificazioni)' : 'Esporta Certificazioni'}\n          </Button>\n        </Grid>\n\n        <Grid item xs={12} md={2}>\n          <Button\n            fullWidth\n            variant=\"contained\"\n            startIcon={<AddIcon />}\n            onClick={openCreateDialog}\n          >\n            Nuova Certificazione\n          </Button>\n        </Grid>\n      </Grid>\n\n      {/* Filtri avanzati - Diversi per ogni tab */}\n      <Collapse in={advancedFiltersOpen}>\n        <Divider sx={{ my: 2 }} />\n        <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mb: 2 }}>\n          {activeTab === 0 ? 'Filtri per Cavi' : 'Filtri per Certificazioni'}\n        </Typography>\n\n        <Grid container spacing={2}>\n          {/* Filtri per tab Cavi */}\n          {activeTab === 0 && (\n            <>\n              <Grid item xs={12} md={3}>\n                <FormControl fullWidth size=\"small\">\n                  <InputLabel>Stato Installazione</InputLabel>\n                  <Select\n                    value={filters.stato}\n                    onChange={(e) => setFilters({...filters, stato: e.target.value})}\n                  >\n                    <MenuItem value=\"\">Tutti</MenuItem>\n                    <MenuItem value=\"INSTALLATO\">Installato</MenuItem>\n                    <MenuItem value=\"NON_INSTALLATO\">Non Installato</MenuItem>\n                    <MenuItem value=\"IN_CORSO\">In Corso</MenuItem>\n                  </Select>\n                </FormControl>\n              </Grid>\n\n              <Grid item xs={12} md={3}>\n                <FormControl fullWidth size=\"small\">\n                  <InputLabel>Tipologia</InputLabel>\n                  <Select\n                    value={filters.tipologia}\n                    onChange={(e) => setFilters({...filters, tipologia: e.target.value})}\n                  >\n                    <MenuItem value=\"\">Tutte</MenuItem>\n                    {[...new Set(cavi.map(c => c.tipologia))].filter(Boolean).map(tip => (\n                      <MenuItem key={tip} value={tip}>{tip}</MenuItem>\n                    ))}\n                  </Select>\n                </FormControl>\n              </Grid>\n\n              <Grid item xs={12} md={3}>\n                <FormControl fullWidth size=\"small\">\n                  <InputLabel>Stato Certificazione</InputLabel>\n                  <Select\n                    value={filters.certificazione}\n                    onChange={(e) => setFilters({...filters, certificazione: e.target.value})}\n                  >\n                    <MenuItem value=\"\">Tutti</MenuItem>\n                    <MenuItem value=\"CERTIFICATO\">Certificato</MenuItem>\n                    <MenuItem value=\"NON_CERTIFICATO\">Non Certificato</MenuItem>\n                  </Select>\n                </FormControl>\n              </Grid>\n            </>\n          )}\n\n          {/* Filtri per tab Certificazioni */}\n          {activeTab === 1 && (\n            <>\n              <Grid item xs={12} md={3}>\n                <FormControl fullWidth size=\"small\">\n                  <InputLabel>Operatore</InputLabel>\n                  <Select\n                    value={filters.operatore}\n                    onChange={(e) => setFilters({...filters, operatore: e.target.value})}\n                  >\n                    <MenuItem value=\"\">Tutti</MenuItem>\n                    {[...new Set(certificazioni.map(c => c.operatore))].filter(Boolean).map(op => (\n                      <MenuItem key={op} value={op}>{op}</MenuItem>\n                    ))}\n                  </Select>\n                </FormControl>\n              </Grid>\n\n              <Grid item xs={12} md={3}>\n                <FormControl fullWidth size=\"small\">\n                  <InputLabel>Risultato Test</InputLabel>\n                  <Select\n                    value={filters.risultatoTest}\n                    onChange={(e) => setFilters({...filters, risultatoTest: e.target.value})}\n                  >\n                    <MenuItem value=\"\">Tutti</MenuItem>\n                    <MenuItem value=\"CONFORME\">Conforme</MenuItem>\n                    <MenuItem value=\"NON_CONFORME\">Non Conforme</MenuItem>\n                    <MenuItem value=\"DA_VERIFICARE\">Da Verificare</MenuItem>\n                  </Select>\n                </FormControl>\n              </Grid>\n\n              <Grid item xs={12} md={3}>\n                <TextField\n                  fullWidth\n                  size=\"small\"\n                  label=\"Isolamento Min (MΩ)\"\n                  type=\"number\"\n                  value={filters.valoreIsolamento}\n                  onChange={(e) => setFilters({...filters, valoreIsolamento: e.target.value})}\n                  placeholder=\"es. 500\"\n                />\n              </Grid>\n\n              <Grid item xs={12} md={3}>\n                <TextField\n                  fullWidth\n                  size=\"small\"\n                  label=\"Data Inizio\"\n                  type=\"date\"\n                  value={filters.dataInizio}\n                  onChange={(e) => setFilters({...filters, dataInizio: e.target.value})}\n                  InputLabelProps={{ shrink: true }}\n                />\n              </Grid>\n\n              <Grid item xs={12} md={3}>\n                <TextField\n                  fullWidth\n                  size=\"small\"\n                  label=\"Data Fine\"\n                  type=\"date\"\n                  value={filters.dataFine}\n                  onChange={(e) => setFilters({...filters, dataFine: e.target.value})}\n                  InputLabelProps={{ shrink: true }}\n                />\n              </Grid>\n            </>\n          )}\n\n          <Grid item xs={12}>\n            <Stack direction=\"row\" spacing={1} justifyContent=\"flex-end\">\n              <Button\n                variant=\"outlined\"\n                size=\"small\"\n                onClick={() => setFilters({\n                  stato: '', tipologia: '', operatore: '', dataInizio: '',\n                  dataFine: '', valoreIsolamento: '', risultatoTest: '', strumento: '',\n                  certificazione: ''\n                })}\n              >\n                Pulisci Tutti i Filtri\n              </Button>\n            </Stack>\n          </Grid>\n        </Grid>\n      </Collapse>\n\n      {/* Barra azioni bulk */}\n      {bulkMode && bulkSelection.length > 0 && (\n        <>\n          <Divider sx={{ my: 2 }} />\n          <Stack direction=\"row\" spacing={2} alignItems=\"center\">\n            <Typography variant=\"body2\">\n              {bulkSelection.length} elementi selezionati\n            </Typography>\n            <Button\n              size=\"small\"\n              variant=\"outlined\"\n              onClick={selectAllItems}\n            >\n              Seleziona Tutto\n            </Button>\n            <Button\n              size=\"small\"\n              variant=\"outlined\"\n              onClick={clearSelection}\n            >\n              Deseleziona\n            </Button>\n            <Button\n              size=\"small\"\n              variant=\"outlined\"\n              startIcon={<ExportIcon />}\n              onClick={handleBulkExport}\n            >\n              Esporta Selezionati\n            </Button>\n            <Button\n              size=\"small\"\n              variant=\"outlined\"\n              color=\"error\"\n              startIcon={<DeleteIcon />}\n              onClick={handleBulkDelete}\n            >\n              Elimina Selezionati\n            </Button>\n          </Stack>\n        </>\n      )}\n    </Paper>\n  );\n\n  // Renderizza la tabella dei cavi\n  const renderCaviTable = () => {\n    const currentItems = getCurrentPageItems(filteredCavi);\n\n    if (filteredCavi.length === 0) {\n      return (\n        <Alert severity=\"info\">\n          {searchTerm || filters.stato || filters.tipologia\n            ? 'Nessun cavo trovato con i filtri applicati'\n            : 'Nessun cavo disponibile'}\n        </Alert>\n      );\n    }\n\n    return (\n      <>\n        <TableContainer component={Paper}>\n          <Table size=\"small\">\n            <TableHead>\n              <TableRow>\n                <TableCell>ID Cavo</TableCell>\n                <TableCell>Tipologia</TableCell>\n                <TableCell>Sezione</TableCell>\n                <TableCell>Partenza</TableCell>\n                <TableCell>Arrivo</TableCell>\n                <TableCell>Metri</TableCell>\n                <TableCell>Stato</TableCell>\n                <TableCell>Collegamenti</TableCell>\n                <TableCell>Certificato</TableCell>\n                <TableCell>Azioni</TableCell>\n              </TableRow>\n            </TableHead>\n            <TableBody>\n              {currentItems.map((cavo) => {\n                const isCertificato = isCavoCertificato(cavo.id_cavo);\n                const puoCertificare = puoEssereCertificato(cavo);\n                const messaggioErrore = !puoCertificare ? getMessaggioErroreCertificazione(cavo) : '';\n\n                return (\n                  <TableRow key={cavo.id_cavo}>\n                    <TableCell>\n                      <Typography variant=\"body2\" fontWeight=\"medium\">\n                        {cavo.id_cavo}\n                      </Typography>\n                    </TableCell>\n                    <TableCell>{cavo.tipologia}</TableCell>\n                    <TableCell>{cavo.sezione}</TableCell>\n                    <TableCell>{cavo.ubicazione_partenza}</TableCell>\n                    <TableCell>{cavo.ubicazione_arrivo}</TableCell>\n                    <TableCell>{cavo.metratura_reale || cavo.metri_teorici} m</TableCell>\n                    <TableCell>\n                      <Chip\n                        size=\"small\"\n                        label={cavo.stato_installazione}\n                        color={cavo.stato_installazione === 'INSTALLATO' ? 'success' : 'default'}\n                      />\n                    </TableCell>\n                    <TableCell>\n                      {(() => {\n                        const collegamenti = cavo.collegamenti || 0;\n                        const statoCollegamento = collegamenti === 0 ? 'Non collegato' :\n                                                 collegamenti === 1 ? 'Solo partenza' :\n                                                 collegamenti === 2 ? 'Solo arrivo' :\n                                                 collegamenti === 3 ? 'Completo' :\n                                                 'Sconosciuto';\n                        const colore = collegamenti === 3 ? 'success' :\n                                      collegamenti === 0 ? 'error' : 'warning';\n\n                        return (\n                          <Tooltip title={`Partenza: ${cavo.responsabile_partenza || 'Non collegato'} | Arrivo: ${cavo.responsabile_arrivo || 'Non collegato'}`}>\n                            <Chip\n                              size=\"small\"\n                              label={statoCollegamento}\n                              color={colore}\n                              icon={collegamenti === 3 ? <CheckIcon /> : <WarningIcon />}\n                            />\n                          </Tooltip>\n                        );\n                      })()}\n                    </TableCell>\n                    <TableCell>\n                      {isCertificato ? (\n                        <Chip\n                          size=\"small\"\n                          icon={<CheckIcon />}\n                          label=\"Certificato\"\n                          color=\"success\"\n                        />\n                      ) : (\n                        <Chip\n                          size=\"small\"\n                          icon={<WarningIcon />}\n                          label=\"Non certificato\"\n                          color=\"warning\"\n                        />\n                      )}\n                    </TableCell>\n                    <TableCell>\n                      {isCertificato ? (\n                        <Tooltip title=\"Cavo già certificato\">\n                          <Chip\n                            icon={<CheckIcon />}\n                            label=\"Certificato\"\n                            color=\"success\"\n                            size=\"small\"\n                          />\n                        </Tooltip>\n                      ) : puoCertificare ? (\n                        <Tooltip title=\"Crea certificazione per questo cavo\">\n                          <IconButton\n                            size=\"small\"\n                            onClick={() => openCreateDialog(cavo)}\n                            color=\"primary\"\n                          >\n                            <AddIcon />\n                          </IconButton>\n                        </Tooltip>\n                      ) : (\n                        <Tooltip title={messaggioErrore}>\n                          <span>\n                            <IconButton\n                              size=\"small\"\n                              disabled\n                              onClick={() => showSnackbar(messaggioErrore, 'warning')}\n                            >\n                              <BlockIcon />\n                            </IconButton>\n                          </span>\n                        </Tooltip>\n                      )}\n                    </TableCell>\n                  </TableRow>\n                );\n              })}\n            </TableBody>\n          </Table>\n        </TableContainer>\n\n        {getTotalPages(filteredCavi) > 1 && (\n          <Box sx={{ display: 'flex', justifyContent: 'center', mt: 2 }}>\n            <Pagination\n              count={getTotalPages(filteredCavi)}\n              page={currentPage}\n              onChange={(event, value) => setCurrentPage(value)}\n              color=\"primary\"\n            />\n          </Box>\n        )}\n      </>\n    );\n  };\n\n  // Renderizza la tabella delle certificazioni\n  const renderCertificazioniTable = () => {\n    const currentItems = getCurrentPageItems(filteredCertificazioni);\n\n    if (filteredCertificazioni.length === 0) {\n      return (\n        <Alert severity=\"info\">\n          {searchTerm || filters.operatore\n            ? 'Nessuna certificazione trovata con i filtri applicati'\n            : 'Nessuna certificazione disponibile'}\n        </Alert>\n      );\n    }\n\n    return (\n      <>\n        <TableContainer component={Paper}>\n          <Table size=\"small\">\n            <TableHead>\n              <TableRow>\n                {bulkMode && (\n                  <TableCell padding=\"checkbox\">\n                    <IconButton\n                      size=\"small\"\n                      onClick={bulkSelection.length === filteredCertificazioni.length ? clearSelection : selectAllItems}\n                    >\n                      {bulkSelection.length === filteredCertificazioni.length ? <ClearIcon /> : <CheckIcon />}\n                    </IconButton>\n                  </TableCell>\n                )}\n                <TableCell>\n                  <Stack direction=\"row\" alignItems=\"center\" spacing={1}>\n                    <Typography variant=\"body2\" fontWeight=\"bold\">N° Certificato</Typography>\n                    <IconButton size=\"small\" onClick={() => {\n                      setSortBy('numero_certificato');\n                      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');\n                    }}>\n                      {sortBy === 'numero_certificato' ? (sortOrder === 'asc' ? <ExpandLessIcon /> : <ExpandMoreIcon />) : <ExpandMoreIcon />}\n                    </IconButton>\n                  </Stack>\n                </TableCell>\n                <TableCell>ID Cavo</TableCell>\n                <TableCell>\n                  <Stack direction=\"row\" alignItems=\"center\" spacing={1}>\n                    <Typography variant=\"body2\" fontWeight=\"bold\">Data</Typography>\n                    <IconButton size=\"small\" onClick={() => {\n                      setSortBy('data_certificazione');\n                      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');\n                    }}>\n                      {sortBy === 'data_certificazione' ? (sortOrder === 'asc' ? <ExpandLessIcon /> : <ExpandMoreIcon />) : <ExpandMoreIcon />}\n                    </IconButton>\n                  </Stack>\n                </TableCell>\n                <TableCell>Operatore</TableCell>\n                <TableCell>Strumento</TableCell>\n                <TableCell>Lunghezza</TableCell>\n                <TableCell>Isolamento</TableCell>\n                <TableCell>Risultato</TableCell>\n                <TableCell>Azioni</TableCell>\n              </TableRow>\n            </TableHead>\n            <TableBody>\n              {currentItems.map((cert) => (\n                <TableRow\n                  key={cert.id_certificazione}\n                  selected={bulkSelection.includes(cert.id_certificazione)}\n                  hover\n                >\n                  {bulkMode && (\n                    <TableCell padding=\"checkbox\">\n                      <IconButton\n                        size=\"small\"\n                        onClick={() => toggleItemSelection(cert.id_certificazione)}\n                        color={bulkSelection.includes(cert.id_certificazione) ? 'primary' : 'default'}\n                      >\n                        {bulkSelection.includes(cert.id_certificazione) ? <CheckIcon /> : <AddIcon />}\n                      </IconButton>\n                    </TableCell>\n                  )}\n                  <TableCell>\n                    <Typography variant=\"body2\" fontWeight=\"medium\">\n                      {cert.numero_certificato}\n                    </Typography>\n                  </TableCell>\n                  <TableCell>\n                    <Chip size=\"small\" label={cert.id_cavo} variant=\"outlined\" />\n                  </TableCell>\n                  <TableCell>{new Date(cert.data_certificazione).toLocaleDateString()}</TableCell>\n                  <TableCell>\n                    <Stack direction=\"row\" alignItems=\"center\" spacing={1}>\n                      <PersonIcon fontSize=\"small\" />\n                      <Typography variant=\"body2\">{cert.operatore || cert.id_operatore}</Typography>\n                    </Stack>\n                  </TableCell>\n                  <TableCell>\n                    <Typography variant=\"body2\">\n                      {cert.id_strumento ?\n                        (() => {\n                          const strumento = strumenti.find(s => s.id_strumento === cert.id_strumento);\n                          return strumento ? `${strumento.nome} - ${strumento.marca}` : 'Strumento non trovato';\n                        })()\n                        : (cert.strumento_utilizzato || 'N/A')\n                      }\n                    </Typography>\n                  </TableCell>\n                  <TableCell>\n                    <Typography variant=\"body2\">{cert.lunghezza_misurata} m</Typography>\n                  </TableCell>\n                  <TableCell>\n                    <Chip\n                      size=\"small\"\n                      label={`${cert.valore_isolamento} MΩ`}\n                      color={parseFloat(cert.valore_isolamento) >= 500 ? 'success' : 'warning'}\n                      icon={parseFloat(cert.valore_isolamento) >= 500 ? <CheckIcon /> : <WarningIcon />}\n                    />\n                  </TableCell>\n                  <TableCell>\n                    <Chip\n                      size=\"small\"\n                      label={cert.risultato_finale || 'CONFORME'}\n                      color={cert.risultato_finale === 'CONFORME' ? 'success' : cert.risultato_finale === 'NON_CONFORME' ? 'error' : 'warning'}\n                    />\n                  </TableCell>\n                  <TableCell>\n                    <Stack direction=\"row\" spacing={0.5}>\n                      <Tooltip title=\"Visualizza dettagli\">\n                        <IconButton\n                          size=\"small\"\n                          onClick={() => {\n                            setSelectedItem(cert);\n                            setDialogType('view');\n                            setOpenDialog(true);\n                          }}\n                        >\n                          <ViewIcon />\n                        </IconButton>\n                      </Tooltip>\n                      <Tooltip title=\"Genera PDF\">\n                        <IconButton\n                          size=\"small\"\n                          onClick={() => handleGeneratePdf(cert)}\n                          disabled={operationInProgress}\n                        >\n                          <PdfIcon />\n                        </IconButton>\n                      </Tooltip>\n                      <Tooltip title=\"Elimina\">\n                        <IconButton\n                          size=\"small\"\n                          color=\"error\"\n                          onClick={() => handleDeleteCertificazione(cert)}\n                          disabled={operationInProgress}\n                        >\n                          <DeleteIcon />\n                        </IconButton>\n                      </Tooltip>\n                    </Stack>\n                  </TableCell>\n                </TableRow>\n              ))}\n            </TableBody>\n          </Table>\n        </TableContainer>\n\n        {getTotalPages(filteredCertificazioni) > 1 && (\n          <Box sx={{ display: 'flex', justifyContent: 'center', mt: 2 }}>\n            <Pagination\n              count={getTotalPages(filteredCertificazioni)}\n              page={currentPage}\n              onChange={(event, value) => setCurrentPage(value)}\n              color=\"primary\"\n            />\n          </Box>\n        )}\n      </>\n    );\n  };\n\n  // Renderizza il dialog per creare/modificare certificazione\n  const renderCertificazioneDialog = () => {\n    if (dialogType !== 'create' && dialogType !== 'edit') return null;\n\n    return (\n      <Dialog open={openDialog} onClose={closeDialog} maxWidth=\"md\" fullWidth>\n        <DialogTitle>\n          {dialogType === 'create' ? 'Nuova Certificazione' : 'Modifica Certificazione'}\n        </DialogTitle>\n        <DialogContent>\n          <Grid container spacing={2} sx={{ mt: 1 }}>\n            <Grid item xs={12} md={6}>\n              <Autocomplete\n                options={cavi.filter(cavo => {\n                  // Mostra solo cavi che possono essere certificati o quello già selezionato\n                  const isSelected = cavo.id_cavo === formData.id_cavo;\n                  const isNotCertified = !certificazioni.some(cert => cert.id_cavo === cavo.id_cavo);\n                  const canBeCertified = puoEssereCertificato(cavo);\n\n                  return isSelected || (isNotCertified && canBeCertified);\n                })}\n                getOptionLabel={(option) => `${option.id_cavo} - ${option.tipologia}`}\n                value={cavi.find(c => c.id_cavo === formData.id_cavo) || null}\n                onChange={(event, newValue) => {\n                  if (newValue) {\n                    handleCavoSelect(newValue);\n                  } else {\n                    setFormData(prev => ({ ...prev, id_cavo: '', lunghezza_misurata: '' }));\n                  }\n                }}\n                renderInput={(params) => (\n                  <TextField\n                    {...params}\n                    label=\"Cavo *\"\n                    placeholder=\"Seleziona un cavo certificabile\"\n                    required\n                    helperText=\"Solo cavi installati e completamente collegati\"\n                  />\n                )}\n                renderOption={(props, option) => {\n                  const canCertify = puoEssereCertificato(option);\n                  const collegamenti = option.collegamenti || 0;\n\n                  return (\n                    <Box component=\"li\" {...props}>\n                      <Box sx={{ width: '100%' }}>\n                        <Stack direction=\"row\" justifyContent=\"space-between\" alignItems=\"center\">\n                          <Box>\n                            <Typography variant=\"body2\" fontWeight=\"medium\">\n                              {option.id_cavo}\n                            </Typography>\n                            <Typography variant=\"caption\" color=\"text.secondary\">\n                              {option.tipologia} - {option.ubicazione_partenza} → {option.ubicazione_arrivo}\n                            </Typography>\n                          </Box>\n                          <Stack direction=\"row\" spacing={1}>\n                            <Chip\n                              size=\"small\"\n                              label={option.stato_installazione}\n                              color={option.stato_installazione === 'INSTALLATO' ? 'success' : 'default'}\n                            />\n                            <Chip\n                              size=\"small\"\n                              label={collegamenti === 3 ? 'Collegato' : 'Incompleto'}\n                              color={collegamenti === 3 ? 'success' : 'warning'}\n                            />\n                          </Stack>\n                        </Stack>\n                      </Box>\n                    </Box>\n                  );\n                }}\n              />\n            </Grid>\n\n            <Grid item xs={12} md={6}>\n              <TextField\n                fullWidth\n                label=\"Operatore *\"\n                value={formData.id_operatore}\n                onChange={(e) => handleFormChange('id_operatore', e.target.value)}\n                required\n              />\n            </Grid>\n\n            <Grid item xs={12} md={6}>\n              <FormControl fullWidth required>\n                <InputLabel>Strumento *</InputLabel>\n                <Select\n                  value={formData.id_strumento}\n                  onChange={(e) => handleFormChange('id_strumento', e.target.value)}\n                  label=\"Strumento *\"\n                >\n                  {strumenti.map((strumento) => (\n                    <MenuItem key={strumento.id_strumento} value={strumento.id_strumento}>\n                      {strumento.nome} - {strumento.marca} {strumento.modello} (S/N: {strumento.numero_serie})\n                    </MenuItem>\n                  ))}\n                </Select>\n              </FormControl>\n            </Grid>\n\n            <Grid item xs={12} md={6}>\n              <TextField\n                fullWidth\n                label=\"Lunghezza Misurata (m) *\"\n                type=\"number\"\n                value={formData.lunghezza_misurata}\n                onChange={(e) => handleFormChange('lunghezza_misurata', e.target.value)}\n                required\n              />\n            </Grid>\n\n            <Grid item xs={12} md={4}>\n              <FormControl fullWidth>\n                <InputLabel>Continuità</InputLabel>\n                <Select\n                  value={formData.valore_continuita}\n                  onChange={(e) => handleFormChange('valore_continuita', e.target.value)}\n                  label=\"Continuità\"\n                >\n                  <MenuItem value=\"OK\">OK</MenuItem>\n                  <MenuItem value=\"NOK\">NOK</MenuItem>\n                </Select>\n              </FormControl>\n            </Grid>\n\n            <Grid item xs={12} md={4}>\n              <TextField\n                fullWidth\n                label=\"Isolamento (MΩ) *\"\n                type=\"number\"\n                value={formData.valore_isolamento}\n                onChange={(e) => handleFormChange('valore_isolamento', e.target.value)}\n                required\n                helperText=\"Valore minimo consigliato: 500 MΩ\"\n              />\n            </Grid>\n\n            <Grid item xs={12} md={4}>\n              <FormControl fullWidth>\n                <InputLabel>Resistenza</InputLabel>\n                <Select\n                  value={formData.valore_resistenza}\n                  onChange={(e) => handleFormChange('valore_resistenza', e.target.value)}\n                  label=\"Resistenza\"\n                >\n                  <MenuItem value=\"OK\">OK</MenuItem>\n                  <MenuItem value=\"NOK\">NOK</MenuItem>\n                </Select>\n              </FormControl>\n            </Grid>\n\n            {/* Campi avanzati */}\n            <Grid item xs={12}>\n              <Divider sx={{ my: 2 }}>\n                <Typography variant=\"body2\" color=\"text.secondary\">\n                  Parametri Ambientali e Test Avanzati\n                </Typography>\n              </Divider>\n            </Grid>\n\n            <Grid item xs={12} md={3}>\n              <TextField\n                fullWidth\n                label=\"Temperatura Ambiente (°C)\"\n                type=\"number\"\n                value={formData.temperatura_ambiente}\n                onChange={(e) => handleFormChange('temperatura_ambiente', e.target.value)}\n                helperText=\"Temperatura durante il test\"\n              />\n            </Grid>\n\n            <Grid item xs={12} md={3}>\n              <TextField\n                fullWidth\n                label=\"Umidità (%)\"\n                type=\"number\"\n                value={formData.umidita}\n                onChange={(e) => handleFormChange('umidita', e.target.value)}\n                helperText=\"Umidità relativa\"\n              />\n            </Grid>\n\n            <Grid item xs={12} md={3}>\n              <TextField\n                fullWidth\n                label=\"Tensione di Prova (V)\"\n                type=\"number\"\n                value={formData.tensione_prova}\n                onChange={(e) => handleFormChange('tensione_prova', e.target.value)}\n                helperText=\"Tensione applicata per il test\"\n              />\n            </Grid>\n\n            <Grid item xs={12} md={3}>\n              <TextField\n                fullWidth\n                label=\"Durata Prova (min)\"\n                type=\"number\"\n                value={formData.durata_prova}\n                onChange={(e) => handleFormChange('durata_prova', e.target.value)}\n                helperText=\"Durata del test in minuti\"\n              />\n            </Grid>\n\n            <Grid item xs={12} md={6}>\n              <FormControl fullWidth>\n                <InputLabel>Risultato Finale</InputLabel>\n                <Select\n                  value={formData.risultato_finale}\n                  onChange={(e) => handleFormChange('risultato_finale', e.target.value)}\n                  label=\"Risultato Finale\"\n                >\n                  <MenuItem value=\"CONFORME\">\n                    <Stack direction=\"row\" alignItems=\"center\" spacing={1}>\n                      <CheckIcon color=\"success\" />\n                      <Typography>Conforme</Typography>\n                    </Stack>\n                  </MenuItem>\n                  <MenuItem value=\"NON_CONFORME\">\n                    <Stack direction=\"row\" alignItems=\"center\" spacing={1}>\n                      <ErrorIcon color=\"error\" />\n                      <Typography>Non Conforme</Typography>\n                    </Stack>\n                  </MenuItem>\n                  <MenuItem value=\"DA_VERIFICARE\">\n                    <Stack direction=\"row\" alignItems=\"center\" spacing={1}>\n                      <WarningIcon color=\"warning\" />\n                      <Typography>Da Verificare</Typography>\n                    </Stack>\n                  </MenuItem>\n                </Select>\n              </FormControl>\n            </Grid>\n\n            <Grid item xs={12}>\n              <TextField\n                fullWidth\n                label=\"Note\"\n                multiline\n                rows={3}\n                value={formData.note}\n                onChange={(e) => handleFormChange('note', e.target.value)}\n                placeholder=\"Inserisci eventuali note, osservazioni o anomalie riscontrate durante il test...\"\n              />\n            </Grid>\n          </Grid>\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={closeDialog}>Annulla</Button>\n          <Button\n            onClick={handleCreateCertificazione}\n            variant=\"contained\"\n            disabled={loading || !formData.id_cavo || !formData.id_operatore || !formData.id_strumento || !formData.valore_isolamento}\n            startIcon={loading ? <CircularProgress size={20} /> : <SaveIcon />}\n          >\n            {dialogType === 'create' ? 'Crea Certificazione' : 'Salva Modifiche'}\n          </Button>\n        </DialogActions>\n      </Dialog>\n    );\n  };\n\n  // Renderizza il dialog di visualizzazione dettagli\n  const renderViewDialog = () => {\n    if (dialogType !== 'view' || !selectedItem) return null;\n\n    return (\n      <Dialog open={openDialog} onClose={closeDialog} maxWidth=\"md\" fullWidth>\n        <DialogTitle>\n          Dettagli Certificazione - {selectedItem.numero_certificato}\n        </DialogTitle>\n        <DialogContent>\n          <Grid container spacing={2} sx={{ mt: 1 }}>\n            <Grid item xs={12} md={6}>\n              <Card variant=\"outlined\">\n                <CardContent>\n                  <Typography variant=\"h6\" gutterBottom>\n                    Informazioni Cavo\n                  </Typography>\n                  <Typography variant=\"body2\" color=\"text.secondary\">\n                    ID Cavo: <strong>{selectedItem.id_cavo}</strong>\n                  </Typography>\n                  <Typography variant=\"body2\" color=\"text.secondary\">\n                    Lunghezza Misurata: <strong>{selectedItem.lunghezza_misurata} m</strong>\n                  </Typography>\n                </CardContent>\n              </Card>\n            </Grid>\n\n            <Grid item xs={12} md={6}>\n              <Card variant=\"outlined\">\n                <CardContent>\n                  <Typography variant=\"h6\" gutterBottom>\n                    Informazioni Certificazione\n                  </Typography>\n                  <Typography variant=\"body2\" color=\"text.secondary\">\n                    Numero: <strong>{selectedItem.numero_certificato}</strong>\n                  </Typography>\n                  <Typography variant=\"body2\" color=\"text.secondary\">\n                    Data: <strong>{new Date(selectedItem.data_certificazione).toLocaleDateString()}</strong>\n                  </Typography>\n                  <Typography variant=\"body2\" color=\"text.secondary\">\n                    Operatore: <strong>{selectedItem.operatore || selectedItem.id_operatore}</strong>\n                  </Typography>\n                </CardContent>\n              </Card>\n            </Grid>\n\n            <Grid item xs={12}>\n              <Card variant=\"outlined\">\n                <CardContent>\n                  <Typography variant=\"h6\" gutterBottom>\n                    Risultati Test\n                  </Typography>\n                  <Grid container spacing={2}>\n                    <Grid item xs={4}>\n                      <Typography variant=\"body2\" color=\"text.secondary\">\n                        Continuità\n                      </Typography>\n                      <Chip\n                        size=\"small\"\n                        label={selectedItem.valore_continuita}\n                        color={selectedItem.valore_continuita === 'OK' ? 'success' : 'error'}\n                      />\n                    </Grid>\n                    <Grid item xs={4}>\n                      <Typography variant=\"body2\" color=\"text.secondary\">\n                        Isolamento\n                      </Typography>\n                      <Chip\n                        size=\"small\"\n                        label={`${selectedItem.valore_isolamento} MΩ`}\n                        color={parseFloat(selectedItem.valore_isolamento) >= 500 ? 'success' : 'warning'}\n                      />\n                    </Grid>\n                    <Grid item xs={4}>\n                      <Typography variant=\"body2\" color=\"text.secondary\">\n                        Resistenza\n                      </Typography>\n                      <Chip\n                        size=\"small\"\n                        label={selectedItem.valore_resistenza}\n                        color={selectedItem.valore_resistenza === 'OK' ? 'success' : 'error'}\n                      />\n                    </Grid>\n                  </Grid>\n                </CardContent>\n              </Card>\n            </Grid>\n\n            {selectedItem.note && (\n              <Grid item xs={12}>\n                <Card variant=\"outlined\">\n                  <CardContent>\n                    <Typography variant=\"h6\" gutterBottom>\n                      Note\n                    </Typography>\n                    <Typography variant=\"body2\">\n                      {selectedItem.note}\n                    </Typography>\n                  </CardContent>\n                </Card>\n              </Grid>\n            )}\n          </Grid>\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={closeDialog}>Chiudi</Button>\n          <Button\n            onClick={() => handleGeneratePdf(selectedItem)}\n            variant=\"contained\"\n            startIcon={<PdfIcon />}\n            disabled={loading}\n          >\n            Genera PDF\n          </Button>\n        </DialogActions>\n      </Dialog>\n    );\n  };\n\n  // Renderizza le statistiche\n  const renderStats = () => {\n    const totalCavi = cavi.length;\n    const caviInstallati = cavi.filter(c => c.stato_installazione === 'INSTALLATO').length;\n    const caviCertificati = certificazioni.length;\n    const percentualeCertificazione = totalCavi > 0 ? Math.round((caviCertificati / caviInstallati) * 100) : 0;\n\n    return (\n      <Grid container spacing={2} sx={{ mb: 3 }}>\n        <Grid item xs={12} sm={6} md={3}>\n          <Card>\n            <CardContent>\n              <Typography color=\"text.secondary\" gutterBottom>\n                Cavi Totali\n              </Typography>\n              <Typography variant=\"h4\">\n                {totalCavi}\n              </Typography>\n            </CardContent>\n          </Card>\n        </Grid>\n        <Grid item xs={12} sm={6} md={3}>\n          <Card>\n            <CardContent>\n              <Typography color=\"text.secondary\" gutterBottom>\n                Cavi Installati\n              </Typography>\n              <Typography variant=\"h4\">\n                {caviInstallati}\n              </Typography>\n            </CardContent>\n          </Card>\n        </Grid>\n        <Grid item xs={12} sm={6} md={3}>\n          <Card>\n            <CardContent>\n              <Typography color=\"text.secondary\" gutterBottom>\n                Certificazioni\n              </Typography>\n              <Typography variant=\"h4\">\n                {caviCertificati}\n              </Typography>\n            </CardContent>\n          </Card>\n        </Grid>\n        <Grid item xs={12} sm={6} md={3}>\n          <Card>\n            <CardContent>\n              <Typography color=\"text.secondary\" gutterBottom>\n                % Certificazione\n              </Typography>\n              <Typography variant=\"h4\" color={percentualeCertificazione >= 80 ? 'success.main' : 'warning.main'}>\n                {percentualeCertificazione}%\n              </Typography>\n            </CardContent>\n          </Card>\n        </Grid>\n      </Grid>\n    );\n  };\n\n  return (\n    <Container maxWidth=\"xl\" sx={{ py: 3 }}>\n      {/* Header con titolo e azioni rapide */}\n      <Box sx={{ mb: 4 }}>\n        <Typography variant=\"h4\" component=\"h1\" gutterBottom fontWeight=\"bold\">\n          🔌 Sistema di Certificazione Cavi\n        </Typography>\n        <Typography variant=\"body1\" color=\"text.secondary\" sx={{ mb: 2 }}>\n          Gestione completa delle certificazioni elettriche secondo standard CEI 64-8\n        </Typography>\n\n        {/* Messaggio di aiuto */}\n        <Paper sx={{ p: 2, bgcolor: 'info.light', color: 'info.contrastText', mb: 2 }}>\n          <Stack direction=\"row\" alignItems=\"center\" spacing={2}>\n            <InfoIcon />\n            <Box>\n              <Typography variant=\"body2\" fontWeight=\"bold\">\n                Come utilizzare il sistema:\n              </Typography>\n              <Typography variant=\"caption\">\n                • <strong>Tab \"Cavi da Certificare\":</strong> Visualizza tutti i cavi posati e clicca sul \"+\" per certificare\n                • <strong>Certificazione:</strong> Richiede solo che il cavo sia posato, il collegamento può essere gestito al momento\n                • <strong>Tab \"Certificazioni Completate\":</strong> Gestisci le certificazioni esistenti, attiva la \"Selezione\" per operazioni multiple\n                • <strong>Filtri:</strong> Usa i filtri specifici per ogni tab per trovare rapidamente quello che cerchi\n              </Typography>\n            </Box>\n          </Stack>\n        </Paper>\n      </Box>\n\n      {/* Dashboard con statistiche */}\n      {renderDashboard()}\n\n      {/* Progress bar per operazioni in corso */}\n      {(loading || operationInProgress) && (\n        <Box sx={{ mb: 2 }}>\n          <LinearProgress />\n          {progress > 0 && (\n            <Typography variant=\"caption\" color=\"text.secondary\" sx={{ mt: 1 }}>\n              Caricamento... {progress}%\n            </Typography>\n          )}\n        </Box>\n      )}\n\n      {/* Tabs per navigazione */}\n      <Paper sx={{ mb: 3 }}>\n        <Tabs\n          value={activeTab}\n          onChange={handleTabChange}\n          indicatorColor=\"primary\"\n          textColor=\"primary\"\n          variant=\"fullWidth\"\n        >\n          <Tab\n            label={\n              <Stack direction=\"row\" alignItems=\"center\" spacing={1}>\n                <CableIcon />\n                <Box>\n                  <Typography variant=\"body2\" fontWeight=\"bold\">\n                    Cavi da Certificare\n                  </Typography>\n                  <Typography variant=\"caption\" color=\"text.secondary\">\n                    {filteredCavi.length} cavi totali\n                  </Typography>\n                </Box>\n                {statistics.caviNonCertificati > 0 && (\n                  <Badge badgeContent={statistics.caviNonCertificati} color=\"warning\">\n                    <WarningIcon />\n                  </Badge>\n                )}\n              </Stack>\n            }\n          />\n          <Tab\n            label={\n              <Stack direction=\"row\" alignItems=\"center\" spacing={1}>\n                <ScienceIcon />\n                <Box>\n                  <Typography variant=\"body2\" fontWeight=\"bold\">\n                    Certificazioni Completate\n                  </Typography>\n                  <Typography variant=\"caption\" color=\"text.secondary\">\n                    {filteredCertificazioni.length} certificazioni\n                  </Typography>\n                </Box>\n                {statistics.certificazioniOggi > 0 && (\n                  <Badge badgeContent={statistics.certificazioniOggi} color=\"success\">\n                    <CheckIcon />\n                  </Badge>\n                )}\n              </Stack>\n            }\n          />\n        </Tabs>\n      </Paper>\n\n      {/* Barra di ricerca e filtri avanzati */}\n      {renderSearchAndFilters()}\n\n      {/* Contenuto delle tabs */}\n      {!loading && activeTab === 0 && renderCaviTable()}\n      {!loading && activeTab === 1 && renderCertificazioniTable()}\n\n      {/* Dialogs */}\n      {renderCertificazioneDialog()}\n      {renderViewDialog()}\n\n      {/* Snackbar per notifiche */}\n      <Snackbar\n        open={snackbar.open}\n        autoHideDuration={6000}\n        onClose={closeSnackbar}\n        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}\n      >\n        <Alert onClose={closeSnackbar} severity={snackbar.severity} sx={{ width: '100%' }}>\n          {snackbar.message}\n        </Alert>\n      </Snackbar>\n\n      {/* Speed Dial per azioni rapide */}\n      <SpeedDial\n        ariaLabel=\"Azioni rapide\"\n        sx={{ position: 'fixed', bottom: 16, right: 16 }}\n        icon={<SpeedDialIcon />}\n      >\n        <SpeedDialAction\n          icon={<AddIcon />}\n          tooltipTitle=\"Nuova Certificazione\"\n          onClick={openCreateDialog}\n        />\n        <SpeedDialAction\n          icon={<ExportIcon />}\n          tooltipTitle=\"Esporta Tutto\"\n          onClick={handleExportAll}\n        />\n        <SpeedDialAction\n          icon={<RefreshIcon />}\n          tooltipTitle=\"Aggiorna Dati\"\n          onClick={loadInitialData}\n        />\n        <SpeedDialAction\n          icon={<ReportIcon />}\n          tooltipTitle=\"Report Avanzato\"\n          onClick={() => showSnackbar('Funzionalità in sviluppo', 'info')}\n        />\n      </SpeedDial>\n    </Container>\n  );\n});\n\nexport default CertificazioneCaviImproved;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,UAAU,EAAEC,mBAAmB,QAAQ,OAAO;AACnF,SACEC,GAAG,EACHC,UAAU,EACVC,MAAM,EACNC,KAAK,EACLC,IAAI,EACJC,IAAI,EACJC,WAAW,EACXC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,SAAS,EACTC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,YAAY,EACZC,gBAAgB,EAChBC,KAAK,EACLC,KAAK,EACLC,SAAS,EACTC,SAAS,EACTC,cAAc,EACdC,SAAS,EACTC,QAAQ,EACRC,UAAU,EACVC,IAAI,EACJC,GAAG,EACHC,UAAU,EACVC,cAAc,EACdC,OAAO,EACPC,KAAK,EACLC,IAAI,EACJC,OAAO,EACPC,KAAK,EACLC,cAAc,EACdC,QAAQ,EACRC,IAAI,EACJC,QAAQ,EACRC,YAAY,EACZC,YAAY,EACZC,QAAQ,EACRC,MAAM,EACNC,OAAO,EACPC,SAAS,EACTC,GAAG,EACHC,SAAS,EACTC,eAAe,EACfC,aAAa,QACR,eAAe;AACtB,SACEC,GAAG,IAAIC,OAAO,EACdC,MAAM,IAAIC,UAAU,EACpBC,UAAU,IAAIC,UAAU,EACxBC,YAAY,IAAIC,OAAO,EACvBC,QAAQ,IAAIC,YAAY,EACxBC,UAAU,IAAIC,QAAQ,EACtBC,MAAM,IAAIC,UAAU,EACpBC,IAAI,IAAIC,QAAQ,EAChBC,IAAI,IAAIC,QAAQ,EAChBC,KAAK,IAAIC,SAAS,EAClBC,KAAK,IAAIC,SAAS,EAClBC,WAAW,IAAIC,SAAS,EACxBC,OAAO,IAAIC,WAAW,EACtBC,MAAM,IAAIC,UAAU,EACpBC,KAAK,IAAIC,SAAS,EAClBC,KAAK,IAAIC,SAAS,EAClBC,WAAW,IAAIC,UAAU,EACzBC,UAAU,IAAIC,UAAU,EACxBC,QAAQ,IAAIC,YAAY,EACxBC,OAAO,IAAIC,WAAW,EACtBC,UAAU,IAAIC,cAAc,EAC5BC,UAAU,IAAIC,cAAc,EAC5BC,IAAI,IAAIC,QAAQ,EAChBC,KAAK,IAAIC,SAAS,EAClBC,QAAQ,IAAIC,YAAY,EACxBC,MAAM,IAAIC,UAAU,EACpBC,KAAK,IAAIC,SAAS,EAClBC,OAAO,IAAIC,WAAW,EACtBC,KAAK,IAAIC,SAAS,QACb,qBAAqB;AAE5B,OAAOC,qBAAqB,MAAM,sCAAsC;AACxE,OAAOC,WAAW,MAAM,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAErD,MAAMC,0BAA0B,gBAAAC,EAAA,cAAGpH,UAAU,CAAAqH,EAAA,GAAAD,EAAA,CAAC,CAAC;EAAEE,UAAU;EAAEC,SAAS;EAAEC;AAAQ,CAAC,EAAEC,GAAG,KAAK;EAAAL,EAAA;EACzF;EACA,MAAM,CAACM,OAAO,EAAEC,UAAU,CAAC,GAAG7H,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC8H,SAAS,EAAEC,YAAY,CAAC,GAAG/H,QAAQ,CAAC,CAAC,CAAC;EAC7C,MAAM,CAACgI,cAAc,EAAEC,iBAAiB,CAAC,GAAGjI,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAACkI,IAAI,EAAEC,OAAO,CAAC,GAAGnI,QAAQ,CAAC,EAAE,CAAC;EACpC,MAAM,CAACoI,SAAS,EAAEC,YAAY,CAAC,GAAGrI,QAAQ,CAAC,EAAE,CAAC;;EAE9C;EACA,MAAM,CAACsI,UAAU,EAAEC,aAAa,CAAC,GAAGvI,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACwI,YAAY,EAAEC,eAAe,CAAC,GAAGzI,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAAC0I,sBAAsB,EAAEC,yBAAyB,CAAC,GAAG3I,QAAQ,CAAC,EAAE,CAAC;EACxE,MAAM,CAAC4I,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG7I,QAAQ,CAAC,KAAK,CAAC;EACrE,MAAM,CAAC8I,OAAO,EAAEC,UAAU,CAAC,GAAG/I,QAAQ,CAAC;IACrCgJ,KAAK,EAAE,EAAE;IACTC,SAAS,EAAE,EAAE;IACbC,SAAS,EAAE,EAAE;IACbC,UAAU,EAAE,EAAE;IACdC,QAAQ,EAAE,EAAE;IACZC,gBAAgB,EAAE,EAAE;IACpBC,aAAa,EAAE,EAAE;IACjBC,SAAS,EAAE,EAAE;IACbC,cAAc,EAAE;EAClB,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAG1J,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAAC2J,YAAY,EAAEC,eAAe,CAAC,GAAG5J,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAAC6J,MAAM,EAAEC,SAAS,CAAC,GAAG9J,QAAQ,CAAC,qBAAqB,CAAC;EAC3D,MAAM,CAAC+J,SAAS,EAAEC,YAAY,CAAC,GAAGhK,QAAQ,CAAC,MAAM,CAAC;;EAElD;EACA,MAAM,CAACiK,UAAU,EAAEC,aAAa,CAAC,GAAGlK,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACmK,UAAU,EAAEC,aAAa,CAAC,GAAGpK,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACqK,YAAY,EAAEC,eAAe,CAAC,GAAGtK,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACuK,aAAa,EAAEC,gBAAgB,CAAC,GAAGxK,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACyK,QAAQ,EAAEC,WAAW,CAAC,GAAG1K,QAAQ,CAAC,KAAK,CAAC;;EAE/C;EACA,MAAM,CAAC2K,QAAQ,EAAEC,WAAW,CAAC,GAAG5K,QAAQ,CAAC;IAAE6K,IAAI,EAAE,KAAK;IAAEC,OAAO,EAAE,EAAE;IAAEC,QAAQ,EAAE;EAAO,CAAC,CAAC;EACxF,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGjL,QAAQ,CAAC,CAAC,CAAC;EAC3C,MAAM,CAACkL,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGnL,QAAQ,CAAC,KAAK,CAAC;;EAErE;EACA,MAAM,CAACoL,QAAQ,EAAEC,WAAW,CAAC,GAAGrL,QAAQ,CAAC;IACvCsL,OAAO,EAAE,EAAE;IACXC,YAAY,EAAE,EAAE;IAChBC,YAAY,EAAE,EAAE;IAChBC,kBAAkB,EAAE,EAAE;IACtBC,iBAAiB,EAAE,IAAI;IACvBC,iBAAiB,EAAE,EAAE;IACrBC,iBAAiB,EAAE,IAAI;IACvBC,IAAI,EAAE,EAAE;IACRC,oBAAoB,EAAE,EAAE;IACxBC,OAAO,EAAE,EAAE;IACXC,cAAc,EAAE,EAAE;IAClBC,YAAY,EAAE,EAAE;IAChBC,gBAAgB,EAAE;EACpB,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGpM,QAAQ,CAAC;IAC3CqM,UAAU,EAAE,CAAC;IACbC,eAAe,EAAE,CAAC;IAClBC,kBAAkB,EAAE,CAAC;IACrBC,wBAAwB,EAAE,CAAC;IAC3BC,kBAAkB,EAAE,CAAC;IACrBC,uBAAuB,EAAE;EAC3B,CAAC,CAAC;;EAEF;EACAzM,SAAS,CAAC,MAAM;IACd0M,eAAe,CAAC,CAAC;EACnB,CAAC,EAAE,CAACnF,UAAU,CAAC,CAAC;;EAEhB;EACAvH,SAAS,CAAC,MAAM;IACd2M,UAAU,CAAC,CAAC;EACd,CAAC,EAAE,CAAC1E,IAAI,EAAEI,UAAU,EAAEQ,OAAO,EAAEe,MAAM,EAAEE,SAAS,CAAC,CAAC;;EAElD;EACA9J,SAAS,CAAC,MAAM;IACd4M,oBAAoB,CAAC,CAAC;EACxB,CAAC,EAAE,CAAC7E,cAAc,EAAEM,UAAU,EAAEQ,OAAO,EAAEe,MAAM,EAAEE,SAAS,CAAC,CAAC;;EAE5D;EACA9J,SAAS,CAAC,MAAM;IACd6M,mBAAmB,CAAC,CAAC;EACvB,CAAC,EAAE,CAAC5E,IAAI,EAAEF,cAAc,CAAC,CAAC;EAE1B,MAAM2E,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACF9E,UAAU,CAAC,IAAI,CAAC;MAChBoD,WAAW,CAAC,CAAC,CAAC;;MAEd;MACAA,WAAW,CAAC,EAAE,CAAC;MACf,MAAM8B,QAAQ,CAAC,CAAC;MAEhB9B,WAAW,CAAC,EAAE,CAAC;MACf,MAAM+B,kBAAkB,CAAC,CAAC;MAE1B/B,WAAW,CAAC,EAAE,CAAC;MACf,MAAMgC,aAAa,CAAC,CAAC;MAErBhC,WAAW,CAAC,GAAG,CAAC;MAChB6B,mBAAmB,CAAC,CAAC;IAEvB,CAAC,CAAC,OAAOI,KAAK,EAAE;MACdC,YAAY,CAAC,0CAA0C,EAAE,OAAO,CAAC;MACjEzF,OAAO,CAAC,0CAA0C,CAAC;IACrD,CAAC,SAAS;MACRG,UAAU,CAAC,KAAK,CAAC;MACjBoD,WAAW,CAAC,CAAC,CAAC;IAChB;EACF,CAAC;EAED,MAAM+B,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrC,IAAI;MACF,MAAMI,IAAI,GAAG,MAAMrG,qBAAqB,CAACsG,iBAAiB,CAAC7F,UAAU,CAAC;MACtES,iBAAiB,CAACmF,IAAI,CAAC;MACvB,OAAOA,IAAI;IACb,CAAC,CAAC,OAAOF,KAAK,EAAE;MACdI,OAAO,CAACJ,KAAK,CAAC,8CAA8C,EAAEA,KAAK,CAAC;MACpE,MAAMA,KAAK;IACb;EACF,CAAC;EAED,MAAMH,QAAQ,GAAG,MAAAA,CAAA,KAAY;IAC3B,IAAI;MACF,MAAMK,IAAI,GAAG,MAAMpG,WAAW,CAACuG,OAAO,CAAC/F,UAAU,CAAC;MAClDW,OAAO,CAACiF,IAAI,CAAC;MACb,OAAOA,IAAI;IACb,CAAC,CAAC,OAAOF,KAAK,EAAE;MACdI,OAAO,CAACJ,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;MACxD,MAAMA,KAAK;IACb;EACF,CAAC;EAED,MAAMD,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACF,MAAMG,IAAI,GAAG,MAAMrG,qBAAqB,CAACyG,YAAY,CAAChG,UAAU,CAAC;MACjEa,YAAY,CAAC+E,IAAI,CAAC;MAClB,OAAOA,IAAI;IACb,CAAC,CAAC,OAAOF,KAAK,EAAE;MACdI,OAAO,CAACJ,KAAK,CAAC,yCAAyC,EAAEA,KAAK,CAAC;MAC/D,MAAMA,KAAK;IACb;EACF,CAAC;;EAED;EACA,MAAMJ,mBAAmB,GAAGA,CAAA,KAAM;IAChC,MAAMT,UAAU,GAAGnE,IAAI,CAACuF,MAAM;IAC9B,MAAMnB,eAAe,GAAGtE,cAAc,CAACyF,MAAM;IAC7C,MAAMlB,kBAAkB,GAAGF,UAAU,GAAGC,eAAe;IACvD,MAAME,wBAAwB,GAAGH,UAAU,GAAG,CAAC,GAAGqB,IAAI,CAACC,KAAK,CAAErB,eAAe,GAAGD,UAAU,GAAI,GAAG,CAAC,GAAG,CAAC;;IAEtG;IACA,MAAMuB,IAAI,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACC,YAAY,CAAC,CAAC;IACtC,MAAMrB,kBAAkB,GAAGzE,cAAc,CAAC+F,MAAM,CAACC,IAAI,IACnD,IAAIH,IAAI,CAACG,IAAI,CAACC,mBAAmB,CAAC,CAACH,YAAY,CAAC,CAAC,KAAKF,IACxD,CAAC,CAACH,MAAM;;IAER;IACA,MAAMS,cAAc,GAAG,IAAIL,IAAI,CAAC,CAAC;IACjCK,cAAc,CAACC,OAAO,CAACD,cAAc,CAACE,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC;IACpD,MAAM1B,uBAAuB,GAAG1E,cAAc,CAAC+F,MAAM,CAACC,IAAI,IACxD,IAAIH,IAAI,CAACG,IAAI,CAACC,mBAAmB,CAAC,IAAIC,cACxC,CAAC,CAACT,MAAM;;IAER;IACA,MAAMY,iBAAiB,GAAGnG,IAAI,CAAC6F,MAAM,CAACO,IAAI,IAAIC,oBAAoB,CAACD,IAAI,CAAC,CAAC,CAACb,MAAM;IAChF,MAAMe,oBAAoB,GAAGnC,UAAU,GAAGgC,iBAAiB;;IAE3D;IACA,MAAMI,aAAa,GAAGvG,IAAI,CAAC6F,MAAM,CAACO,IAAI,IAAII,eAAe,CAACJ,IAAI,CAAC,CAAC,CAACb,MAAM;IAEvErB,aAAa,CAAC;MACZC,UAAU;MACVC,eAAe;MACfC,kBAAkB;MAClB8B,iBAAiB;MACjBG,oBAAoB;MACpBC,aAAa;MACbjC,wBAAwB;MACxBC,kBAAkB;MAClBC;IACF,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMS,YAAY,GAAGA,CAACrC,OAAO,EAAEC,QAAQ,GAAG,MAAM,KAAK;IACnDH,WAAW,CAAC;MAAEC,IAAI,EAAE,IAAI;MAAEC,OAAO;MAAEC;IAAS,CAAC,CAAC;EAChD,CAAC;EAED,MAAM4D,aAAa,GAAGA,CAAA,KAAM;IAC1B/D,WAAW,CAAC;MAAE,GAAGD,QAAQ;MAAEE,IAAI,EAAE;IAAM,CAAC,CAAC;EAC3C,CAAC;EAED,MAAM+B,UAAU,GAAGA,CAAA,KAAM;IACvB,IAAIgC,QAAQ,GAAG1G,IAAI;;IAEnB;IACA,IAAII,UAAU,EAAE;MACd,MAAMuG,WAAW,GAAGvG,UAAU,CAACwG,WAAW,CAAC,CAAC;MAC5CF,QAAQ,GAAGA,QAAQ,CAACb,MAAM,CAACO,IAAI;QAAA,IAAAS,eAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,aAAA,EAAAC,aAAA;QAAA,OAC7Bb,IAAI,CAAChD,OAAO,CAACwD,WAAW,CAAC,CAAC,CAACM,QAAQ,CAACP,WAAW,CAAC,MAAAE,eAAA,GAChDT,IAAI,CAACrF,SAAS,cAAA8F,eAAA,uBAAdA,eAAA,CAAgBD,WAAW,CAAC,CAAC,CAACM,QAAQ,CAACP,WAAW,CAAC,OAAAG,qBAAA,GACnDV,IAAI,CAACe,mBAAmB,cAAAL,qBAAA,uBAAxBA,qBAAA,CAA0BF,WAAW,CAAC,CAAC,CAACM,QAAQ,CAACP,WAAW,CAAC,OAAAI,qBAAA,GAC7DX,IAAI,CAACgB,iBAAiB,cAAAL,qBAAA,uBAAtBA,qBAAA,CAAwBH,WAAW,CAAC,CAAC,CAACM,QAAQ,CAACP,WAAW,CAAC,OAAAK,aAAA,GAC3DZ,IAAI,CAACiB,OAAO,cAAAL,aAAA,uBAAZA,aAAA,CAAcJ,WAAW,CAAC,CAAC,CAACM,QAAQ,CAACP,WAAW,CAAC,OAAAM,aAAA,GACjDb,IAAI,CAACkB,OAAO,cAAAL,aAAA,uBAAZA,aAAA,CAAcL,WAAW,CAAC,CAAC,CAACM,QAAQ,CAACP,WAAW,CAAC;MAAA,CACnD,CAAC;IACH;;IAEA;IACA,IAAI/F,OAAO,CAACE,KAAK,EAAE;MACjB4F,QAAQ,GAAGA,QAAQ,CAACb,MAAM,CAACO,IAAI,IAAIA,IAAI,CAACmB,mBAAmB,KAAK3G,OAAO,CAACE,KAAK,CAAC;IAChF;IACA,IAAIF,OAAO,CAACG,SAAS,EAAE;MACrB2F,QAAQ,GAAGA,QAAQ,CAACb,MAAM,CAACO,IAAI,IAAIA,IAAI,CAACrF,SAAS,KAAKH,OAAO,CAACG,SAAS,CAAC;IAC1E;;IAEA;IACA,IAAIH,OAAO,CAACU,cAAc,EAAE;MAC1B,IAAIV,OAAO,CAACU,cAAc,KAAK,aAAa,EAAE;QAC5CoF,QAAQ,GAAGA,QAAQ,CAACb,MAAM,CAACO,IAAI,IAC7BtG,cAAc,CAAC0H,IAAI,CAAC1B,IAAI,IAAIA,IAAI,CAAC1C,OAAO,KAAKgD,IAAI,CAAChD,OAAO,CAC3D,CAAC;MACH,CAAC,MAAM,IAAIxC,OAAO,CAACU,cAAc,KAAK,iBAAiB,EAAE;QACvDoF,QAAQ,GAAGA,QAAQ,CAACb,MAAM,CAACO,IAAI,IAC7B,CAACtG,cAAc,CAAC0H,IAAI,CAAC1B,IAAI,IAAIA,IAAI,CAAC1C,OAAO,KAAKgD,IAAI,CAAChD,OAAO,CAC5D,CAAC;MACH;IACF;;IAEA;IACAsD,QAAQ,CAACe,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;MACtB,IAAIC,MAAM,GAAGF,CAAC,CAAC/F,MAAM,CAAC;MACtB,IAAIkG,MAAM,GAAGF,CAAC,CAAChG,MAAM,CAAC;MAEtB,IAAI,OAAOiG,MAAM,KAAK,QAAQ,EAAE;QAC9BA,MAAM,GAAGA,MAAM,CAAChB,WAAW,CAAC,CAAC;QAC7BiB,MAAM,GAAGA,MAAM,CAACjB,WAAW,CAAC,CAAC;MAC/B;MAEA,IAAI/E,SAAS,KAAK,KAAK,EAAE;QACvB,OAAO+F,MAAM,GAAGC,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC;MACjC,CAAC,MAAM;QACL,OAAOD,MAAM,GAAGC,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC;MACjC;IACF,CAAC,CAAC;IAEFtH,eAAe,CAACmG,QAAQ,CAAC;EAC3B,CAAC;EAED,MAAM/B,oBAAoB,GAAGA,CAAA,KAAM;IACjC,IAAI+B,QAAQ,GAAG5G,cAAc;;IAE7B;IACA,IAAIM,UAAU,EAAE;MACd,MAAMuG,WAAW,GAAGvG,UAAU,CAACwG,WAAW,CAAC,CAAC;MAC5CF,QAAQ,GAAGA,QAAQ,CAACb,MAAM,CAACC,IAAI;QAAA,IAAAgC,eAAA,EAAAC,qBAAA,EAAAC,UAAA;QAAA,OAC7BlC,IAAI,CAAC1C,OAAO,CAACwD,WAAW,CAAC,CAAC,CAACM,QAAQ,CAACP,WAAW,CAAC,MAAAmB,eAAA,GAChDhC,IAAI,CAAC9E,SAAS,cAAA8G,eAAA,uBAAdA,eAAA,CAAgBlB,WAAW,CAAC,CAAC,CAACM,QAAQ,CAACP,WAAW,CAAC,OAAAoB,qBAAA,GACnDjC,IAAI,CAACmC,kBAAkB,cAAAF,qBAAA,uBAAvBA,qBAAA,CAAyBnB,WAAW,CAAC,CAAC,CAACM,QAAQ,CAACP,WAAW,CAAC,OAAAqB,UAAA,GAC5DlC,IAAI,CAACnC,IAAI,cAAAqE,UAAA,uBAATA,UAAA,CAAWpB,WAAW,CAAC,CAAC,CAACM,QAAQ,CAACP,WAAW,CAAC;MAAA,CAChD,CAAC;IACH;;IAEA;IACA,IAAI/F,OAAO,CAACI,SAAS,EAAE;MACrB0F,QAAQ,GAAGA,QAAQ,CAACb,MAAM,CAACC,IAAI,IAAIA,IAAI,CAAC9E,SAAS,KAAKJ,OAAO,CAACI,SAAS,CAAC;IAC1E;IACA,IAAIJ,OAAO,CAACS,SAAS,EAAE;MACrBqF,QAAQ,GAAGA,QAAQ,CAACb,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACzE,SAAS,KAAKT,OAAO,CAACS,SAAS,CAAC;IAC1E;IACA,IAAIT,OAAO,CAACQ,aAAa,EAAE;MACzBsF,QAAQ,GAAGA,QAAQ,CAACb,MAAM,CAACC,IAAI,IAAIA,IAAI,CAAC9B,gBAAgB,KAAKpD,OAAO,CAACQ,aAAa,CAAC;IACrF;IACA,IAAIR,OAAO,CAACK,UAAU,EAAE;MACtByF,QAAQ,GAAGA,QAAQ,CAACb,MAAM,CAACC,IAAI,IAC7B,IAAIH,IAAI,CAACG,IAAI,CAACC,mBAAmB,CAAC,IAAI,IAAIJ,IAAI,CAAC/E,OAAO,CAACK,UAAU,CACnE,CAAC;IACH;IACA,IAAIL,OAAO,CAACM,QAAQ,EAAE;MACpBwF,QAAQ,GAAGA,QAAQ,CAACb,MAAM,CAACC,IAAI,IAC7B,IAAIH,IAAI,CAACG,IAAI,CAACC,mBAAmB,CAAC,IAAI,IAAIJ,IAAI,CAAC/E,OAAO,CAACM,QAAQ,CACjE,CAAC;IACH;IACA,IAAIN,OAAO,CAACO,gBAAgB,EAAE;MAC5B,MAAM+G,MAAM,GAAGC,UAAU,CAACvH,OAAO,CAACO,gBAAgB,CAAC;MACnDuF,QAAQ,GAAGA,QAAQ,CAACb,MAAM,CAACC,IAAI,IAC7BqC,UAAU,CAACrC,IAAI,CAACrC,iBAAiB,CAAC,IAAIyE,MACxC,CAAC;IACH;;IAEA;IACAxB,QAAQ,CAACe,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;MACtB,IAAIC,MAAM,GAAGF,CAAC,CAAC/F,MAAM,CAAC;MACtB,IAAIkG,MAAM,GAAGF,CAAC,CAAChG,MAAM,CAAC;MAEtB,IAAIA,MAAM,KAAK,qBAAqB,EAAE;QACpCiG,MAAM,GAAG,IAAIjC,IAAI,CAACiC,MAAM,CAAC;QACzBC,MAAM,GAAG,IAAIlC,IAAI,CAACkC,MAAM,CAAC;MAC3B,CAAC,MAAM,IAAI,OAAOD,MAAM,KAAK,QAAQ,EAAE;QACrCA,MAAM,GAAGA,MAAM,CAAChB,WAAW,CAAC,CAAC;QAC7BiB,MAAM,GAAGA,MAAM,CAACjB,WAAW,CAAC,CAAC;MAC/B;MAEA,IAAI/E,SAAS,KAAK,KAAK,EAAE;QACvB,OAAO+F,MAAM,GAAGC,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC;MACjC,CAAC,MAAM;QACL,OAAOD,MAAM,GAAGC,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC;MACjC;IACF,CAAC,CAAC;IAEFpH,yBAAyB,CAACiG,QAAQ,CAAC;EACrC,CAAC;;EAED;EACA,MAAM0B,cAAc,GAAGA,CAAA,KAAM;IAC3B,IAAIxI,SAAS,KAAK,CAAC,EAAE;MACnBqF,YAAY,CAAC,gEAAgE,EAAE,SAAS,CAAC;MACzF;IACF;IACAzC,WAAW,CAAC,CAACD,QAAQ,CAAC;IACtBD,gBAAgB,CAAC,EAAE,CAAC;IACpB2C,YAAY,CACV,CAAC1C,QAAQ,GACL,4EAA4E,GAC5E,gCAAgC,EACpC,MACF,CAAC;EACH,CAAC;EAED,MAAM8F,mBAAmB,GAAIC,MAAM,IAAK;IACtChG,gBAAgB,CAACiG,IAAI,IAAI;MACvB,MAAMC,YAAY,GAAGD,IAAI,CAACrB,QAAQ,CAACoB,MAAM,CAAC,GACtCC,IAAI,CAAC1C,MAAM,CAAC4C,EAAE,IAAIA,EAAE,KAAKH,MAAM,CAAC,GAChC,CAAC,GAAGC,IAAI,EAAED,MAAM,CAAC;MAErBrD,YAAY,CACV,GAAGuD,YAAY,CAACjD,MAAM,6BAA6B,EACnD,MACF,CAAC;MACD,OAAOiD,YAAY;IACrB,CAAC,CAAC;EACJ,CAAC;EAED,MAAME,cAAc,GAAGA,CAAA,KAAM;IAC3B,IAAI9I,SAAS,KAAK,CAAC,EAAE;IAErB,MAAM+I,MAAM,GAAGnI,sBAAsB,CAACoI,GAAG,CAAC9C,IAAI,IAAIA,IAAI,CAAC+C,iBAAiB,CAAC;IACzEvG,gBAAgB,CAACqG,MAAM,CAAC;IACxB1D,YAAY,CAAC,YAAY0D,MAAM,CAACpD,MAAM,6BAA6B,EAAE,SAAS,CAAC;EACjF,CAAC;EAED,MAAMuD,cAAc,GAAGA,CAAA,KAAM;IAC3BxG,gBAAgB,CAAC,EAAE,CAAC;IACpB2C,YAAY,CAAC,sBAAsB,EAAE,MAAM,CAAC;EAC9C,CAAC;;EAED;EACA,MAAM8D,iBAAiB,GAAIC,MAAM,IAAK;IACpC,OAAOlJ,cAAc,CAAC0H,IAAI,CAAC1B,IAAI,IAAIA,IAAI,CAAC1C,OAAO,KAAK4F,MAAM,CAAC;EAC7D,CAAC;;EAED;EACA,MAAM3C,oBAAoB,GAAID,IAAI,IAAK;IACrC;IACA,MAAM6C,YAAY,GAAG7C,IAAI,CAACmB,mBAAmB,KAAK,YAAY,IAC1CnB,IAAI,CAACmB,mBAAmB,KAAK,YAAY,IACzCnB,IAAI,CAACmB,mBAAmB,KAAK,QAAQ;;IAEzD;IACA;IACA,OAAO0B,YAAY;EACrB,CAAC;;EAED;EACA,MAAMzC,eAAe,GAAIJ,IAAI,IAAK;IAChC,MAAM8C,WAAW,GAAG9C,IAAI,CAAC+C,YAAY,KAAK,CAAC;IAC3C,MAAMC,eAAe,GAAGhD,IAAI,CAACiD,qBAAqB,IAAIjD,IAAI,CAACkD,mBAAmB;IAC9E,OAAOJ,WAAW,IAAIE,eAAe;EACvC,CAAC;;EAED;EACA,MAAMG,gCAAgC,GAAInD,IAAI,IAAK;IACjD,MAAM6C,YAAY,GAAG7C,IAAI,CAACmB,mBAAmB,KAAK,YAAY,IAC1CnB,IAAI,CAACmB,mBAAmB,KAAK,YAAY,IACzCnB,IAAI,CAACmB,mBAAmB,KAAK,QAAQ;IAEzD,IAAI,CAAC0B,YAAY,EAAE;MACjB,OAAO,yEAAyE;IAClF;IAEA,OAAO,+CAA+C;EACxD,CAAC;;EAED;EACA,MAAMO,eAAe,GAAGA,CAACC,KAAK,EAAEC,QAAQ,KAAK;IAC3C7J,YAAY,CAAC6J,QAAQ,CAAC;IACtBlI,cAAc,CAAC,CAAC,CAAC;IACjBnB,aAAa,CAAC,EAAE,CAAC;IACjBQ,UAAU,CAAC;MAAEC,KAAK,EAAE,EAAE;MAAEC,SAAS,EAAE,EAAE;MAAEC,SAAS,EAAE;IAAG,CAAC,CAAC;EACzD,CAAC;;EAED;EACA,MAAM2I,gBAAgB,GAAGA,CAACC,kBAAkB,GAAG,IAAI,KAAK;IACtD1H,aAAa,CAAC,QAAQ,CAAC;IACvBE,eAAe,CAAC,IAAI,CAAC;;IAErB;IACA,IAAIwH,kBAAkB,EAAE;MACtBzG,WAAW,CAAC;QACVC,OAAO,EAAEwG,kBAAkB,CAACxG,OAAO;QACnCC,YAAY,EAAE,EAAE;QAChBC,YAAY,EAAE,EAAE;QAChBC,kBAAkB,EAAEqG,kBAAkB,CAACC,eAAe,IAAID,kBAAkB,CAACE,aAAa,IAAI,EAAE;QAChGtG,iBAAiB,EAAE,IAAI;QACvBC,iBAAiB,EAAE,EAAE;QACrBC,iBAAiB,EAAE,IAAI;QACvBC,IAAI,EAAE,EAAE;QACRC,oBAAoB,EAAE,EAAE;QACxBC,OAAO,EAAE,EAAE;QACXC,cAAc,EAAE,EAAE;QAClBC,YAAY,EAAE,EAAE;QAChBC,gBAAgB,EAAE;MACpB,CAAC,CAAC;MACFiB,YAAY,CAAC,QAAQ2E,kBAAkB,CAACxG,OAAO,8BAA8B,EAAE,SAAS,CAAC;IAC3F,CAAC,MAAM;MACL;MACAD,WAAW,CAAC;QACVC,OAAO,EAAE,EAAE;QACXC,YAAY,EAAE,EAAE;QAChBC,YAAY,EAAE,EAAE;QAChBC,kBAAkB,EAAE,EAAE;QACtBC,iBAAiB,EAAE,IAAI;QACvBC,iBAAiB,EAAE,EAAE;QACrBC,iBAAiB,EAAE,IAAI;QACvBC,IAAI,EAAE,EAAE;QACRC,oBAAoB,EAAE,EAAE;QACxBC,OAAO,EAAE,EAAE;QACXC,cAAc,EAAE,EAAE;QAClBC,YAAY,EAAE,EAAE;QAChBC,gBAAgB,EAAE;MACpB,CAAC,CAAC;IACJ;IAEAhC,aAAa,CAAC,IAAI,CAAC;EACrB,CAAC;EAED,MAAM+H,WAAW,GAAGA,CAAA,KAAM;IACxB/H,aAAa,CAAC,KAAK,CAAC;IACpBI,eAAe,CAAC,IAAI,CAAC;IACrBF,aAAa,CAAC,EAAE,CAAC;EACnB,CAAC;;EAED;EACA,MAAM8H,gBAAgB,GAAGA,CAACC,KAAK,EAAEC,KAAK,KAAK;IACzC/G,WAAW,CAACoF,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAAC0B,KAAK,GAAGC;IACX,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMC,gBAAgB,GAAI/D,IAAI,IAAK;IACjCjD,WAAW,CAACoF,IAAI,KAAK;MACnB,GAAGA,IAAI;MACPnF,OAAO,EAAEgD,IAAI,CAAChD,OAAO;MACrBG,kBAAkB,EAAE6C,IAAI,CAACyD,eAAe,IAAIzD,IAAI,CAAC0D,aAAa,IAAI;IACpE,CAAC,CAAC,CAAC;EACL,CAAC;;EAED;EACA,MAAMM,0BAA0B,GAAG,MAAAA,CAAA,KAAY;IAC7C,IAAI;MACF,IAAI,CAAClH,QAAQ,CAACE,OAAO,IAAI,CAACF,QAAQ,CAACG,YAAY,IAAI,CAACH,QAAQ,CAACI,YAAY,IAAI,CAACJ,QAAQ,CAACO,iBAAiB,EAAE;QACxGwB,YAAY,CAAC,mCAAmC,EAAE,SAAS,CAAC;QAC5D;MACF;;MAEA;MACA,MAAMmB,IAAI,GAAGpG,IAAI,CAACqK,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAClH,OAAO,KAAKF,QAAQ,CAACE,OAAO,CAAC;MAC3D,IAAI,CAACgD,IAAI,EAAE;QACTnB,YAAY,CAAC,kBAAkB,EAAE,OAAO,CAAC;QACzC;MACF;MAEA,IAAI,CAACoB,oBAAoB,CAACD,IAAI,CAAC,EAAE;QAC/B,MAAMmE,SAAS,GAAGhB,gCAAgC,CAACnD,IAAI,CAAC;QACxDnB,YAAY,CAAC,oCAAoCsF,SAAS,EAAE,EAAE,OAAO,CAAC;QACtE;MACF;;MAEA;MACA,IAAIxB,iBAAiB,CAAC7F,QAAQ,CAACE,OAAO,CAAC,EAAE;QACvC6B,YAAY,CAAC,iCAAiC,EAAE,SAAS,CAAC;QAC1D;MACF;;MAEA;MACA,IAAI,CAACuB,eAAe,CAACJ,IAAI,CAAC,EAAE;QAC1B,MAAMoE,QAAQ,GAAGC,MAAM,CAACC,OAAO,CAC7B,uBAAuBtE,IAAI,CAAChD,OAAO,2CAA2C,GAC9E,uBAAuBgD,IAAI,CAAC+C,YAAY,KAAK,CAAC,GAAG,eAAe,GAC1C/C,IAAI,CAAC+C,YAAY,KAAK,CAAC,GAAG,yBAAyB,GACnD/C,IAAI,CAAC+C,YAAY,KAAK,CAAC,GAAG,uBAAuB,GACjD,mBAAmB,MAAM,GAC/C,kDAAkD,GAClD,sEACF,CAAC;QAED,IAAI,CAACqB,QAAQ,EAAE;UACb;QACF;MACF;MAEAvH,sBAAsB,CAAC,IAAI,CAAC;MAC5B,MAAMpE,qBAAqB,CAAC8L,oBAAoB,CAACrL,UAAU,EAAE4D,QAAQ,CAAC;MACtE+B,YAAY,CAAC,oCAAoC,EAAE,SAAS,CAAC;MAC7D8E,WAAW,CAAC,CAAC;MACb,MAAMjF,kBAAkB,CAAC,CAAC;MAC1BF,mBAAmB,CAAC,CAAC;IACvB,CAAC,CAAC,OAAOI,KAAK,EAAE;MACdC,YAAY,CAAC,+CAA+C,IAAID,KAAK,CAACpC,OAAO,IAAI,oBAAoB,CAAC,EAAE,OAAO,CAAC;IAClH,CAAC,SAAS;MACRK,sBAAsB,CAAC,KAAK,CAAC;IAC/B;EACF,CAAC;EAED,MAAM2H,iBAAiB,GAAG,MAAOtJ,cAAc,IAAK;IAClD,IAAI;MACF2B,sBAAsB,CAAC,IAAI,CAAC;MAC5BgC,YAAY,CAAC,6BAA6B,EAAE,MAAM,CAAC;MAEnD,MAAM4F,QAAQ,GAAG,MAAMhM,qBAAqB,CAACiM,WAAW,CAACxL,UAAU,EAAEgC,cAAc,CAACuH,iBAAiB,CAAC;MAEtG,IAAIgC,QAAQ,CAACE,QAAQ,EAAE;QACrB;QACA,MAAMC,SAAS,GAAGP,MAAM,CAAC9H,IAAI,CAACkI,QAAQ,CAACE,QAAQ,EAAE,QAAQ,CAAC;QAC1D,IAAIC,SAAS,EAAE;UACb/F,YAAY,CAAC,6CAA6C,EAAE,SAAS,CAAC;QACxE,CAAC,MAAM;UACL;UACA,MAAMgG,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;UACxCF,IAAI,CAACG,IAAI,GAAGP,QAAQ,CAACE,QAAQ;UAC7BE,IAAI,CAACI,QAAQ,GAAG,kBAAkB/J,cAAc,CAAC2G,kBAAkB,MAAM;UACzEiD,QAAQ,CAACI,IAAI,CAACC,WAAW,CAACN,IAAI,CAAC;UAC/BA,IAAI,CAACO,KAAK,CAAC,CAAC;UACZN,QAAQ,CAACI,IAAI,CAACG,WAAW,CAACR,IAAI,CAAC;UAC/BhG,YAAY,CAAC,uCAAuC,EAAE,SAAS,CAAC;QAClE;MACF,CAAC,MAAM,IAAI4F,QAAQ,CAACa,WAAW,EAAE;QAC/B;QACA,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACC,IAAI,CAAChB,QAAQ,CAACa,WAAW,CAAC,CAAC,EAAE;UAAEI,IAAI,EAAE;QAAkB,CAAC,CAAC;QAChF,MAAMC,GAAG,GAAGC,GAAG,CAACC,eAAe,CAACN,IAAI,CAAC;QACrC,MAAMV,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;QACxCF,IAAI,CAACG,IAAI,GAAGW,GAAG;QACfd,IAAI,CAACI,QAAQ,GAAG,kBAAkB/J,cAAc,CAAC2G,kBAAkB,MAAM;QACzEiD,QAAQ,CAACI,IAAI,CAACC,WAAW,CAACN,IAAI,CAAC;QAC/BA,IAAI,CAACO,KAAK,CAAC,CAAC;QACZN,QAAQ,CAACI,IAAI,CAACG,WAAW,CAACR,IAAI,CAAC;QAC/Be,GAAG,CAACE,eAAe,CAACH,GAAG,CAAC;QACxB9G,YAAY,CAAC,4BAA4B,EAAE,SAAS,CAAC;MACvD,CAAC,MAAM;QACLA,YAAY,CAAC,sCAAsC,EAAE,OAAO,CAAC;MAC/D;IACF,CAAC,CAAC,OAAOD,KAAK,EAAE;MACdI,OAAO,CAACJ,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/CC,YAAY,CAAC,oCAAoC,IAAID,KAAK,CAACpC,OAAO,IAAI,oBAAoB,CAAC,EAAE,OAAO,CAAC;IACvG,CAAC,SAAS;MACRK,sBAAsB,CAAC,KAAK,CAAC;IAC/B;EACF,CAAC;EAED,MAAMkJ,0BAA0B,GAAG,MAAO7K,cAAc,IAAK;IAC3D,IAAImJ,MAAM,CAACC,OAAO,CAAC,mDAAmDpJ,cAAc,CAAC2G,kBAAkB,GAAG,CAAC,EAAE;MAC3G,IAAI;QACFhF,sBAAsB,CAAC,IAAI,CAAC;QAC5B,MAAMpE,qBAAqB,CAACuN,oBAAoB,CAAC9M,UAAU,EAAEgC,cAAc,CAACuH,iBAAiB,CAAC;QAC9F5D,YAAY,CAAC,uCAAuC,EAAE,SAAS,CAAC;QAChE,MAAMH,kBAAkB,CAAC,CAAC;QAC1BF,mBAAmB,CAAC,CAAC;MACvB,CAAC,CAAC,OAAOI,KAAK,EAAE;QACdC,YAAY,CAAC,kDAAkD,IAAID,KAAK,CAACpC,OAAO,IAAI,oBAAoB,CAAC,EAAE,OAAO,CAAC;MACrH,CAAC,SAAS;QACRK,sBAAsB,CAAC,KAAK,CAAC;MAC/B;IACF;EACF,CAAC;;EAED;EACA,MAAMoJ,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAIhK,aAAa,CAACkD,MAAM,KAAK,CAAC,EAAE;MAC9BN,YAAY,CAAC,8BAA8B,EAAE,SAAS,CAAC;MACvD;IACF;IAEA,IAAIwF,MAAM,CAACC,OAAO,CAAC,iCAAiCrI,aAAa,CAACkD,MAAM,kBAAkB,CAAC,EAAE;MAC3F,IAAI;QACFtC,sBAAsB,CAAC,IAAI,CAAC;QAC5B,KAAK,MAAMwF,EAAE,IAAIpG,aAAa,EAAE;UAC9B,MAAMxD,qBAAqB,CAACuN,oBAAoB,CAAC9M,UAAU,EAAEmJ,EAAE,CAAC;QAClE;QACAxD,YAAY,CAAC,GAAG5C,aAAa,CAACkD,MAAM,wCAAwC,EAAE,SAAS,CAAC;QACxFjD,gBAAgB,CAAC,EAAE,CAAC;QACpB,MAAMwC,kBAAkB,CAAC,CAAC;QAC1BF,mBAAmB,CAAC,CAAC;MACvB,CAAC,CAAC,OAAOI,KAAK,EAAE;QACdC,YAAY,CAAC,gDAAgD,EAAE,OAAO,CAAC;MACzE,CAAC,SAAS;QACRhC,sBAAsB,CAAC,KAAK,CAAC;MAC/B;IACF;EACF,CAAC;EAED,MAAMqJ,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAIjK,aAAa,CAACkD,MAAM,KAAK,CAAC,EAAE;MAC9BN,YAAY,CAAC,8BAA8B,EAAE,SAAS,CAAC;MACvD;IACF;IAEA,IAAI;MACFhC,sBAAsB,CAAC,IAAI,CAAC;MAC5B;MACA,MAAMsJ,aAAa,GAAGzM,cAAc,CAAC+F,MAAM,CAACC,IAAI,IAC9CzD,aAAa,CAAC6E,QAAQ,CAACpB,IAAI,CAAC+C,iBAAiB,CAC/C,CAAC;;MAED;MACA,MAAM2D,UAAU,GAAGC,WAAW,CAACF,aAAa,CAAC;MAC7CG,WAAW,CAACF,UAAU,EAAE,kBAAkB,IAAI7G,IAAI,CAAC,CAAC,CAACgH,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;MAEvF3H,YAAY,CAAC,GAAG5C,aAAa,CAACkD,MAAM,2BAA2B,EAAE,SAAS,CAAC;IAC7E,CAAC,CAAC,OAAOP,KAAK,EAAE;MACdC,YAAY,CAAC,2BAA2B,EAAE,OAAO,CAAC;IACpD,CAAC,SAAS;MACRhC,sBAAsB,CAAC,KAAK,CAAC;IAC/B;EACF,CAAC;;EAED;EACA,MAAMwJ,WAAW,GAAIvH,IAAI,IAAK;IAC5B,MAAM2H,OAAO,GAAG,CAAC,SAAS,EAAE,oBAAoB,EAAE,MAAM,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,YAAY,EAAE,WAAW,CAAC;IAC3H,MAAMC,IAAI,GAAG5H,IAAI,CAAC0D,GAAG,CAAC9C,IAAI,IAAI,CAC5BA,IAAI,CAAC1C,OAAO,EACZ0C,IAAI,CAACmC,kBAAkB,EACvB,IAAItC,IAAI,CAACG,IAAI,CAACC,mBAAmB,CAAC,CAACgH,kBAAkB,CAAC,CAAC,EACvDjH,IAAI,CAAC9E,SAAS,EACd8E,IAAI,CAACzE,SAAS,EACdyE,IAAI,CAACvC,kBAAkB,EACvBuC,IAAI,CAACrC,iBAAiB,EACtBqC,IAAI,CAAC9B,gBAAgB,CACtB,CAAC;IAEF,OAAO,CAAC6I,OAAO,EAAE,GAAGC,IAAI,CAAC,CAAClE,GAAG,CAACoE,GAAG,IAAIA,GAAG,CAACC,IAAI,CAAC,GAAG,CAAC,CAAC,CAACA,IAAI,CAAC,IAAI,CAAC;EAChE,CAAC;EAED,MAAMP,WAAW,GAAGA,CAACQ,OAAO,EAAEC,QAAQ,KAAK;IACzC,MAAMxB,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACsB,OAAO,CAAC,EAAE;MAAEpB,IAAI,EAAE;IAA0B,CAAC,CAAC;IACrE,MAAMb,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;IACxC,IAAIF,IAAI,CAACI,QAAQ,KAAK+B,SAAS,EAAE;MAC/B,MAAMrB,GAAG,GAAGC,GAAG,CAACC,eAAe,CAACN,IAAI,CAAC;MACrCV,IAAI,CAACoC,YAAY,CAAC,MAAM,EAAEtB,GAAG,CAAC;MAC9Bd,IAAI,CAACoC,YAAY,CAAC,UAAU,EAAEF,QAAQ,CAAC;MACvClC,IAAI,CAACqC,KAAK,CAACC,UAAU,GAAG,QAAQ;MAChCrC,QAAQ,CAACI,IAAI,CAACC,WAAW,CAACN,IAAI,CAAC;MAC/BA,IAAI,CAACO,KAAK,CAAC,CAAC;MACZN,QAAQ,CAACI,IAAI,CAACG,WAAW,CAACR,IAAI,CAAC;IACjC;EACF,CAAC;EAED,MAAMuC,eAAe,GAAGA,CAAA,KAAM;IAC5B,MAAMhB,UAAU,GAAGC,WAAW,CAACjM,sBAAsB,CAAC;IACtDkM,WAAW,CAACF,UAAU,EAAE,wBAAwB,IAAI7G,IAAI,CAAC,CAAC,CAACgH,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;IAC7F3H,YAAY,CAAC,yBAAyB,EAAE,SAAS,CAAC;EACpD,CAAC;;EAED;EACAhN,mBAAmB,CAACwH,GAAG,EAAE,OAAO;IAC9BgO,kBAAkB,EAAGC,MAAM,IAAK;MAC9B,IAAIA,MAAM,KAAK,oBAAoB,EAAE;QACnC/D,gBAAgB,CAAC,CAAC;MACpB,CAAC,MAAM,IAAI+D,MAAM,KAAK,0BAA0B,EAAE;QAChD7N,YAAY,CAAC,CAAC,CAAC;MACjB;IACF;EACF,CAAC,CAAC,CAAC;;EAEH;EACA,MAAM8N,mBAAmB,GAAIC,KAAK,IAAK;IACrC,MAAMC,UAAU,GAAG,CAACtM,WAAW,GAAG,CAAC,IAAIE,YAAY;IACnD,MAAMqM,QAAQ,GAAGD,UAAU,GAAGpM,YAAY;IAC1C,OAAOmM,KAAK,CAACG,KAAK,CAACF,UAAU,EAAEC,QAAQ,CAAC;EAC1C,CAAC;EAED,MAAME,aAAa,GAAIJ,KAAK,IAAKpI,IAAI,CAACyI,IAAI,CAACL,KAAK,CAACrI,MAAM,GAAG9D,YAAY,CAAC;;EAEvE;EACA,MAAMyM,eAAe,GAAGA,CAACC,KAAK,EAAElE,KAAK,KAAK;IACxC,OAAO,CAAC,GAAG,IAAImE,GAAG,CAACD,KAAK,CAACvF,GAAG,CAACyF,IAAI,IAAIA,IAAI,CAACpE,KAAK,CAAC,CAAC,CAACpE,MAAM,CAACyI,OAAO,CAAC,CAAC,CAAC;EACrE,CAAC;;EAED;EACA,MAAMC,eAAe,GAAGA,CAAA,kBACtBvP,OAAA,CAAC1G,IAAI;IAACkW,SAAS;IAACC,OAAO,EAAE,CAAE;IAACC,EAAE,EAAE;MAAEC,EAAE,EAAE;IAAE,CAAE;IAAAC,QAAA,gBACxC5P,OAAA,CAAC1G,IAAI;MAAC+V,IAAI;MAACQ,EAAE,EAAE,EAAG;MAACC,EAAE,EAAE,CAAE;MAAAF,QAAA,eACvB5P,OAAA,CAACzG,IAAI;QAACmW,EAAE,EAAE;UAAEK,UAAU,EAAE,mDAAmD;UAAEC,KAAK,EAAE;QAAQ,CAAE;QAAAJ,QAAA,eAC5F5P,OAAA,CAACxG,WAAW;UAACkW,EAAE,EAAE;YAAEO,SAAS,EAAE,QAAQ;YAAEC,EAAE,EAAE;UAAE,CAAE;UAAAN,QAAA,gBAC9C5P,OAAA,CAACR,SAAS;YAACkQ,EAAE,EAAE;cAAES,QAAQ,EAAE,EAAE;cAAER,EAAE,EAAE;YAAE;UAAE;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC1CvQ,OAAA,CAAC7G,UAAU;YAACqX,OAAO,EAAC,IAAI;YAACC,UAAU,EAAC,MAAM;YAAAb,QAAA,EACvC3K,UAAU,CAACE;UAAU;YAAAiL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACZ,CAAC,eACbvQ,OAAA,CAAC7G,UAAU;YAACqX,OAAO,EAAC,OAAO;YAAAZ,QAAA,EAAC;UAE5B;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAEPvQ,OAAA,CAAC1G,IAAI;MAAC+V,IAAI;MAACQ,EAAE,EAAE,EAAG;MAACC,EAAE,EAAE,CAAE;MAAAF,QAAA,eACvB5P,OAAA,CAACzG,IAAI;QAACmW,EAAE,EAAE;UAAEK,UAAU,EAAE,mDAAmD;UAAEC,KAAK,EAAE;QAAQ,CAAE;QAAAJ,QAAA,eAC5F5P,OAAA,CAACxG,WAAW;UAACkW,EAAE,EAAE;YAAEO,SAAS,EAAE,QAAQ;YAAEC,EAAE,EAAE;UAAE,CAAE;UAAAN,QAAA,gBAC9C5P,OAAA,CAACtC,SAAS;YAACgS,EAAE,EAAE;cAAES,QAAQ,EAAE,EAAE;cAAER,EAAE,EAAE;YAAE;UAAE;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC1CvQ,OAAA,CAAC7G,UAAU;YAACqX,OAAO,EAAC,IAAI;YAACC,UAAU,EAAC,MAAM;YAAAb,QAAA,EACvC3K,UAAU,CAACG;UAAe;YAAAgL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjB,CAAC,eACbvQ,OAAA,CAAC7G,UAAU;YAACqX,OAAO,EAAC,OAAO;YAAAZ,QAAA,EAAC;UAE5B;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAEPvQ,OAAA,CAAC1G,IAAI;MAAC+V,IAAI;MAACQ,EAAE,EAAE,EAAG;MAACC,EAAE,EAAE,CAAE;MAAAF,QAAA,eACvB5P,OAAA,CAACzG,IAAI;QAACmW,EAAE,EAAE;UAAEK,UAAU,EAAE,mDAAmD;UAAEC,KAAK,EAAE;QAAQ,CAAE;QAAAJ,QAAA,eAC5F5P,OAAA,CAACxG,WAAW;UAACkW,EAAE,EAAE;YAAEO,SAAS,EAAE,QAAQ;YAAEC,EAAE,EAAE;UAAE,CAAE;UAAAN,QAAA,gBAC9C5P,OAAA,CAACxC,SAAS;YAACkS,EAAE,EAAE;cAAES,QAAQ,EAAE,EAAE;cAAER,EAAE,EAAE;YAAE;UAAE;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC1CvQ,OAAA,CAAC7G,UAAU;YAACqX,OAAO,EAAC,IAAI;YAACC,UAAU,EAAC,MAAM;YAAAb,QAAA,EACvC3K,UAAU,CAACkC;UAAiB;YAAAiJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC,eACbvQ,OAAA,CAAC7G,UAAU;YAACqX,OAAO,EAAC,OAAO;YAAAZ,QAAA,EAAC;UAE5B;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAEPvQ,OAAA,CAAC1G,IAAI;MAAC+V,IAAI;MAACQ,EAAE,EAAE,EAAG;MAACC,EAAE,EAAE,CAAE;MAAAF,QAAA,eACvB5P,OAAA,CAACzG,IAAI;QAACmW,EAAE,EAAE;UAAEK,UAAU,EAAE,mDAAmD;UAAEC,KAAK,EAAE;QAAO,CAAE;QAAAJ,QAAA,eAC3F5P,OAAA,CAACxG,WAAW;UAACkW,EAAE,EAAE;YAAEO,SAAS,EAAE,QAAQ;YAAEC,EAAE,EAAE;UAAE,CAAE;UAAAN,QAAA,gBAC9C5P,OAAA,CAACtC,SAAS;YAACgS,EAAE,EAAE;cAAES,QAAQ,EAAE,EAAE;cAAER,EAAE,EAAE;YAAE;UAAE;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC1CvQ,OAAA,CAAC7G,UAAU;YAACqX,OAAO,EAAC,IAAI;YAACC,UAAU,EAAC,MAAM;YAAAb,QAAA,EACvC3K,UAAU,CAACsC;UAAa;YAAA6I,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CAAC,eACbvQ,OAAA,CAAC7G,UAAU;YAACqX,OAAO,EAAC,OAAO;YAAAZ,QAAA,EAAC;UAE5B;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAEPvQ,OAAA,CAAC1G,IAAI;MAAC+V,IAAI;MAACQ,EAAE,EAAE,EAAG;MAACC,EAAE,EAAE,CAAE;MAAAF,QAAA,eACvB5P,OAAA,CAACzG,IAAI;QAACmW,EAAE,EAAE;UAAEK,UAAU,EAAE,mDAAmD;UAAEC,KAAK,EAAE;QAAQ,CAAE;QAAAJ,QAAA,eAC5F5P,OAAA,CAACxG,WAAW;UAACkW,EAAE,EAAE;YAAEO,SAAS,EAAE,QAAQ;YAAEC,EAAE,EAAE;UAAE,CAAE;UAAAN,QAAA,gBAC9C5P,OAAA,CAAC1B,UAAU;YAACoR,EAAE,EAAE;cAAES,QAAQ,EAAE,EAAE;cAAER,EAAE,EAAE;YAAE;UAAE;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC3CvQ,OAAA,CAAC7G,UAAU;YAACqX,OAAO,EAAC,IAAI;YAACC,UAAU,EAAC,MAAM;YAAAb,QAAA,GACvC3K,UAAU,CAACK,wBAAwB,EAAC,GACvC;UAAA;YAAA8K,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbvQ,OAAA,CAAC7G,UAAU;YAACqX,OAAO,EAAC,OAAO;YAAAZ,QAAA,EAAC;UAE5B;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAEPvQ,OAAA,CAAC1G,IAAI;MAAC+V,IAAI;MAACQ,EAAE,EAAE,EAAG;MAACC,EAAE,EAAE,CAAE;MAAAF,QAAA,eACvB5P,OAAA,CAACzG,IAAI;QAACmW,EAAE,EAAE;UAAEK,UAAU,EAAE,mDAAmD;UAAEC,KAAK,EAAE;QAAQ,CAAE;QAAAJ,QAAA,eAC5F5P,OAAA,CAACxG,WAAW;UAACkW,EAAE,EAAE;YAAEO,SAAS,EAAE,QAAQ;YAAEC,EAAE,EAAE;UAAE,CAAE;UAAAN,QAAA,gBAC9C5P,OAAA,CAACZ,YAAY;YAACsQ,EAAE,EAAE;cAAES,QAAQ,EAAE,EAAE;cAAER,EAAE,EAAE;YAAE;UAAE;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC7CvQ,OAAA,CAAC7G,UAAU;YAACqX,OAAO,EAAC,IAAI;YAACC,UAAU,EAAC,MAAM;YAAAb,QAAA,EACvC3K,UAAU,CAACM;UAAkB;YAAA6K,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpB,CAAC,eACbvQ,OAAA,CAAC7G,UAAU;YAACqX,OAAO,EAAC,OAAO;YAAAZ,QAAA,EAAC;UAE5B;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAEPvQ,OAAA,CAAC1G,IAAI;MAAC+V,IAAI;MAACQ,EAAE,EAAE,EAAG;MAACC,EAAE,EAAE,CAAE;MAAAF,QAAA,eACvB5P,OAAA,CAACzG,IAAI;QAACmW,EAAE,EAAE;UAAEK,UAAU,EAAE,mDAAmD;UAAEC,KAAK,EAAE;QAAO,CAAE;QAAAJ,QAAA,eAC3F5P,OAAA,CAACxG,WAAW;UAACkW,EAAE,EAAE;YAAEO,SAAS,EAAE,QAAQ;YAAEC,EAAE,EAAE;UAAE,CAAE;UAAAN,QAAA,gBAC9C5P,OAAA,CAACN,WAAW;YAACgQ,EAAE,EAAE;cAAES,QAAQ,EAAE,EAAE;cAAER,EAAE,EAAE;YAAE;UAAE;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC5CvQ,OAAA,CAAC7G,UAAU;YAACqX,OAAO,EAAC,IAAI;YAACC,UAAU,EAAC,MAAM;YAAAb,QAAA,EACvC3K,UAAU,CAACO;UAAuB;YAAA4K,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzB,CAAC,eACbvQ,OAAA,CAAC7G,UAAU;YAACqX,OAAO,EAAC,OAAO;YAAAZ,QAAA,EAAC;UAE5B;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACP;;EAED;EACA,MAAMG,sBAAsB,GAAGA,CAAA,kBAC7B1Q,OAAA,CAAC3G,KAAK;IAACqW,EAAE,EAAE;MAAEiB,CAAC,EAAE,CAAC;MAAEhB,EAAE,EAAE;IAAE,CAAE;IAAAC,QAAA,gBACzB5P,OAAA,CAAC1G,IAAI;MAACkW,SAAS;MAACC,OAAO,EAAE,CAAE;MAACmB,UAAU,EAAC,QAAQ;MAAAhB,QAAA,gBAC7C5P,OAAA,CAAC1G,IAAI;QAAC+V,IAAI;QAACQ,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAF,QAAA,eACvB5P,OAAA,CAACnG,SAAS;UACRgX,SAAS;UACTC,WAAW,EAAC,0CAA0C;UACtD5F,KAAK,EAAE9J,UAAW;UAClB2P,QAAQ,EAAGC,CAAC,IAAK3P,aAAa,CAAC2P,CAAC,CAACC,MAAM,CAAC/F,KAAK,CAAE;UAC/CgG,UAAU,EAAE;YACVC,cAAc,eACZnR,OAAA,CAACjF,cAAc;cAACqW,QAAQ,EAAC,OAAO;cAAAxB,QAAA,eAC9B5P,OAAA,CAAC1D,UAAU;gBAAA8T,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CACjB;YACDc,YAAY,EAAEjQ,UAAU,iBACtBpB,OAAA,CAACjF,cAAc;cAACqW,QAAQ,EAAC,KAAK;cAAAxB,QAAA,eAC5B5P,OAAA,CAACrF,UAAU;gBAAC2W,OAAO,EAAEA,CAAA,KAAMjQ,aAAa,CAAC,EAAE,CAAE;gBAACkQ,IAAI,EAAC,OAAO;gBAAA3B,QAAA,eACxD5P,OAAA,CAAC1C,SAAS;kBAAA8S,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UAEpB;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAEPvQ,OAAA,CAAC1G,IAAI;QAAC+V,IAAI;QAACQ,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAF,QAAA,eACvB5P,OAAA,CAAC5G,MAAM;UACLyX,SAAS;UACTL,OAAO,EAAC,UAAU;UAClBgB,SAAS,eAAExR,OAAA,CAACxD,UAAU;YAAA4T,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC1Be,OAAO,EAAEA,CAAA,KAAM3P,sBAAsB,CAAC,CAACD,mBAAmB,CAAE;UAC5DsO,KAAK,EAAEyB,MAAM,CAACC,MAAM,CAAC9P,OAAO,CAAC,CAAC4G,IAAI,CAACmJ,CAAC,IAAIA,CAAC,CAAC,GAAG,SAAS,GAAG,SAAU;UAAA/B,QAAA,GACpE,SACQ,EAAC6B,MAAM,CAACC,MAAM,CAAC9P,OAAO,CAAC,CAACiF,MAAM,CAAC8K,CAAC,IAAIA,CAAC,CAAC,CAACpL,MAAM,GAAG,CAAC,IAAI,IAAIkL,MAAM,CAACC,MAAM,CAAC9P,OAAO,CAAC,CAACiF,MAAM,CAAC8K,CAAC,IAAIA,CAAC,CAAC,CAACpL,MAAM,GAAG;QAAA;UAAA6J,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAEPvQ,OAAA,CAAC1G,IAAI;QAAC+V,IAAI;QAACQ,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAF,QAAA,eACvB5P,OAAA,CAAC5G,MAAM;UACLyX,SAAS;UACTL,OAAO,EAAC,UAAU;UAClBgB,SAAS,EAAEjO,QAAQ,gBAAGvD,OAAA,CAAC1C,SAAS;YAAA8S,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAAGvQ,OAAA,CAACtC,SAAS;YAAA0S,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACpDe,OAAO,EAAElI,cAAe;UACxB4G,KAAK,EAAEzM,QAAQ,GAAG,WAAW,GAAG,SAAU;UAC1CqO,QAAQ,EAAEhR,SAAS,KAAK,CAAE;UAAAgP,QAAA,EAEzBrM,QAAQ,GAAG,gBAAgB,GAAG;QAAoB;UAAA6M,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7C;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAEPvQ,OAAA,CAAC1G,IAAI;QAAC+V,IAAI;QAACQ,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAF,QAAA,eACvB5P,OAAA,CAAC5G,MAAM;UACLyX,SAAS;UACTL,OAAO,EAAC,UAAU;UAClBgB,SAAS,eAAExR,OAAA,CAAClC,UAAU;YAAAsS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC1Be,OAAO,EAAE9C,eAAgB;UACzBoD,QAAQ,EAAEhR,SAAS,KAAK,CAAC,IAAIY,sBAAsB,CAAC+E,MAAM,KAAK,CAAE;UAAAqJ,QAAA,EAEhEhP,SAAS,KAAK,CAAC,GAAG,8BAA8B,GAAG;QAAwB;UAAAwP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAEPvQ,OAAA,CAAC1G,IAAI;QAAC+V,IAAI;QAACQ,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAF,QAAA,eACvB5P,OAAA,CAAC5G,MAAM;UACLyX,SAAS;UACTL,OAAO,EAAC,WAAW;UACnBgB,SAAS,eAAExR,OAAA,CAAC5D,OAAO;YAAAgU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACvBe,OAAO,EAAE3G,gBAAiB;UAAAiF,QAAA,EAC3B;QAED;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGPvQ,OAAA,CAAC1E,QAAQ;MAACuW,EAAE,EAAEnQ,mBAAoB;MAAAkO,QAAA,gBAChC5P,OAAA,CAAChF,OAAO;QAAC0U,EAAE,EAAE;UAAEoC,EAAE,EAAE;QAAE;MAAE;QAAA1B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC1BvQ,OAAA,CAAC7G,UAAU;QAACqX,OAAO,EAAC,OAAO;QAACR,KAAK,EAAC,gBAAgB;QAACN,EAAE,EAAE;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAC,QAAA,EAC9DhP,SAAS,KAAK,CAAC,GAAG,iBAAiB,GAAG;MAA2B;QAAAwP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxD,CAAC,eAEbvQ,OAAA,CAAC1G,IAAI;QAACkW,SAAS;QAACC,OAAO,EAAE,CAAE;QAAAG,QAAA,GAExBhP,SAAS,KAAK,CAAC,iBACdZ,OAAA,CAAAE,SAAA;UAAA0P,QAAA,gBACE5P,OAAA,CAAC1G,IAAI;YAAC+V,IAAI;YAACQ,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAF,QAAA,eACvB5P,OAAA,CAAClG,WAAW;cAAC+W,SAAS;cAACU,IAAI,EAAC,OAAO;cAAA3B,QAAA,gBACjC5P,OAAA,CAACjG,UAAU;gBAAA6V,QAAA,EAAC;cAAmB;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAC5CvQ,OAAA,CAAChG,MAAM;gBACLkR,KAAK,EAAEtJ,OAAO,CAACE,KAAM;gBACrBiP,QAAQ,EAAGC,CAAC,IAAKnP,UAAU,CAAC;kBAAC,GAAGD,OAAO;kBAAEE,KAAK,EAAEkP,CAAC,CAACC,MAAM,CAAC/F;gBAAK,CAAC,CAAE;gBAAA0E,QAAA,gBAEjE5P,OAAA,CAAC/F,QAAQ;kBAACiR,KAAK,EAAC,EAAE;kBAAA0E,QAAA,EAAC;gBAAK;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,eACnCvQ,OAAA,CAAC/F,QAAQ;kBAACiR,KAAK,EAAC,YAAY;kBAAA0E,QAAA,EAAC;gBAAU;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,eAClDvQ,OAAA,CAAC/F,QAAQ;kBAACiR,KAAK,EAAC,gBAAgB;kBAAA0E,QAAA,EAAC;gBAAc;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,eAC1DvQ,OAAA,CAAC/F,QAAQ;kBAACiR,KAAK,EAAC,UAAU;kBAAA0E,QAAA,EAAC;gBAAQ;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eAEPvQ,OAAA,CAAC1G,IAAI;YAAC+V,IAAI;YAACQ,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAF,QAAA,eACvB5P,OAAA,CAAClG,WAAW;cAAC+W,SAAS;cAACU,IAAI,EAAC,OAAO;cAAA3B,QAAA,gBACjC5P,OAAA,CAACjG,UAAU;gBAAA6V,QAAA,EAAC;cAAS;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAClCvQ,OAAA,CAAChG,MAAM;gBACLkR,KAAK,EAAEtJ,OAAO,CAACG,SAAU;gBACzBgP,QAAQ,EAAGC,CAAC,IAAKnP,UAAU,CAAC;kBAAC,GAAGD,OAAO;kBAAEG,SAAS,EAAEiP,CAAC,CAACC,MAAM,CAAC/F;gBAAK,CAAC,CAAE;gBAAA0E,QAAA,gBAErE5P,OAAA,CAAC/F,QAAQ;kBAACiR,KAAK,EAAC,EAAE;kBAAA0E,QAAA,EAAC;gBAAK;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,EAClC,CAAC,GAAG,IAAInB,GAAG,CAACpO,IAAI,CAAC4I,GAAG,CAAC0B,CAAC,IAAIA,CAAC,CAACvJ,SAAS,CAAC,CAAC,CAAC,CAAC8E,MAAM,CAACyI,OAAO,CAAC,CAAC1F,GAAG,CAACmI,GAAG,iBAC/D/R,OAAA,CAAC/F,QAAQ;kBAAWiR,KAAK,EAAE6G,GAAI;kBAAAnC,QAAA,EAAEmC;gBAAG,GAArBA,GAAG;kBAAA3B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAA6B,CAChD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eAEPvQ,OAAA,CAAC1G,IAAI;YAAC+V,IAAI;YAACQ,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAF,QAAA,eACvB5P,OAAA,CAAClG,WAAW;cAAC+W,SAAS;cAACU,IAAI,EAAC,OAAO;cAAA3B,QAAA,gBACjC5P,OAAA,CAACjG,UAAU;gBAAA6V,QAAA,EAAC;cAAoB;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAC7CvQ,OAAA,CAAChG,MAAM;gBACLkR,KAAK,EAAEtJ,OAAO,CAACU,cAAe;gBAC9ByO,QAAQ,EAAGC,CAAC,IAAKnP,UAAU,CAAC;kBAAC,GAAGD,OAAO;kBAAEU,cAAc,EAAE0O,CAAC,CAACC,MAAM,CAAC/F;gBAAK,CAAC,CAAE;gBAAA0E,QAAA,gBAE1E5P,OAAA,CAAC/F,QAAQ;kBAACiR,KAAK,EAAC,EAAE;kBAAA0E,QAAA,EAAC;gBAAK;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,eACnCvQ,OAAA,CAAC/F,QAAQ;kBAACiR,KAAK,EAAC,aAAa;kBAAA0E,QAAA,EAAC;gBAAW;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,eACpDvQ,OAAA,CAAC/F,QAAQ;kBAACiR,KAAK,EAAC,iBAAiB;kBAAA0E,QAAA,EAAC;gBAAe;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA,eACP,CACH,EAGA3P,SAAS,KAAK,CAAC,iBACdZ,OAAA,CAAAE,SAAA;UAAA0P,QAAA,gBACE5P,OAAA,CAAC1G,IAAI;YAAC+V,IAAI;YAACQ,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAF,QAAA,eACvB5P,OAAA,CAAClG,WAAW;cAAC+W,SAAS;cAACU,IAAI,EAAC,OAAO;cAAA3B,QAAA,gBACjC5P,OAAA,CAACjG,UAAU;gBAAA6V,QAAA,EAAC;cAAS;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAClCvQ,OAAA,CAAChG,MAAM;gBACLkR,KAAK,EAAEtJ,OAAO,CAACI,SAAU;gBACzB+O,QAAQ,EAAGC,CAAC,IAAKnP,UAAU,CAAC;kBAAC,GAAGD,OAAO;kBAAEI,SAAS,EAAEgP,CAAC,CAACC,MAAM,CAAC/F;gBAAK,CAAC,CAAE;gBAAA0E,QAAA,gBAErE5P,OAAA,CAAC/F,QAAQ;kBAACiR,KAAK,EAAC,EAAE;kBAAA0E,QAAA,EAAC;gBAAK;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,EAClC,CAAC,GAAG,IAAInB,GAAG,CAACtO,cAAc,CAAC8I,GAAG,CAAC0B,CAAC,IAAIA,CAAC,CAACtJ,SAAS,CAAC,CAAC,CAAC,CAAC6E,MAAM,CAACyI,OAAO,CAAC,CAAC1F,GAAG,CAACoI,EAAE,iBACxEhS,OAAA,CAAC/F,QAAQ;kBAAUiR,KAAK,EAAE8G,EAAG;kBAAApC,QAAA,EAAEoC;gBAAE,GAAlBA,EAAE;kBAAA5B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAA2B,CAC7C,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eAEPvQ,OAAA,CAAC1G,IAAI;YAAC+V,IAAI;YAACQ,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAF,QAAA,eACvB5P,OAAA,CAAClG,WAAW;cAAC+W,SAAS;cAACU,IAAI,EAAC,OAAO;cAAA3B,QAAA,gBACjC5P,OAAA,CAACjG,UAAU;gBAAA6V,QAAA,EAAC;cAAc;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACvCvQ,OAAA,CAAChG,MAAM;gBACLkR,KAAK,EAAEtJ,OAAO,CAACQ,aAAc;gBAC7B2O,QAAQ,EAAGC,CAAC,IAAKnP,UAAU,CAAC;kBAAC,GAAGD,OAAO;kBAAEQ,aAAa,EAAE4O,CAAC,CAACC,MAAM,CAAC/F;gBAAK,CAAC,CAAE;gBAAA0E,QAAA,gBAEzE5P,OAAA,CAAC/F,QAAQ;kBAACiR,KAAK,EAAC,EAAE;kBAAA0E,QAAA,EAAC;gBAAK;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,eACnCvQ,OAAA,CAAC/F,QAAQ;kBAACiR,KAAK,EAAC,UAAU;kBAAA0E,QAAA,EAAC;gBAAQ;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,eAC9CvQ,OAAA,CAAC/F,QAAQ;kBAACiR,KAAK,EAAC,cAAc;kBAAA0E,QAAA,EAAC;gBAAY;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,eACtDvQ,OAAA,CAAC/F,QAAQ;kBAACiR,KAAK,EAAC,eAAe;kBAAA0E,QAAA,EAAC;gBAAa;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eAEPvQ,OAAA,CAAC1G,IAAI;YAAC+V,IAAI;YAACQ,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAF,QAAA,eACvB5P,OAAA,CAACnG,SAAS;cACRgX,SAAS;cACTU,IAAI,EAAC,OAAO;cACZU,KAAK,EAAC,0BAAqB;cAC3BnF,IAAI,EAAC,QAAQ;cACb5B,KAAK,EAAEtJ,OAAO,CAACO,gBAAiB;cAChC4O,QAAQ,EAAGC,CAAC,IAAKnP,UAAU,CAAC;gBAAC,GAAGD,OAAO;gBAAEO,gBAAgB,EAAE6O,CAAC,CAACC,MAAM,CAAC/F;cAAK,CAAC,CAAE;cAC5E4F,WAAW,EAAC;YAAS;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAEPvQ,OAAA,CAAC1G,IAAI;YAAC+V,IAAI;YAACQ,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAF,QAAA,eACvB5P,OAAA,CAACnG,SAAS;cACRgX,SAAS;cACTU,IAAI,EAAC,OAAO;cACZU,KAAK,EAAC,aAAa;cACnBnF,IAAI,EAAC,MAAM;cACX5B,KAAK,EAAEtJ,OAAO,CAACK,UAAW;cAC1B8O,QAAQ,EAAGC,CAAC,IAAKnP,UAAU,CAAC;gBAAC,GAAGD,OAAO;gBAAEK,UAAU,EAAE+O,CAAC,CAACC,MAAM,CAAC/F;cAAK,CAAC,CAAE;cACtEgH,eAAe,EAAE;gBAAEC,MAAM,EAAE;cAAK;YAAE;cAAA/B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAEPvQ,OAAA,CAAC1G,IAAI;YAAC+V,IAAI;YAACQ,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAF,QAAA,eACvB5P,OAAA,CAACnG,SAAS;cACRgX,SAAS;cACTU,IAAI,EAAC,OAAO;cACZU,KAAK,EAAC,WAAW;cACjBnF,IAAI,EAAC,MAAM;cACX5B,KAAK,EAAEtJ,OAAO,CAACM,QAAS;cACxB6O,QAAQ,EAAGC,CAAC,IAAKnP,UAAU,CAAC;gBAAC,GAAGD,OAAO;gBAAEM,QAAQ,EAAE8O,CAAC,CAACC,MAAM,CAAC/F;cAAK,CAAC,CAAE;cACpEgH,eAAe,EAAE;gBAAEC,MAAM,EAAE;cAAK;YAAE;cAAA/B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA,eACP,CACH,eAEDvQ,OAAA,CAAC1G,IAAI;UAAC+V,IAAI;UAACQ,EAAE,EAAE,EAAG;UAAAD,QAAA,eAChB5P,OAAA,CAAC/E,KAAK;YAACmX,SAAS,EAAC,KAAK;YAAC3C,OAAO,EAAE,CAAE;YAAC4C,cAAc,EAAC,UAAU;YAAAzC,QAAA,eAC1D5P,OAAA,CAAC5G,MAAM;cACLoX,OAAO,EAAC,UAAU;cAClBe,IAAI,EAAC,OAAO;cACZD,OAAO,EAAEA,CAAA,KAAMzP,UAAU,CAAC;gBACxBC,KAAK,EAAE,EAAE;gBAAEC,SAAS,EAAE,EAAE;gBAAEC,SAAS,EAAE,EAAE;gBAAEC,UAAU,EAAE,EAAE;gBACvDC,QAAQ,EAAE,EAAE;gBAAEC,gBAAgB,EAAE,EAAE;gBAAEC,aAAa,EAAE,EAAE;gBAAEC,SAAS,EAAE,EAAE;gBACpEC,cAAc,EAAE;cAClB,CAAC,CAAE;cAAAsN,QAAA,EACJ;YAED;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,EAGVhN,QAAQ,IAAIF,aAAa,CAACkD,MAAM,GAAG,CAAC,iBACnCvG,OAAA,CAAAE,SAAA;MAAA0P,QAAA,gBACE5P,OAAA,CAAChF,OAAO;QAAC0U,EAAE,EAAE;UAAEoC,EAAE,EAAE;QAAE;MAAE;QAAA1B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC1BvQ,OAAA,CAAC/E,KAAK;QAACmX,SAAS,EAAC,KAAK;QAAC3C,OAAO,EAAE,CAAE;QAACmB,UAAU,EAAC,QAAQ;QAAAhB,QAAA,gBACpD5P,OAAA,CAAC7G,UAAU;UAACqX,OAAO,EAAC,OAAO;UAAAZ,QAAA,GACxBvM,aAAa,CAACkD,MAAM,EAAC,uBACxB;QAAA;UAAA6J,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbvQ,OAAA,CAAC5G,MAAM;UACLmY,IAAI,EAAC,OAAO;UACZf,OAAO,EAAC,UAAU;UAClBc,OAAO,EAAE5H,cAAe;UAAAkG,QAAA,EACzB;QAED;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTvQ,OAAA,CAAC5G,MAAM;UACLmY,IAAI,EAAC,OAAO;UACZf,OAAO,EAAC,UAAU;UAClBc,OAAO,EAAExH,cAAe;UAAA8F,QAAA,EACzB;QAED;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTvQ,OAAA,CAAC5G,MAAM;UACLmY,IAAI,EAAC,OAAO;UACZf,OAAO,EAAC,UAAU;UAClBgB,SAAS,eAAExR,OAAA,CAAClC,UAAU;YAAAsS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC1Be,OAAO,EAAEhE,gBAAiB;UAAAsC,QAAA,EAC3B;QAED;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTvQ,OAAA,CAAC5G,MAAM;UACLmY,IAAI,EAAC,OAAO;UACZf,OAAO,EAAC,UAAU;UAClBR,KAAK,EAAC,OAAO;UACbwB,SAAS,eAAExR,OAAA,CAAChD,UAAU;YAAAoT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC1Be,OAAO,EAAEjE,gBAAiB;UAAAuC,QAAA,EAC3B;QAED;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA,eACR,CACH;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACI,CACR;;EAED;EACA,MAAM+B,eAAe,GAAGA,CAAA,KAAM;IAC5B,MAAMC,YAAY,GAAG5D,mBAAmB,CAACrN,YAAY,CAAC;IAEtD,IAAIA,YAAY,CAACiF,MAAM,KAAK,CAAC,EAAE;MAC7B,oBACEvG,OAAA,CAAC5F,KAAK;QAACyJ,QAAQ,EAAC,MAAM;QAAA+L,QAAA,EACnBxO,UAAU,IAAIQ,OAAO,CAACE,KAAK,IAAIF,OAAO,CAACG,SAAS,GAC7C,4CAA4C,GAC5C;MAAyB;QAAAqO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxB,CAAC;IAEZ;IAEA,oBACEvQ,OAAA,CAAAE,SAAA;MAAA0P,QAAA,gBACE5P,OAAA,CAACxF,cAAc;QAACgY,SAAS,EAAEnZ,KAAM;QAAAuW,QAAA,eAC/B5P,OAAA,CAAC3F,KAAK;UAACkX,IAAI,EAAC,OAAO;UAAA3B,QAAA,gBACjB5P,OAAA,CAACvF,SAAS;YAAAmV,QAAA,eACR5P,OAAA,CAACtF,QAAQ;cAAAkV,QAAA,gBACP5P,OAAA,CAACzF,SAAS;gBAAAqV,QAAA,EAAC;cAAO;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC9BvQ,OAAA,CAACzF,SAAS;gBAAAqV,QAAA,EAAC;cAAS;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAChCvQ,OAAA,CAACzF,SAAS;gBAAAqV,QAAA,EAAC;cAAO;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC9BvQ,OAAA,CAACzF,SAAS;gBAAAqV,QAAA,EAAC;cAAQ;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC/BvQ,OAAA,CAACzF,SAAS;gBAAAqV,QAAA,EAAC;cAAM;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC7BvQ,OAAA,CAACzF,SAAS;gBAAAqV,QAAA,EAAC;cAAK;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC5BvQ,OAAA,CAACzF,SAAS;gBAAAqV,QAAA,EAAC;cAAK;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC5BvQ,OAAA,CAACzF,SAAS;gBAAAqV,QAAA,EAAC;cAAY;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eACnCvQ,OAAA,CAACzF,SAAS;gBAAAqV,QAAA,EAAC;cAAW;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAClCvQ,OAAA,CAACzF,SAAS;gBAAAqV,QAAA,EAAC;cAAM;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACZvQ,OAAA,CAAC1F,SAAS;YAAAsV,QAAA,EACP2C,YAAY,CAAC3I,GAAG,CAAExC,IAAI,IAAK;cAC1B,MAAMqL,aAAa,GAAG1I,iBAAiB,CAAC3C,IAAI,CAAChD,OAAO,CAAC;cACrD,MAAMsO,cAAc,GAAGrL,oBAAoB,CAACD,IAAI,CAAC;cACjD,MAAMuL,eAAe,GAAG,CAACD,cAAc,GAAGnI,gCAAgC,CAACnD,IAAI,CAAC,GAAG,EAAE;cAErF,oBACEpH,OAAA,CAACtF,QAAQ;gBAAAkV,QAAA,gBACP5P,OAAA,CAACzF,SAAS;kBAAAqV,QAAA,eACR5P,OAAA,CAAC7G,UAAU;oBAACqX,OAAO,EAAC,OAAO;oBAACC,UAAU,EAAC,QAAQ;oBAAAb,QAAA,EAC5CxI,IAAI,CAAChD;kBAAO;oBAAAgM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eACZvQ,OAAA,CAACzF,SAAS;kBAAAqV,QAAA,EAAExI,IAAI,CAACrF;gBAAS;kBAAAqO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACvCvQ,OAAA,CAACzF,SAAS;kBAAAqV,QAAA,EAAExI,IAAI,CAACiB;gBAAO;kBAAA+H,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACrCvQ,OAAA,CAACzF,SAAS;kBAAAqV,QAAA,EAAExI,IAAI,CAACe;gBAAmB;kBAAAiI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACjDvQ,OAAA,CAACzF,SAAS;kBAAAqV,QAAA,EAAExI,IAAI,CAACgB;gBAAiB;kBAAAgI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC/CvQ,OAAA,CAACzF,SAAS;kBAAAqV,QAAA,GAAExI,IAAI,CAACyD,eAAe,IAAIzD,IAAI,CAAC0D,aAAa,EAAC,IAAE;gBAAA;kBAAAsF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eACrEvQ,OAAA,CAACzF,SAAS;kBAAAqV,QAAA,eACR5P,OAAA,CAAC9E,IAAI;oBACHqW,IAAI,EAAC,OAAO;oBACZU,KAAK,EAAE7K,IAAI,CAACmB,mBAAoB;oBAChCyH,KAAK,EAAE5I,IAAI,CAACmB,mBAAmB,KAAK,YAAY,GAAG,SAAS,GAAG;kBAAU;oBAAA6H,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1E;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACO,CAAC,eACZvQ,OAAA,CAACzF,SAAS;kBAAAqV,QAAA,EACP,CAAC,MAAM;oBACN,MAAMzF,YAAY,GAAG/C,IAAI,CAAC+C,YAAY,IAAI,CAAC;oBAC3C,MAAMyI,iBAAiB,GAAGzI,YAAY,KAAK,CAAC,GAAG,eAAe,GACrCA,YAAY,KAAK,CAAC,GAAG,eAAe,GACpCA,YAAY,KAAK,CAAC,GAAG,aAAa,GAClCA,YAAY,KAAK,CAAC,GAAG,UAAU,GAC/B,aAAa;oBACtC,MAAM0I,MAAM,GAAG1I,YAAY,KAAK,CAAC,GAAG,SAAS,GAC/BA,YAAY,KAAK,CAAC,GAAG,OAAO,GAAG,SAAS;oBAEtD,oBACEnK,OAAA,CAAC7E,OAAO;sBAAC2X,KAAK,EAAE,aAAa1L,IAAI,CAACiD,qBAAqB,IAAI,eAAe,cAAcjD,IAAI,CAACkD,mBAAmB,IAAI,eAAe,EAAG;sBAAAsF,QAAA,eACpI5P,OAAA,CAAC9E,IAAI;wBACHqW,IAAI,EAAC,OAAO;wBACZU,KAAK,EAAEW,iBAAkB;wBACzB5C,KAAK,EAAE6C,MAAO;wBACdE,IAAI,EAAE5I,YAAY,KAAK,CAAC,gBAAGnK,OAAA,CAACtC,SAAS;0BAAA0S,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,gBAAGvQ,OAAA,CAACpC,WAAW;0BAAAwS,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAE;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC5D;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACK,CAAC;kBAEd,CAAC,EAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACK,CAAC,eACZvQ,OAAA,CAACzF,SAAS;kBAAAqV,QAAA,EACP6C,aAAa,gBACZzS,OAAA,CAAC9E,IAAI;oBACHqW,IAAI,EAAC,OAAO;oBACZwB,IAAI,eAAE/S,OAAA,CAACtC,SAAS;sBAAA0S,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAE;oBACpB0B,KAAK,EAAC,aAAa;oBACnBjC,KAAK,EAAC;kBAAS;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChB,CAAC,gBAEFvQ,OAAA,CAAC9E,IAAI;oBACHqW,IAAI,EAAC,OAAO;oBACZwB,IAAI,eAAE/S,OAAA,CAACpC,WAAW;sBAAAwS,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAE;oBACtB0B,KAAK,EAAC,iBAAiB;oBACvBjC,KAAK,EAAC;kBAAS;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChB;gBACF;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACQ,CAAC,eACZvQ,OAAA,CAACzF,SAAS;kBAAAqV,QAAA,EACP6C,aAAa,gBACZzS,OAAA,CAAC7E,OAAO;oBAAC2X,KAAK,EAAC,yBAAsB;oBAAAlD,QAAA,eACnC5P,OAAA,CAAC9E,IAAI;sBACH6X,IAAI,eAAE/S,OAAA,CAACtC,SAAS;wBAAA0S,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAE;sBACpB0B,KAAK,EAAC,aAAa;sBACnBjC,KAAK,EAAC,SAAS;sBACfuB,IAAI,EAAC;oBAAO;sBAAAnB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACb;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACK,CAAC,GACRmC,cAAc,gBAChB1S,OAAA,CAAC7E,OAAO;oBAAC2X,KAAK,EAAC,qCAAqC;oBAAAlD,QAAA,eAClD5P,OAAA,CAACrF,UAAU;sBACT4W,IAAI,EAAC,OAAO;sBACZD,OAAO,EAAEA,CAAA,KAAM3G,gBAAgB,CAACvD,IAAI,CAAE;sBACtC4I,KAAK,EAAC,SAAS;sBAAAJ,QAAA,eAEf5P,OAAA,CAAC5D,OAAO;wBAAAgU,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACD;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC,gBAEVvQ,OAAA,CAAC7E,OAAO;oBAAC2X,KAAK,EAAEH,eAAgB;oBAAA/C,QAAA,eAC9B5P,OAAA;sBAAA4P,QAAA,eACE5P,OAAA,CAACrF,UAAU;wBACT4W,IAAI,EAAC,OAAO;wBACZK,QAAQ;wBACRN,OAAO,EAAEA,CAAA,KAAMrL,YAAY,CAAC0M,eAAe,EAAE,SAAS,CAAE;wBAAA/C,QAAA,eAExD5P,OAAA,CAACJ,SAAS;0BAAAwQ,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACT;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACA;gBACV;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACQ,CAAC;cAAA,GA3FCnJ,IAAI,CAAChD,OAAO;gBAAAgM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OA4FjB,CAAC;YAEf,CAAC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC,EAEhBvB,aAAa,CAAC1N,YAAY,CAAC,GAAG,CAAC,iBAC9BtB,OAAA,CAAC9G,GAAG;QAACwW,EAAE,EAAE;UAAEsD,OAAO,EAAE,MAAM;UAAEX,cAAc,EAAE,QAAQ;UAAEY,EAAE,EAAE;QAAE,CAAE;QAAArD,QAAA,eAC5D5P,OAAA,CAAClF,UAAU;UACToY,KAAK,EAAElE,aAAa,CAAC1N,YAAY,CAAE;UACnC6R,IAAI,EAAE5Q,WAAY;UAClBwO,QAAQ,EAAEA,CAACtG,KAAK,EAAES,KAAK,KAAK1I,cAAc,CAAC0I,KAAK,CAAE;UAClD8E,KAAK,EAAC;QAAS;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CACN;IAAA,eACD,CAAC;EAEP,CAAC;;EAED;EACA,MAAM6C,yBAAyB,GAAGA,CAAA,KAAM;IACtC,MAAMb,YAAY,GAAG5D,mBAAmB,CAACnN,sBAAsB,CAAC;IAEhE,IAAIA,sBAAsB,CAAC+E,MAAM,KAAK,CAAC,EAAE;MACvC,oBACEvG,OAAA,CAAC5F,KAAK;QAACyJ,QAAQ,EAAC,MAAM;QAAA+L,QAAA,EACnBxO,UAAU,IAAIQ,OAAO,CAACI,SAAS,GAC5B,uDAAuD,GACvD;MAAoC;QAAAoO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnC,CAAC;IAEZ;IAEA,oBACEvQ,OAAA,CAAAE,SAAA;MAAA0P,QAAA,gBACE5P,OAAA,CAACxF,cAAc;QAACgY,SAAS,EAAEnZ,KAAM;QAAAuW,QAAA,eAC/B5P,OAAA,CAAC3F,KAAK;UAACkX,IAAI,EAAC,OAAO;UAAA3B,QAAA,gBACjB5P,OAAA,CAACvF,SAAS;YAAAmV,QAAA,eACR5P,OAAA,CAACtF,QAAQ;cAAAkV,QAAA,GACNrM,QAAQ,iBACPvD,OAAA,CAACzF,SAAS;gBAAC8Y,OAAO,EAAC,UAAU;gBAAAzD,QAAA,eAC3B5P,OAAA,CAACrF,UAAU;kBACT4W,IAAI,EAAC,OAAO;kBACZD,OAAO,EAAEjO,aAAa,CAACkD,MAAM,KAAK/E,sBAAsB,CAAC+E,MAAM,GAAGuD,cAAc,GAAGJ,cAAe;kBAAAkG,QAAA,EAEjGvM,aAAa,CAACkD,MAAM,KAAK/E,sBAAsB,CAAC+E,MAAM,gBAAGvG,OAAA,CAAC1C,SAAS;oBAAA8S,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,gBAAGvQ,OAAA,CAACtC,SAAS;oBAAA0S,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7E;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CACZ,eACDvQ,OAAA,CAACzF,SAAS;gBAAAqV,QAAA,eACR5P,OAAA,CAAC/E,KAAK;kBAACmX,SAAS,EAAC,KAAK;kBAACxB,UAAU,EAAC,QAAQ;kBAACnB,OAAO,EAAE,CAAE;kBAAAG,QAAA,gBACpD5P,OAAA,CAAC7G,UAAU;oBAACqX,OAAO,EAAC,OAAO;oBAACC,UAAU,EAAC,MAAM;oBAAAb,QAAA,EAAC;kBAAc;oBAAAQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACzEvQ,OAAA,CAACrF,UAAU;oBAAC4W,IAAI,EAAC,OAAO;oBAACD,OAAO,EAAEA,CAAA,KAAM;sBACtC1O,SAAS,CAAC,oBAAoB,CAAC;sBAC/BE,YAAY,CAACD,SAAS,KAAK,KAAK,GAAG,MAAM,GAAG,KAAK,CAAC;oBACpD,CAAE;oBAAA+M,QAAA,EACCjN,MAAM,KAAK,oBAAoB,GAAIE,SAAS,KAAK,KAAK,gBAAG7C,OAAA,CAAClB,cAAc;sBAAAsR,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,gBAAGvQ,OAAA,CAACpB,cAAc;sBAAAwR,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,gBAAIvQ,OAAA,CAACpB,cAAc;sBAAAwR,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7G,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACZvQ,OAAA,CAACzF,SAAS;gBAAAqV,QAAA,EAAC;cAAO;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC9BvQ,OAAA,CAACzF,SAAS;gBAAAqV,QAAA,eACR5P,OAAA,CAAC/E,KAAK;kBAACmX,SAAS,EAAC,KAAK;kBAACxB,UAAU,EAAC,QAAQ;kBAACnB,OAAO,EAAE,CAAE;kBAAAG,QAAA,gBACpD5P,OAAA,CAAC7G,UAAU;oBAACqX,OAAO,EAAC,OAAO;oBAACC,UAAU,EAAC,MAAM;oBAAAb,QAAA,EAAC;kBAAI;oBAAAQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eAC/DvQ,OAAA,CAACrF,UAAU;oBAAC4W,IAAI,EAAC,OAAO;oBAACD,OAAO,EAAEA,CAAA,KAAM;sBACtC1O,SAAS,CAAC,qBAAqB,CAAC;sBAChCE,YAAY,CAACD,SAAS,KAAK,KAAK,GAAG,MAAM,GAAG,KAAK,CAAC;oBACpD,CAAE;oBAAA+M,QAAA,EACCjN,MAAM,KAAK,qBAAqB,GAAIE,SAAS,KAAK,KAAK,gBAAG7C,OAAA,CAAClB,cAAc;sBAAAsR,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,gBAAGvQ,OAAA,CAACpB,cAAc;sBAAAwR,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,gBAAIvQ,OAAA,CAACpB,cAAc;sBAAAwR,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9G,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACZvQ,OAAA,CAACzF,SAAS;gBAAAqV,QAAA,EAAC;cAAS;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAChCvQ,OAAA,CAACzF,SAAS;gBAAAqV,QAAA,EAAC;cAAS;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAChCvQ,OAAA,CAACzF,SAAS;gBAAAqV,QAAA,EAAC;cAAS;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAChCvQ,OAAA,CAACzF,SAAS;gBAAAqV,QAAA,EAAC;cAAU;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eACjCvQ,OAAA,CAACzF,SAAS;gBAAAqV,QAAA,EAAC;cAAS;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAChCvQ,OAAA,CAACzF,SAAS;gBAAAqV,QAAA,EAAC;cAAM;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACZvQ,OAAA,CAAC1F,SAAS;YAAAsV,QAAA,EACP2C,YAAY,CAAC3I,GAAG,CAAE9C,IAAI,iBACrB9G,OAAA,CAACtF,QAAQ;cAEP4Y,QAAQ,EAAEjQ,aAAa,CAAC6E,QAAQ,CAACpB,IAAI,CAAC+C,iBAAiB,CAAE;cACzD0J,KAAK;cAAA3D,QAAA,GAEJrM,QAAQ,iBACPvD,OAAA,CAACzF,SAAS;gBAAC8Y,OAAO,EAAC,UAAU;gBAAAzD,QAAA,eAC3B5P,OAAA,CAACrF,UAAU;kBACT4W,IAAI,EAAC,OAAO;kBACZD,OAAO,EAAEA,CAAA,KAAMjI,mBAAmB,CAACvC,IAAI,CAAC+C,iBAAiB,CAAE;kBAC3DmG,KAAK,EAAE3M,aAAa,CAAC6E,QAAQ,CAACpB,IAAI,CAAC+C,iBAAiB,CAAC,GAAG,SAAS,GAAG,SAAU;kBAAA+F,QAAA,EAE7EvM,aAAa,CAAC6E,QAAQ,CAACpB,IAAI,CAAC+C,iBAAiB,CAAC,gBAAG7J,OAAA,CAACtC,SAAS;oBAAA0S,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,gBAAGvQ,OAAA,CAAC5D,OAAO;oBAAAgU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CACZ,eACDvQ,OAAA,CAACzF,SAAS;gBAAAqV,QAAA,eACR5P,OAAA,CAAC7G,UAAU;kBAACqX,OAAO,EAAC,OAAO;kBAACC,UAAU,EAAC,QAAQ;kBAAAb,QAAA,EAC5C9I,IAAI,CAACmC;gBAAkB;kBAAAmH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACd;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACZvQ,OAAA,CAACzF,SAAS;gBAAAqV,QAAA,eACR5P,OAAA,CAAC9E,IAAI;kBAACqW,IAAI,EAAC,OAAO;kBAACU,KAAK,EAAEnL,IAAI,CAAC1C,OAAQ;kBAACoM,OAAO,EAAC;gBAAU;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpD,CAAC,eACZvQ,OAAA,CAACzF,SAAS;gBAAAqV,QAAA,EAAE,IAAIjJ,IAAI,CAACG,IAAI,CAACC,mBAAmB,CAAC,CAACgH,kBAAkB,CAAC;cAAC;gBAAAqC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAChFvQ,OAAA,CAACzF,SAAS;gBAAAqV,QAAA,eACR5P,OAAA,CAAC/E,KAAK;kBAACmX,SAAS,EAAC,KAAK;kBAACxB,UAAU,EAAC,QAAQ;kBAACnB,OAAO,EAAE,CAAE;kBAAAG,QAAA,gBACpD5P,OAAA,CAACV,UAAU;oBAAC6Q,QAAQ,EAAC;kBAAO;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC/BvQ,OAAA,CAAC7G,UAAU;oBAACqX,OAAO,EAAC,OAAO;oBAAAZ,QAAA,EAAE9I,IAAI,CAAC9E,SAAS,IAAI8E,IAAI,CAACzC;kBAAY;oBAAA+L,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAa,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACZvQ,OAAA,CAACzF,SAAS;gBAAAqV,QAAA,eACR5P,OAAA,CAAC7G,UAAU;kBAACqX,OAAO,EAAC,OAAO;kBAAAZ,QAAA,EACxB9I,IAAI,CAACxC,YAAY,GAChB,CAAC,MAAM;oBACL,MAAMjC,SAAS,GAAGnB,SAAS,CAACmK,IAAI,CAACmI,CAAC,IAAIA,CAAC,CAAClP,YAAY,KAAKwC,IAAI,CAACxC,YAAY,CAAC;oBAC3E,OAAOjC,SAAS,GAAG,GAAGA,SAAS,CAACoR,IAAI,MAAMpR,SAAS,CAACqR,KAAK,EAAE,GAAG,uBAAuB;kBACvF,CAAC,EAAE,CAAC,GACD5M,IAAI,CAAC6M,oBAAoB,IAAI;gBAAM;kBAAAvD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAE9B;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACZvQ,OAAA,CAACzF,SAAS;gBAAAqV,QAAA,eACR5P,OAAA,CAAC7G,UAAU;kBAACqX,OAAO,EAAC,OAAO;kBAAAZ,QAAA,GAAE9I,IAAI,CAACvC,kBAAkB,EAAC,IAAE;gBAAA;kBAAA6L,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3D,CAAC,eACZvQ,OAAA,CAACzF,SAAS;gBAAAqV,QAAA,eACR5P,OAAA,CAAC9E,IAAI;kBACHqW,IAAI,EAAC,OAAO;kBACZU,KAAK,EAAE,GAAGnL,IAAI,CAACrC,iBAAiB,KAAM;kBACtCuL,KAAK,EAAE7G,UAAU,CAACrC,IAAI,CAACrC,iBAAiB,CAAC,IAAI,GAAG,GAAG,SAAS,GAAG,SAAU;kBACzEsO,IAAI,EAAE5J,UAAU,CAACrC,IAAI,CAACrC,iBAAiB,CAAC,IAAI,GAAG,gBAAGzE,OAAA,CAACtC,SAAS;oBAAA0S,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,gBAAGvQ,OAAA,CAACpC,WAAW;oBAAAwS,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAE;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnF;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO,CAAC,eACZvQ,OAAA,CAACzF,SAAS;gBAAAqV,QAAA,eACR5P,OAAA,CAAC9E,IAAI;kBACHqW,IAAI,EAAC,OAAO;kBACZU,KAAK,EAAEnL,IAAI,CAAC9B,gBAAgB,IAAI,UAAW;kBAC3CgL,KAAK,EAAElJ,IAAI,CAAC9B,gBAAgB,KAAK,UAAU,GAAG,SAAS,GAAG8B,IAAI,CAAC9B,gBAAgB,KAAK,cAAc,GAAG,OAAO,GAAG;gBAAU;kBAAAoL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1H;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO,CAAC,eACZvQ,OAAA,CAACzF,SAAS;gBAAAqV,QAAA,eACR5P,OAAA,CAAC/E,KAAK;kBAACmX,SAAS,EAAC,KAAK;kBAAC3C,OAAO,EAAE,GAAI;kBAAAG,QAAA,gBAClC5P,OAAA,CAAC7E,OAAO;oBAAC2X,KAAK,EAAC,qBAAqB;oBAAAlD,QAAA,eAClC5P,OAAA,CAACrF,UAAU;sBACT4W,IAAI,EAAC,OAAO;sBACZD,OAAO,EAAEA,CAAA,KAAM;wBACblO,eAAe,CAAC0D,IAAI,CAAC;wBACrB5D,aAAa,CAAC,MAAM,CAAC;wBACrBF,aAAa,CAAC,IAAI,CAAC;sBACrB,CAAE;sBAAA4M,QAAA,eAEF5P,OAAA,CAAClD,QAAQ;wBAAAsT,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACF;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC,eACVvQ,OAAA,CAAC7E,OAAO;oBAAC2X,KAAK,EAAC,YAAY;oBAAAlD,QAAA,eACzB5P,OAAA,CAACrF,UAAU;sBACT4W,IAAI,EAAC,OAAO;sBACZD,OAAO,EAAEA,CAAA,KAAM1F,iBAAiB,CAAC9E,IAAI,CAAE;sBACvC8K,QAAQ,EAAE5N,mBAAoB;sBAAA4L,QAAA,eAE9B5P,OAAA,CAACtD,OAAO;wBAAA0T,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACD;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC,eACVvQ,OAAA,CAAC7E,OAAO;oBAAC2X,KAAK,EAAC,SAAS;oBAAAlD,QAAA,eACtB5P,OAAA,CAACrF,UAAU;sBACT4W,IAAI,EAAC,OAAO;sBACZvB,KAAK,EAAC,OAAO;sBACbsB,OAAO,EAAEA,CAAA,KAAMnE,0BAA0B,CAACrG,IAAI,CAAE;sBAChD8K,QAAQ,EAAE5N,mBAAoB;sBAAA4L,QAAA,eAE9B5P,OAAA,CAAChD,UAAU;wBAAAoT,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA,GA7FPzJ,IAAI,CAAC+C,iBAAiB;cAAAuG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA8FnB,CACX;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC,EAEhBvB,aAAa,CAACxN,sBAAsB,CAAC,GAAG,CAAC,iBACxCxB,OAAA,CAAC9G,GAAG;QAACwW,EAAE,EAAE;UAAEsD,OAAO,EAAE,MAAM;UAAEX,cAAc,EAAE,QAAQ;UAAEY,EAAE,EAAE;QAAE,CAAE;QAAArD,QAAA,eAC5D5P,OAAA,CAAClF,UAAU;UACToY,KAAK,EAAElE,aAAa,CAACxN,sBAAsB,CAAE;UAC7C2R,IAAI,EAAE5Q,WAAY;UAClBwO,QAAQ,EAAEA,CAACtG,KAAK,EAAES,KAAK,KAAK1I,cAAc,CAAC0I,KAAK,CAAE;UAClD8E,KAAK,EAAC;QAAS;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CACN;IAAA,eACD,CAAC;EAEP,CAAC;;EAED;EACA,MAAMqD,0BAA0B,GAAGA,CAAA,KAAM;IACvC,IAAI3Q,UAAU,KAAK,QAAQ,IAAIA,UAAU,KAAK,MAAM,EAAE,OAAO,IAAI;IAEjE,oBACEjD,OAAA,CAACvG,MAAM;MAACkK,IAAI,EAAEZ,UAAW;MAAC8Q,OAAO,EAAE9I,WAAY;MAAC+I,QAAQ,EAAC,IAAI;MAACjD,SAAS;MAAAjB,QAAA,gBACrE5P,OAAA,CAACtG,WAAW;QAAAkW,QAAA,EACT3M,UAAU,KAAK,QAAQ,GAAG,sBAAsB,GAAG;MAAyB;QAAAmN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClE,CAAC,eACdvQ,OAAA,CAACrG,aAAa;QAAAiW,QAAA,eACZ5P,OAAA,CAAC1G,IAAI;UAACkW,SAAS;UAACC,OAAO,EAAE,CAAE;UAACC,EAAE,EAAE;YAAEuD,EAAE,EAAE;UAAE,CAAE;UAAArD,QAAA,gBACxC5P,OAAA,CAAC1G,IAAI;YAAC+V,IAAI;YAACQ,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAF,QAAA,eACvB5P,OAAA,CAAC9F,YAAY;cACX6Z,OAAO,EAAE/S,IAAI,CAAC6F,MAAM,CAACO,IAAI,IAAI;gBAC3B;gBACA,MAAM4M,UAAU,GAAG5M,IAAI,CAAChD,OAAO,KAAKF,QAAQ,CAACE,OAAO;gBACpD,MAAM6P,cAAc,GAAG,CAACnT,cAAc,CAAC0H,IAAI,CAAC1B,IAAI,IAAIA,IAAI,CAAC1C,OAAO,KAAKgD,IAAI,CAAChD,OAAO,CAAC;gBAClF,MAAM8P,cAAc,GAAG7M,oBAAoB,CAACD,IAAI,CAAC;gBAEjD,OAAO4M,UAAU,IAAKC,cAAc,IAAIC,cAAe;cACzD,CAAC,CAAE;cACHC,cAAc,EAAGzF,MAAM,IAAK,GAAGA,MAAM,CAACtK,OAAO,MAAMsK,MAAM,CAAC3M,SAAS,EAAG;cACtEmJ,KAAK,EAAElK,IAAI,CAACqK,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAClH,OAAO,KAAKF,QAAQ,CAACE,OAAO,CAAC,IAAI,IAAK;cAC9D2M,QAAQ,EAAEA,CAACtG,KAAK,EAAEC,QAAQ,KAAK;gBAC7B,IAAIA,QAAQ,EAAE;kBACZS,gBAAgB,CAACT,QAAQ,CAAC;gBAC5B,CAAC,MAAM;kBACLvG,WAAW,CAACoF,IAAI,KAAK;oBAAE,GAAGA,IAAI;oBAAEnF,OAAO,EAAE,EAAE;oBAAEG,kBAAkB,EAAE;kBAAG,CAAC,CAAC,CAAC;gBACzE;cACF,CAAE;cACF6P,WAAW,EAAGC,MAAM,iBAClBrU,OAAA,CAACnG,SAAS;gBAAA,GACJwa,MAAM;gBACVpC,KAAK,EAAC,QAAQ;gBACdnB,WAAW,EAAC,iCAAiC;gBAC7CwD,QAAQ;gBACRC,UAAU,EAAC;cAAgD;gBAAAnE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5D,CACD;cACFiE,YAAY,EAAEA,CAACC,KAAK,EAAE/F,MAAM,KAAK;gBAC/B,MAAMgG,UAAU,GAAGrN,oBAAoB,CAACqH,MAAM,CAAC;gBAC/C,MAAMvE,YAAY,GAAGuE,MAAM,CAACvE,YAAY,IAAI,CAAC;gBAE7C,oBACEnK,OAAA,CAAC9G,GAAG;kBAACsZ,SAAS,EAAC,IAAI;kBAAA,GAAKiC,KAAK;kBAAA7E,QAAA,eAC3B5P,OAAA,CAAC9G,GAAG;oBAACwW,EAAE,EAAE;sBAAEiF,KAAK,EAAE;oBAAO,CAAE;oBAAA/E,QAAA,eACzB5P,OAAA,CAAC/E,KAAK;sBAACmX,SAAS,EAAC,KAAK;sBAACC,cAAc,EAAC,eAAe;sBAACzB,UAAU,EAAC,QAAQ;sBAAAhB,QAAA,gBACvE5P,OAAA,CAAC9G,GAAG;wBAAA0W,QAAA,gBACF5P,OAAA,CAAC7G,UAAU;0BAACqX,OAAO,EAAC,OAAO;0BAACC,UAAU,EAAC,QAAQ;0BAAAb,QAAA,EAC5ClB,MAAM,CAACtK;wBAAO;0BAAAgM,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACL,CAAC,eACbvQ,OAAA,CAAC7G,UAAU;0BAACqX,OAAO,EAAC,SAAS;0BAACR,KAAK,EAAC,gBAAgB;0BAAAJ,QAAA,GACjDlB,MAAM,CAAC3M,SAAS,EAAC,KAAG,EAAC2M,MAAM,CAACvG,mBAAmB,EAAC,UAAG,EAACuG,MAAM,CAACtG,iBAAiB;wBAAA;0BAAAgI,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACnE,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACV,CAAC,eACNvQ,OAAA,CAAC/E,KAAK;wBAACmX,SAAS,EAAC,KAAK;wBAAC3C,OAAO,EAAE,CAAE;wBAAAG,QAAA,gBAChC5P,OAAA,CAAC9E,IAAI;0BACHqW,IAAI,EAAC,OAAO;0BACZU,KAAK,EAAEvD,MAAM,CAACnG,mBAAoB;0BAClCyH,KAAK,EAAEtB,MAAM,CAACnG,mBAAmB,KAAK,YAAY,GAAG,SAAS,GAAG;wBAAU;0BAAA6H,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC5E,CAAC,eACFvQ,OAAA,CAAC9E,IAAI;0BACHqW,IAAI,EAAC,OAAO;0BACZU,KAAK,EAAE9H,YAAY,KAAK,CAAC,GAAG,WAAW,GAAG,YAAa;0BACvD6F,KAAK,EAAE7F,YAAY,KAAK,CAAC,GAAG,SAAS,GAAG;wBAAU;0BAAAiG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACnD,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACG,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAEV;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAEPvQ,OAAA,CAAC1G,IAAI;YAAC+V,IAAI;YAACQ,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAF,QAAA,eACvB5P,OAAA,CAACnG,SAAS;cACRgX,SAAS;cACToB,KAAK,EAAC,aAAa;cACnB/G,KAAK,EAAEhH,QAAQ,CAACG,YAAa;cAC7B0M,QAAQ,EAAGC,CAAC,IAAKhG,gBAAgB,CAAC,cAAc,EAAEgG,CAAC,CAACC,MAAM,CAAC/F,KAAK,CAAE;cAClEoJ,QAAQ;YAAA;cAAAlE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAEPvQ,OAAA,CAAC1G,IAAI;YAAC+V,IAAI;YAACQ,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAF,QAAA,eACvB5P,OAAA,CAAClG,WAAW;cAAC+W,SAAS;cAACyD,QAAQ;cAAA1E,QAAA,gBAC7B5P,OAAA,CAACjG,UAAU;gBAAA6V,QAAA,EAAC;cAAW;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACpCvQ,OAAA,CAAChG,MAAM;gBACLkR,KAAK,EAAEhH,QAAQ,CAACI,YAAa;gBAC7ByM,QAAQ,EAAGC,CAAC,IAAKhG,gBAAgB,CAAC,cAAc,EAAEgG,CAAC,CAACC,MAAM,CAAC/F,KAAK,CAAE;gBAClE+G,KAAK,EAAC,aAAa;gBAAArC,QAAA,EAElB1O,SAAS,CAAC0I,GAAG,CAAEvH,SAAS,iBACvBrC,OAAA,CAAC/F,QAAQ;kBAA8BiR,KAAK,EAAE7I,SAAS,CAACiC,YAAa;kBAAAsL,QAAA,GAClEvN,SAAS,CAACoR,IAAI,EAAC,KAAG,EAACpR,SAAS,CAACqR,KAAK,EAAC,GAAC,EAACrR,SAAS,CAACuS,OAAO,EAAC,SAAO,EAACvS,SAAS,CAACwS,YAAY,EAAC,GACzF;gBAAA,GAFexS,SAAS,CAACiC,YAAY;kBAAA8L,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAE3B,CACX;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eAEPvQ,OAAA,CAAC1G,IAAI;YAAC+V,IAAI;YAACQ,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAF,QAAA,eACvB5P,OAAA,CAACnG,SAAS;cACRgX,SAAS;cACToB,KAAK,EAAC,0BAA0B;cAChCnF,IAAI,EAAC,QAAQ;cACb5B,KAAK,EAAEhH,QAAQ,CAACK,kBAAmB;cACnCwM,QAAQ,EAAGC,CAAC,IAAKhG,gBAAgB,CAAC,oBAAoB,EAAEgG,CAAC,CAACC,MAAM,CAAC/F,KAAK,CAAE;cACxEoJ,QAAQ;YAAA;cAAAlE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAEPvQ,OAAA,CAAC1G,IAAI;YAAC+V,IAAI;YAACQ,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAF,QAAA,eACvB5P,OAAA,CAAClG,WAAW;cAAC+W,SAAS;cAAAjB,QAAA,gBACpB5P,OAAA,CAACjG,UAAU;gBAAA6V,QAAA,EAAC;cAAU;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACnCvQ,OAAA,CAAChG,MAAM;gBACLkR,KAAK,EAAEhH,QAAQ,CAACM,iBAAkB;gBAClCuM,QAAQ,EAAGC,CAAC,IAAKhG,gBAAgB,CAAC,mBAAmB,EAAEgG,CAAC,CAACC,MAAM,CAAC/F,KAAK,CAAE;gBACvE+G,KAAK,EAAC,eAAY;gBAAArC,QAAA,gBAElB5P,OAAA,CAAC/F,QAAQ;kBAACiR,KAAK,EAAC,IAAI;kBAAA0E,QAAA,EAAC;gBAAE;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,eAClCvQ,OAAA,CAAC/F,QAAQ;kBAACiR,KAAK,EAAC,KAAK;kBAAA0E,QAAA,EAAC;gBAAG;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eAEPvQ,OAAA,CAAC1G,IAAI;YAAC+V,IAAI;YAACQ,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAF,QAAA,eACvB5P,OAAA,CAACnG,SAAS;cACRgX,SAAS;cACToB,KAAK,EAAC,wBAAmB;cACzBnF,IAAI,EAAC,QAAQ;cACb5B,KAAK,EAAEhH,QAAQ,CAACO,iBAAkB;cAClCsM,QAAQ,EAAGC,CAAC,IAAKhG,gBAAgB,CAAC,mBAAmB,EAAEgG,CAAC,CAACC,MAAM,CAAC/F,KAAK,CAAE;cACvEoJ,QAAQ;cACRC,UAAU,EAAC;YAAmC;cAAAnE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/C;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAEPvQ,OAAA,CAAC1G,IAAI;YAAC+V,IAAI;YAACQ,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAF,QAAA,eACvB5P,OAAA,CAAClG,WAAW;cAAC+W,SAAS;cAAAjB,QAAA,gBACpB5P,OAAA,CAACjG,UAAU;gBAAA6V,QAAA,EAAC;cAAU;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACnCvQ,OAAA,CAAChG,MAAM;gBACLkR,KAAK,EAAEhH,QAAQ,CAACQ,iBAAkB;gBAClCqM,QAAQ,EAAGC,CAAC,IAAKhG,gBAAgB,CAAC,mBAAmB,EAAEgG,CAAC,CAACC,MAAM,CAAC/F,KAAK,CAAE;gBACvE+G,KAAK,EAAC,YAAY;gBAAArC,QAAA,gBAElB5P,OAAA,CAAC/F,QAAQ;kBAACiR,KAAK,EAAC,IAAI;kBAAA0E,QAAA,EAAC;gBAAE;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,eAClCvQ,OAAA,CAAC/F,QAAQ;kBAACiR,KAAK,EAAC,KAAK;kBAAA0E,QAAA,EAAC;gBAAG;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eAGPvQ,OAAA,CAAC1G,IAAI;YAAC+V,IAAI;YAACQ,EAAE,EAAE,EAAG;YAAAD,QAAA,eAChB5P,OAAA,CAAChF,OAAO;cAAC0U,EAAE,EAAE;gBAAEoC,EAAE,EAAE;cAAE,CAAE;cAAAlC,QAAA,eACrB5P,OAAA,CAAC7G,UAAU;gBAACqX,OAAO,EAAC,OAAO;gBAACR,KAAK,EAAC,gBAAgB;gBAAAJ,QAAA,EAAC;cAEnD;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAEPvQ,OAAA,CAAC1G,IAAI;YAAC+V,IAAI;YAACQ,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAF,QAAA,eACvB5P,OAAA,CAACnG,SAAS;cACRgX,SAAS;cACToB,KAAK,EAAC,8BAA2B;cACjCnF,IAAI,EAAC,QAAQ;cACb5B,KAAK,EAAEhH,QAAQ,CAACU,oBAAqB;cACrCmM,QAAQ,EAAGC,CAAC,IAAKhG,gBAAgB,CAAC,sBAAsB,EAAEgG,CAAC,CAACC,MAAM,CAAC/F,KAAK,CAAE;cAC1EqJ,UAAU,EAAC;YAA6B;cAAAnE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAEPvQ,OAAA,CAAC1G,IAAI;YAAC+V,IAAI;YAACQ,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAF,QAAA,eACvB5P,OAAA,CAACnG,SAAS;cACRgX,SAAS;cACToB,KAAK,EAAC,gBAAa;cACnBnF,IAAI,EAAC,QAAQ;cACb5B,KAAK,EAAEhH,QAAQ,CAACW,OAAQ;cACxBkM,QAAQ,EAAGC,CAAC,IAAKhG,gBAAgB,CAAC,SAAS,EAAEgG,CAAC,CAACC,MAAM,CAAC/F,KAAK,CAAE;cAC7DqJ,UAAU,EAAC;YAAkB;cAAAnE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAEPvQ,OAAA,CAAC1G,IAAI;YAAC+V,IAAI;YAACQ,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAF,QAAA,eACvB5P,OAAA,CAACnG,SAAS;cACRgX,SAAS;cACToB,KAAK,EAAC,uBAAuB;cAC7BnF,IAAI,EAAC,QAAQ;cACb5B,KAAK,EAAEhH,QAAQ,CAACY,cAAe;cAC/BiM,QAAQ,EAAGC,CAAC,IAAKhG,gBAAgB,CAAC,gBAAgB,EAAEgG,CAAC,CAACC,MAAM,CAAC/F,KAAK,CAAE;cACpEqJ,UAAU,EAAC;YAAgC;cAAAnE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5C;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAEPvQ,OAAA,CAAC1G,IAAI;YAAC+V,IAAI;YAACQ,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAF,QAAA,eACvB5P,OAAA,CAACnG,SAAS;cACRgX,SAAS;cACToB,KAAK,EAAC,oBAAoB;cAC1BnF,IAAI,EAAC,QAAQ;cACb5B,KAAK,EAAEhH,QAAQ,CAACa,YAAa;cAC7BgM,QAAQ,EAAGC,CAAC,IAAKhG,gBAAgB,CAAC,cAAc,EAAEgG,CAAC,CAACC,MAAM,CAAC/F,KAAK,CAAE;cAClEqJ,UAAU,EAAC;YAA2B;cAAAnE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAEPvQ,OAAA,CAAC1G,IAAI;YAAC+V,IAAI;YAACQ,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAF,QAAA,eACvB5P,OAAA,CAAClG,WAAW;cAAC+W,SAAS;cAAAjB,QAAA,gBACpB5P,OAAA,CAACjG,UAAU;gBAAA6V,QAAA,EAAC;cAAgB;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACzCvQ,OAAA,CAAChG,MAAM;gBACLkR,KAAK,EAAEhH,QAAQ,CAACc,gBAAiB;gBACjC+L,QAAQ,EAAGC,CAAC,IAAKhG,gBAAgB,CAAC,kBAAkB,EAAEgG,CAAC,CAACC,MAAM,CAAC/F,KAAK,CAAE;gBACtE+G,KAAK,EAAC,kBAAkB;gBAAArC,QAAA,gBAExB5P,OAAA,CAAC/F,QAAQ;kBAACiR,KAAK,EAAC,UAAU;kBAAA0E,QAAA,eACxB5P,OAAA,CAAC/E,KAAK;oBAACmX,SAAS,EAAC,KAAK;oBAACxB,UAAU,EAAC,QAAQ;oBAACnB,OAAO,EAAE,CAAE;oBAAAG,QAAA,gBACpD5P,OAAA,CAACtC,SAAS;sBAACsS,KAAK,EAAC;oBAAS;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAC7BvQ,OAAA,CAAC7G,UAAU;sBAAAyW,QAAA,EAAC;oBAAQ;sBAAAQ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5B;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA,CAAC,eACXvQ,OAAA,CAAC/F,QAAQ;kBAACiR,KAAK,EAAC,cAAc;kBAAA0E,QAAA,eAC5B5P,OAAA,CAAC/E,KAAK;oBAACmX,SAAS,EAAC,KAAK;oBAACxB,UAAU,EAAC,QAAQ;oBAACnB,OAAO,EAAE,CAAE;oBAAAG,QAAA,gBACpD5P,OAAA,CAACd,SAAS;sBAAC8Q,KAAK,EAAC;oBAAO;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAC3BvQ,OAAA,CAAC7G,UAAU;sBAAAyW,QAAA,EAAC;oBAAY;sBAAAQ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA,CAAC,eACXvQ,OAAA,CAAC/F,QAAQ;kBAACiR,KAAK,EAAC,eAAe;kBAAA0E,QAAA,eAC7B5P,OAAA,CAAC/E,KAAK;oBAACmX,SAAS,EAAC,KAAK;oBAACxB,UAAU,EAAC,QAAQ;oBAACnB,OAAO,EAAE,CAAE;oBAAAG,QAAA,gBACpD5P,OAAA,CAACpC,WAAW;sBAACoS,KAAK,EAAC;oBAAS;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAC/BvQ,OAAA,CAAC7G,UAAU;sBAAAyW,QAAA,EAAC;oBAAa;sBAAAQ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eAEPvQ,OAAA,CAAC1G,IAAI;YAAC+V,IAAI;YAACQ,EAAE,EAAE,EAAG;YAAAD,QAAA,eAChB5P,OAAA,CAACnG,SAAS;cACRgX,SAAS;cACToB,KAAK,EAAC,MAAM;cACZ6C,SAAS;cACThH,IAAI,EAAE,CAAE;cACR5C,KAAK,EAAEhH,QAAQ,CAACS,IAAK;cACrBoM,QAAQ,EAAGC,CAAC,IAAKhG,gBAAgB,CAAC,MAAM,EAAEgG,CAAC,CAACC,MAAM,CAAC/F,KAAK,CAAE;cAC1D4F,WAAW,EAAC;YAAkF;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/F;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC,eAChBvQ,OAAA,CAACpG,aAAa;QAAAgW,QAAA,gBACZ5P,OAAA,CAAC5G,MAAM;UAACkY,OAAO,EAAEvG,WAAY;UAAA6E,QAAA,EAAC;QAAO;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAC9CvQ,OAAA,CAAC5G,MAAM;UACLkY,OAAO,EAAElG,0BAA2B;UACpCoF,OAAO,EAAC,WAAW;UACnBoB,QAAQ,EAAElR,OAAO,IAAI,CAACwD,QAAQ,CAACE,OAAO,IAAI,CAACF,QAAQ,CAACG,YAAY,IAAI,CAACH,QAAQ,CAACI,YAAY,IAAI,CAACJ,QAAQ,CAACO,iBAAkB;UAC1H+M,SAAS,EAAE9Q,OAAO,gBAAGV,OAAA,CAAC7F,gBAAgB;YAACoX,IAAI,EAAE;UAAG;YAAAnB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAAGvQ,OAAA,CAAC5C,QAAQ;YAAAgT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAAX,QAAA,EAElE3M,UAAU,KAAK,QAAQ,GAAG,qBAAqB,GAAG;QAAiB;UAAAmN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9D,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAEb,CAAC;;EAED;EACA,MAAMwE,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAI9R,UAAU,KAAK,MAAM,IAAI,CAACE,YAAY,EAAE,OAAO,IAAI;IAEvD,oBACEnD,OAAA,CAACvG,MAAM;MAACkK,IAAI,EAAEZ,UAAW;MAAC8Q,OAAO,EAAE9I,WAAY;MAAC+I,QAAQ,EAAC,IAAI;MAACjD,SAAS;MAAAjB,QAAA,gBACrE5P,OAAA,CAACtG,WAAW;QAAAkW,QAAA,GAAC,4BACe,EAACzM,YAAY,CAAC8F,kBAAkB;MAAA;QAAAmH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/C,CAAC,eACdvQ,OAAA,CAACrG,aAAa;QAAAiW,QAAA,eACZ5P,OAAA,CAAC1G,IAAI;UAACkW,SAAS;UAACC,OAAO,EAAE,CAAE;UAACC,EAAE,EAAE;YAAEuD,EAAE,EAAE;UAAE,CAAE;UAAArD,QAAA,gBACxC5P,OAAA,CAAC1G,IAAI;YAAC+V,IAAI;YAACQ,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAF,QAAA,eACvB5P,OAAA,CAACzG,IAAI;cAACiX,OAAO,EAAC,UAAU;cAAAZ,QAAA,eACtB5P,OAAA,CAACxG,WAAW;gBAAAoW,QAAA,gBACV5P,OAAA,CAAC7G,UAAU;kBAACqX,OAAO,EAAC,IAAI;kBAACwE,YAAY;kBAAApF,QAAA,EAAC;gBAEtC;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACbvQ,OAAA,CAAC7G,UAAU;kBAACqX,OAAO,EAAC,OAAO;kBAACR,KAAK,EAAC,gBAAgB;kBAAAJ,QAAA,GAAC,WACxC,eAAA5P,OAAA;oBAAA4P,QAAA,EAASzM,YAAY,CAACiB;kBAAO;oBAAAgM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAS,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtC,CAAC,eACbvQ,OAAA,CAAC7G,UAAU;kBAACqX,OAAO,EAAC,OAAO;kBAACR,KAAK,EAAC,gBAAgB;kBAAAJ,QAAA,GAAC,sBAC7B,eAAA5P,OAAA;oBAAA4P,QAAA,GAASzM,YAAY,CAACoB,kBAAkB,EAAC,IAAE;kBAAA;oBAAA6L,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9D,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEPvQ,OAAA,CAAC1G,IAAI;YAAC+V,IAAI;YAACQ,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAF,QAAA,eACvB5P,OAAA,CAACzG,IAAI;cAACiX,OAAO,EAAC,UAAU;cAAAZ,QAAA,eACtB5P,OAAA,CAACxG,WAAW;gBAAAoW,QAAA,gBACV5P,OAAA,CAAC7G,UAAU;kBAACqX,OAAO,EAAC,IAAI;kBAACwE,YAAY;kBAAApF,QAAA,EAAC;gBAEtC;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACbvQ,OAAA,CAAC7G,UAAU;kBAACqX,OAAO,EAAC,OAAO;kBAACR,KAAK,EAAC,gBAAgB;kBAAAJ,QAAA,GAAC,UACzC,eAAA5P,OAAA;oBAAA4P,QAAA,EAASzM,YAAY,CAAC8F;kBAAkB;oBAAAmH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAS,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChD,CAAC,eACbvQ,OAAA,CAAC7G,UAAU;kBAACqX,OAAO,EAAC,OAAO;kBAACR,KAAK,EAAC,gBAAgB;kBAAAJ,QAAA,GAAC,QAC3C,eAAA5P,OAAA;oBAAA4P,QAAA,EAAS,IAAIjJ,IAAI,CAACxD,YAAY,CAAC4D,mBAAmB,CAAC,CAACgH,kBAAkB,CAAC;kBAAC;oBAAAqC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAS,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9E,CAAC,eACbvQ,OAAA,CAAC7G,UAAU;kBAACqX,OAAO,EAAC,OAAO;kBAACR,KAAK,EAAC,gBAAgB;kBAAAJ,QAAA,GAAC,aACtC,eAAA5P,OAAA;oBAAA4P,QAAA,EAASzM,YAAY,CAACnB,SAAS,IAAImB,YAAY,CAACkB;kBAAY;oBAAA+L,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAS,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEPvQ,OAAA,CAAC1G,IAAI;YAAC+V,IAAI;YAACQ,EAAE,EAAE,EAAG;YAAAD,QAAA,eAChB5P,OAAA,CAACzG,IAAI;cAACiX,OAAO,EAAC,UAAU;cAAAZ,QAAA,eACtB5P,OAAA,CAACxG,WAAW;gBAAAoW,QAAA,gBACV5P,OAAA,CAAC7G,UAAU;kBAACqX,OAAO,EAAC,IAAI;kBAACwE,YAAY;kBAAApF,QAAA,EAAC;gBAEtC;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACbvQ,OAAA,CAAC1G,IAAI;kBAACkW,SAAS;kBAACC,OAAO,EAAE,CAAE;kBAAAG,QAAA,gBACzB5P,OAAA,CAAC1G,IAAI;oBAAC+V,IAAI;oBAACQ,EAAE,EAAE,CAAE;oBAAAD,QAAA,gBACf5P,OAAA,CAAC7G,UAAU;sBAACqX,OAAO,EAAC,OAAO;sBAACR,KAAK,EAAC,gBAAgB;sBAAAJ,QAAA,EAAC;oBAEnD;sBAAAQ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACbvQ,OAAA,CAAC9E,IAAI;sBACHqW,IAAI,EAAC,OAAO;sBACZU,KAAK,EAAE9O,YAAY,CAACqB,iBAAkB;sBACtCwL,KAAK,EAAE7M,YAAY,CAACqB,iBAAiB,KAAK,IAAI,GAAG,SAAS,GAAG;oBAAQ;sBAAA4L,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC,eACPvQ,OAAA,CAAC1G,IAAI;oBAAC+V,IAAI;oBAACQ,EAAE,EAAE,CAAE;oBAAAD,QAAA,gBACf5P,OAAA,CAAC7G,UAAU;sBAACqX,OAAO,EAAC,OAAO;sBAACR,KAAK,EAAC,gBAAgB;sBAAAJ,QAAA,EAAC;oBAEnD;sBAAAQ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACbvQ,OAAA,CAAC9E,IAAI;sBACHqW,IAAI,EAAC,OAAO;sBACZU,KAAK,EAAE,GAAG9O,YAAY,CAACsB,iBAAiB,KAAM;sBAC9CuL,KAAK,EAAE7G,UAAU,CAAChG,YAAY,CAACsB,iBAAiB,CAAC,IAAI,GAAG,GAAG,SAAS,GAAG;oBAAU;sBAAA2L,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAClF,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC,eACPvQ,OAAA,CAAC1G,IAAI;oBAAC+V,IAAI;oBAACQ,EAAE,EAAE,CAAE;oBAAAD,QAAA,gBACf5P,OAAA,CAAC7G,UAAU;sBAACqX,OAAO,EAAC,OAAO;sBAACR,KAAK,EAAC,gBAAgB;sBAAAJ,QAAA,EAAC;oBAEnD;sBAAAQ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACbvQ,OAAA,CAAC9E,IAAI;sBACHqW,IAAI,EAAC,OAAO;sBACZU,KAAK,EAAE9O,YAAY,CAACuB,iBAAkB;sBACtCsL,KAAK,EAAE7M,YAAY,CAACuB,iBAAiB,KAAK,IAAI,GAAG,SAAS,GAAG;oBAAQ;sBAAA0L,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EAENpN,YAAY,CAACwB,IAAI,iBAChB3E,OAAA,CAAC1G,IAAI;YAAC+V,IAAI;YAACQ,EAAE,EAAE,EAAG;YAAAD,QAAA,eAChB5P,OAAA,CAACzG,IAAI;cAACiX,OAAO,EAAC,UAAU;cAAAZ,QAAA,eACtB5P,OAAA,CAACxG,WAAW;gBAAAoW,QAAA,gBACV5P,OAAA,CAAC7G,UAAU;kBAACqX,OAAO,EAAC,IAAI;kBAACwE,YAAY;kBAAApF,QAAA,EAAC;gBAEtC;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACbvQ,OAAA,CAAC7G,UAAU;kBAACqX,OAAO,EAAC,OAAO;kBAAAZ,QAAA,EACxBzM,YAAY,CAACwB;gBAAI;kBAAAyL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACP;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC,eAChBvQ,OAAA,CAACpG,aAAa;QAAAgW,QAAA,gBACZ5P,OAAA,CAAC5G,MAAM;UAACkY,OAAO,EAAEvG,WAAY;UAAA6E,QAAA,EAAC;QAAM;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAC7CvQ,OAAA,CAAC5G,MAAM;UACLkY,OAAO,EAAEA,CAAA,KAAM1F,iBAAiB,CAACzI,YAAY,CAAE;UAC/CqN,OAAO,EAAC,WAAW;UACnBgB,SAAS,eAAExR,OAAA,CAACtD,OAAO;YAAA0T,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACvBqB,QAAQ,EAAElR,OAAQ;UAAAkP,QAAA,EACnB;QAED;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAEb,CAAC;;EAED;EACA,MAAM0E,WAAW,GAAGA,CAAA,KAAM;IACxB,MAAMC,SAAS,GAAGlU,IAAI,CAACuF,MAAM;IAC7B,MAAM4O,cAAc,GAAGnU,IAAI,CAAC6F,MAAM,CAACyE,CAAC,IAAIA,CAAC,CAAC/C,mBAAmB,KAAK,YAAY,CAAC,CAAChC,MAAM;IACtF,MAAMnB,eAAe,GAAGtE,cAAc,CAACyF,MAAM;IAC7C,MAAM6O,yBAAyB,GAAGF,SAAS,GAAG,CAAC,GAAG1O,IAAI,CAACC,KAAK,CAAErB,eAAe,GAAG+P,cAAc,GAAI,GAAG,CAAC,GAAG,CAAC;IAE1G,oBACEnV,OAAA,CAAC1G,IAAI;MAACkW,SAAS;MAACC,OAAO,EAAE,CAAE;MAACC,EAAE,EAAE;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAC,QAAA,gBACxC5P,OAAA,CAAC1G,IAAI;QAAC+V,IAAI;QAACQ,EAAE,EAAE,EAAG;QAACwF,EAAE,EAAE,CAAE;QAACvF,EAAE,EAAE,CAAE;QAAAF,QAAA,eAC9B5P,OAAA,CAACzG,IAAI;UAAAqW,QAAA,eACH5P,OAAA,CAACxG,WAAW;YAAAoW,QAAA,gBACV5P,OAAA,CAAC7G,UAAU;cAAC6W,KAAK,EAAC,gBAAgB;cAACgF,YAAY;cAAApF,QAAA,EAAC;YAEhD;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbvQ,OAAA,CAAC7G,UAAU;cAACqX,OAAO,EAAC,IAAI;cAAAZ,QAAA,EACrBsF;YAAS;cAAA9E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACPvQ,OAAA,CAAC1G,IAAI;QAAC+V,IAAI;QAACQ,EAAE,EAAE,EAAG;QAACwF,EAAE,EAAE,CAAE;QAACvF,EAAE,EAAE,CAAE;QAAAF,QAAA,eAC9B5P,OAAA,CAACzG,IAAI;UAAAqW,QAAA,eACH5P,OAAA,CAACxG,WAAW;YAAAoW,QAAA,gBACV5P,OAAA,CAAC7G,UAAU;cAAC6W,KAAK,EAAC,gBAAgB;cAACgF,YAAY;cAAApF,QAAA,EAAC;YAEhD;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbvQ,OAAA,CAAC7G,UAAU;cAACqX,OAAO,EAAC,IAAI;cAAAZ,QAAA,EACrBuF;YAAc;cAAA/E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACPvQ,OAAA,CAAC1G,IAAI;QAAC+V,IAAI;QAACQ,EAAE,EAAE,EAAG;QAACwF,EAAE,EAAE,CAAE;QAACvF,EAAE,EAAE,CAAE;QAAAF,QAAA,eAC9B5P,OAAA,CAACzG,IAAI;UAAAqW,QAAA,eACH5P,OAAA,CAACxG,WAAW;YAAAoW,QAAA,gBACV5P,OAAA,CAAC7G,UAAU;cAAC6W,KAAK,EAAC,gBAAgB;cAACgF,YAAY;cAAApF,QAAA,EAAC;YAEhD;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbvQ,OAAA,CAAC7G,UAAU;cAACqX,OAAO,EAAC,IAAI;cAAAZ,QAAA,EACrBxK;YAAe;cAAAgL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACPvQ,OAAA,CAAC1G,IAAI;QAAC+V,IAAI;QAACQ,EAAE,EAAE,EAAG;QAACwF,EAAE,EAAE,CAAE;QAACvF,EAAE,EAAE,CAAE;QAAAF,QAAA,eAC9B5P,OAAA,CAACzG,IAAI;UAAAqW,QAAA,eACH5P,OAAA,CAACxG,WAAW;YAAAoW,QAAA,gBACV5P,OAAA,CAAC7G,UAAU;cAAC6W,KAAK,EAAC,gBAAgB;cAACgF,YAAY;cAAApF,QAAA,EAAC;YAEhD;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbvQ,OAAA,CAAC7G,UAAU;cAACqX,OAAO,EAAC,IAAI;cAACR,KAAK,EAAEoF,yBAAyB,IAAI,EAAE,GAAG,cAAc,GAAG,cAAe;cAAAxF,QAAA,GAC/FwF,yBAAyB,EAAC,GAC7B;YAAA;cAAAhF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEX,CAAC;EAED,oBACEvQ,OAAA,CAAClE,SAAS;IAACgY,QAAQ,EAAC,IAAI;IAACpE,EAAE,EAAE;MAAEQ,EAAE,EAAE;IAAE,CAAE;IAAAN,QAAA,gBAErC5P,OAAA,CAAC9G,GAAG;MAACwW,EAAE,EAAE;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAC,QAAA,gBACjB5P,OAAA,CAAC7G,UAAU;QAACqX,OAAO,EAAC,IAAI;QAACgC,SAAS,EAAC,IAAI;QAACwC,YAAY;QAACvE,UAAU,EAAC,MAAM;QAAAb,QAAA,EAAC;MAEvE;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACbvQ,OAAA,CAAC7G,UAAU;QAACqX,OAAO,EAAC,OAAO;QAACR,KAAK,EAAC,gBAAgB;QAACN,EAAE,EAAE;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAC,QAAA,EAAC;MAElE;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAGbvQ,OAAA,CAAC3G,KAAK;QAACqW,EAAE,EAAE;UAAEiB,CAAC,EAAE,CAAC;UAAE2E,OAAO,EAAE,YAAY;UAAEtF,KAAK,EAAE,mBAAmB;UAAEL,EAAE,EAAE;QAAE,CAAE;QAAAC,QAAA,eAC5E5P,OAAA,CAAC/E,KAAK;UAACmX,SAAS,EAAC,KAAK;UAACxB,UAAU,EAAC,QAAQ;UAACnB,OAAO,EAAE,CAAE;UAAAG,QAAA,gBACpD5P,OAAA,CAAChB,QAAQ;YAAAoR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACZvQ,OAAA,CAAC9G,GAAG;YAAA0W,QAAA,gBACF5P,OAAA,CAAC7G,UAAU;cAACqX,OAAO,EAAC,OAAO;cAACC,UAAU,EAAC,MAAM;cAAAb,QAAA,EAAC;YAE9C;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbvQ,OAAA,CAAC7G,UAAU;cAACqX,OAAO,EAAC,SAAS;cAAAZ,QAAA,GAAC,SAC1B,eAAA5P,OAAA;gBAAA4P,QAAA,EAAQ;cAA0B;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,8EAC3C,eAAAvQ,OAAA;gBAAA4P,QAAA,EAAQ;cAAe;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,mGAChC,eAAAvQ,OAAA;gBAAA4P,QAAA,EAAQ;cAAgC;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,kGACjD,eAAAvQ,OAAA;gBAAA4P,QAAA,EAAQ;cAAO;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,kFAC5B;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,EAGLhB,eAAe,CAAC,CAAC,EAGjB,CAAC7O,OAAO,IAAIsD,mBAAmB,kBAC9BhE,OAAA,CAAC9G,GAAG;MAACwW,EAAE,EAAE;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAC,QAAA,gBACjB5P,OAAA,CAAC3E,cAAc;QAAA+U,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,EACjBzM,QAAQ,GAAG,CAAC,iBACX9D,OAAA,CAAC7G,UAAU;QAACqX,OAAO,EAAC,SAAS;QAACR,KAAK,EAAC,gBAAgB;QAACN,EAAE,EAAE;UAAEuD,EAAE,EAAE;QAAE,CAAE;QAAArD,QAAA,GAAC,iBACnD,EAAC9L,QAAQ,EAAC,GAC3B;MAAA;QAAAsM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CACb;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CACN,eAGDvQ,OAAA,CAAC3G,KAAK;MAACqW,EAAE,EAAE;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAC,QAAA,eACnB5P,OAAA,CAACpF,IAAI;QACHsQ,KAAK,EAAEtK,SAAU;QACjBmQ,QAAQ,EAAEvG,eAAgB;QAC1B+K,cAAc,EAAC,SAAS;QACxBC,SAAS,EAAC,SAAS;QACnBhF,OAAO,EAAC,WAAW;QAAAZ,QAAA,gBAEnB5P,OAAA,CAACnF,GAAG;UACFoX,KAAK,eACHjS,OAAA,CAAC/E,KAAK;YAACmX,SAAS,EAAC,KAAK;YAACxB,UAAU,EAAC,QAAQ;YAACnB,OAAO,EAAE,CAAE;YAAAG,QAAA,gBACpD5P,OAAA,CAACR,SAAS;cAAA4Q,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACbvQ,OAAA,CAAC9G,GAAG;cAAA0W,QAAA,gBACF5P,OAAA,CAAC7G,UAAU;gBAACqX,OAAO,EAAC,OAAO;gBAACC,UAAU,EAAC,MAAM;gBAAAb,QAAA,EAAC;cAE9C;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACbvQ,OAAA,CAAC7G,UAAU;gBAACqX,OAAO,EAAC,SAAS;gBAACR,KAAK,EAAC,gBAAgB;gBAAAJ,QAAA,GACjDtO,YAAY,CAACiF,MAAM,EAAC,cACvB;cAAA;gBAAA6J,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,EACLtL,UAAU,CAACI,kBAAkB,GAAG,CAAC,iBAChCrF,OAAA,CAAC5E,KAAK;cAACqa,YAAY,EAAExQ,UAAU,CAACI,kBAAmB;cAAC2K,KAAK,EAAC,SAAS;cAAAJ,QAAA,eACjE5P,OAAA,CAACpC,WAAW;gBAAAwS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CACR;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI;QACR;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACFvQ,OAAA,CAACnF,GAAG;UACFoX,KAAK,eACHjS,OAAA,CAAC/E,KAAK;YAACmX,SAAS,EAAC,KAAK;YAACxB,UAAU,EAAC,QAAQ;YAACnB,OAAO,EAAE,CAAE;YAAAG,QAAA,gBACpD5P,OAAA,CAACN,WAAW;cAAA0Q,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACfvQ,OAAA,CAAC9G,GAAG;cAAA0W,QAAA,gBACF5P,OAAA,CAAC7G,UAAU;gBAACqX,OAAO,EAAC,OAAO;gBAACC,UAAU,EAAC,MAAM;gBAAAb,QAAA,EAAC;cAE9C;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACbvQ,OAAA,CAAC7G,UAAU;gBAACqX,OAAO,EAAC,SAAS;gBAACR,KAAK,EAAC,gBAAgB;gBAAAJ,QAAA,GACjDpO,sBAAsB,CAAC+E,MAAM,EAAC,iBACjC;cAAA;gBAAA6J,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,EACLtL,UAAU,CAACM,kBAAkB,GAAG,CAAC,iBAChCvF,OAAA,CAAC5E,KAAK;cAACqa,YAAY,EAAExQ,UAAU,CAACM,kBAAmB;cAACyK,KAAK,EAAC,SAAS;cAAAJ,QAAA,eACjE5P,OAAA,CAACtC,SAAS;gBAAA0S,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CACR;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI;QACR;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,EAGPG,sBAAsB,CAAC,CAAC,EAGxB,CAAChQ,OAAO,IAAIE,SAAS,KAAK,CAAC,IAAI0R,eAAe,CAAC,CAAC,EAChD,CAAC5R,OAAO,IAAIE,SAAS,KAAK,CAAC,IAAIwS,yBAAyB,CAAC,CAAC,EAG1DQ,0BAA0B,CAAC,CAAC,EAC5BmB,gBAAgB,CAAC,CAAC,eAGnB/U,OAAA,CAACrE,QAAQ;MACPgI,IAAI,EAAEF,QAAQ,CAACE,IAAK;MACpB+R,gBAAgB,EAAE,IAAK;MACvB7B,OAAO,EAAEpM,aAAc;MACvBkO,YAAY,EAAE;QAAEC,QAAQ,EAAE,QAAQ;QAAEC,UAAU,EAAE;MAAQ,CAAE;MAAAjG,QAAA,eAE1D5P,OAAA,CAAC5F,KAAK;QAACyZ,OAAO,EAAEpM,aAAc;QAAC5D,QAAQ,EAAEJ,QAAQ,CAACI,QAAS;QAAC6L,EAAE,EAAE;UAAEiF,KAAK,EAAE;QAAO,CAAE;QAAA/E,QAAA,EAC/EnM,QAAQ,CAACG;MAAO;QAAAwM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACZ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eAGXvQ,OAAA,CAAChE,SAAS;MACR8Z,SAAS,EAAC,eAAe;MACzBpG,EAAE,EAAE;QAAE0B,QAAQ,EAAE,OAAO;QAAE2E,MAAM,EAAE,EAAE;QAAEC,KAAK,EAAE;MAAG,CAAE;MACjDjD,IAAI,eAAE/S,OAAA,CAAC9D,aAAa;QAAAkU,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAE;MAAAX,QAAA,gBAExB5P,OAAA,CAAC/D,eAAe;QACd8W,IAAI,eAAE/S,OAAA,CAAC5D,OAAO;UAAAgU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAClB0F,YAAY,EAAC,sBAAsB;QACnC3E,OAAO,EAAE3G;MAAiB;QAAAyF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3B,CAAC,eACFvQ,OAAA,CAAC/D,eAAe;QACd8W,IAAI,eAAE/S,OAAA,CAAClC,UAAU;UAAAsS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACrB0F,YAAY,EAAC,eAAe;QAC5B3E,OAAO,EAAE9C;MAAgB;QAAA4B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1B,CAAC,eACFvQ,OAAA,CAAC/D,eAAe;QACd8W,IAAI,eAAE/S,OAAA,CAACtB,WAAW;UAAA0R,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACtB0F,YAAY,EAAC,eAAe;QAC5B3E,OAAO,EAAE7L;MAAgB;QAAA2K,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1B,CAAC,eACFvQ,OAAA,CAAC/D,eAAe;QACd8W,IAAI,eAAE/S,OAAA,CAAC1B,UAAU;UAAA8R,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACrB0F,YAAY,EAAC,iBAAiB;QAC9B3E,OAAO,EAAEA,CAAA,KAAMrL,YAAY,CAAC,0BAA0B,EAAE,MAAM;MAAE;QAAAmK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACO,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEhB,CAAC,kCAAC;AAAC2F,GAAA,GA98DG/V,0BAA0B;AAg9DhC,eAAeA,0BAA0B;AAAC,IAAAE,EAAA,EAAA6V,GAAA;AAAAC,YAAA,CAAA9V,EAAA;AAAA8V,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}