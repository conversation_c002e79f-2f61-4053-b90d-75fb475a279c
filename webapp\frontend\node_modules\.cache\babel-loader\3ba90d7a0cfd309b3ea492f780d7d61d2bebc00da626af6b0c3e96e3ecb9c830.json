{"ast": null, "code": "var _jsxFileName = \"C:\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\cavi\\\\CertificazioneCaviImproved.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, forwardRef, useImperativeHandle } from 'react';\nimport { Box, Typography, Button, Paper, Grid, Card, CardContent, Dialog, DialogTitle, DialogContent, DialogActions, TextField, FormControl, InputLabel, Select, MenuItem, Autocomplete, CircularProgress, Alert, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, IconButton, Tabs, Tab, Pagination, InputAdornment, Divider, Stack, Chip, Tooltip, Badge, LinearProgress, Collapse, List, ListItem, ListItemText, ListItemIcon, Snackbar, AppBar, Toolbar, Container, Fab, SpeedDial, SpeedDialAction, SpeedDialIcon } from '@mui/material';\nimport { Add as AddIcon, Search as SearchIcon, FilterList as FilterIcon, PictureAsPdf as PdfIcon, Download as DownloadIcon, Visibility as ViewIcon, Delete as DeleteIcon, Edit as EditIcon, Save as SaveIcon, Clear as ClearIcon, Build as BuildIcon, CheckCircle as CheckIcon, Warning as WarningIcon, GetApp as ExportIcon, Print as PrintIcon, Email as EmailIcon, CloudUpload as UploadIcon, Assessment as ReportIcon, Settings as SettingsIcon, Refresh as RefreshIcon, ExpandMore as ExpandMoreIcon, ExpandLess as ExpandLessIcon, Info as InfoIcon, Error as ErrorIcon, Schedule as ScheduleIcon, Person as PersonIcon, Cable as CableIcon, Science as ScienceIcon, Block as BlockIcon } from '@mui/icons-material';\nimport certificazioneService from '../../services/certificazioneService';\nimport caviService from '../../services/caviService';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst CertificazioneCaviImproved = /*#__PURE__*/_s(/*#__PURE__*/forwardRef(_c = _s(({\n  cantiereId,\n  onSuccess,\n  onError\n}, ref) => {\n  _s();\n  // Stati principali\n  const [loading, setLoading] = useState(false);\n  const [activeTab, setActiveTab] = useState(0);\n  const [certificazioni, setCertificazioni] = useState([]);\n  const [cavi, setCavi] = useState([]);\n  const [strumenti, setStrumenti] = useState([]);\n\n  // Stati per ricerca e filtri avanzati\n  const [searchTerm, setSearchTerm] = useState('');\n  const [filteredCavi, setFilteredCavi] = useState([]);\n  const [filteredCertificazioni, setFilteredCertificazioni] = useState([]);\n  const [advancedFiltersOpen, setAdvancedFiltersOpen] = useState(false);\n  const [filters, setFilters] = useState({\n    stato: '',\n    tipologia: '',\n    operatore: '',\n    dataInizio: '',\n    dataFine: '',\n    valoreIsolamento: '',\n    risultatoTest: '',\n    strumento: '',\n    certificazione: ''\n  });\n\n  // Stati per paginazione e ordinamento\n  const [currentPage, setCurrentPage] = useState(1);\n  const [itemsPerPage, setItemsPerPage] = useState(10);\n  const [sortBy, setSortBy] = useState('data_certificazione');\n  const [sortOrder, setSortOrder] = useState('desc');\n\n  // Stati per dialogs e modali\n  const [openDialog, setOpenDialog] = useState(false);\n  const [dialogType, setDialogType] = useState('');\n  const [selectedItem, setSelectedItem] = useState(null);\n  const [bulkSelection, setBulkSelection] = useState([]);\n  const [bulkMode, setBulkMode] = useState(false);\n\n  // Stati per notifiche e feedback\n  const [snackbar, setSnackbar] = useState({\n    open: false,\n    message: '',\n    severity: 'info'\n  });\n  const [progress, setProgress] = useState(0);\n  const [operationInProgress, setOperationInProgress] = useState(false);\n\n  // Stati per form certificazione avanzato\n  const [formData, setFormData] = useState({\n    id_cavo: '',\n    id_operatore: '',\n    id_strumento: '',\n    lunghezza_misurata: '',\n    valore_continuita: 'OK',\n    valore_isolamento: '',\n    valore_resistenza: 'OK',\n    note: '',\n    temperatura_ambiente: '',\n    umidita: '',\n    tensione_prova: '',\n    durata_prova: '',\n    risultato_finale: 'CONFORME'\n  });\n\n  // Stati per statistiche e dashboard\n  const [statistics, setStatistics] = useState({\n    totaleCavi: 0,\n    caviCertificati: 0,\n    caviNonCertificati: 0,\n    percentualeCompletamento: 0,\n    certificazioniOggi: 0,\n    certificazioniSettimana: 0\n  });\n\n  // Carica dati iniziali\n  useEffect(() => {\n    loadInitialData();\n  }, [cantiereId]);\n\n  // Filtra cavi in base alla ricerca\n  useEffect(() => {\n    filterCavi();\n  }, [cavi, searchTerm, filters, sortBy, sortOrder]);\n\n  // Filtra certificazioni\n  useEffect(() => {\n    filterCertificazioni();\n  }, [certificazioni, searchTerm, filters, sortBy, sortOrder]);\n\n  // Ricalcola statistiche quando cambiano i dati\n  useEffect(() => {\n    calculateStatistics();\n  }, [cavi, certificazioni]);\n  const loadInitialData = async () => {\n    try {\n      setLoading(true);\n      setProgress(0);\n\n      // Carica dati in sequenza con progress\n      setProgress(25);\n      await loadCavi();\n      setProgress(50);\n      await loadCertificazioni();\n      setProgress(75);\n      await loadStrumenti();\n      setProgress(100);\n      calculateStatistics();\n    } catch (error) {\n      showSnackbar('Errore nel caricamento dei dati iniziali', 'error');\n      onError('Errore nel caricamento dei dati iniziali');\n    } finally {\n      setLoading(false);\n      setProgress(0);\n    }\n  };\n  const loadCertificazioni = async () => {\n    try {\n      const data = await certificazioneService.getCertificazioni(cantiereId);\n      setCertificazioni(data);\n      return data;\n    } catch (error) {\n      console.error('Errore nel caricamento delle certificazioni:', error);\n      throw error;\n    }\n  };\n  const loadCavi = async () => {\n    try {\n      const data = await caviService.getCavi(cantiereId);\n      setCavi(data);\n      return data;\n    } catch (error) {\n      console.error('Errore nel caricamento dei cavi:', error);\n      throw error;\n    }\n  };\n  const loadStrumenti = async () => {\n    try {\n      const data = await certificazioneService.getStrumenti(cantiereId);\n      setStrumenti(data);\n      return data;\n    } catch (error) {\n      console.error('Errore nel caricamento degli strumenti:', error);\n      throw error;\n    }\n  };\n\n  // Calcola statistiche avanzate\n  const calculateStatistics = () => {\n    const totaleCavi = cavi.length;\n    const caviCertificati = certificazioni.length;\n    const caviNonCertificati = totaleCavi - caviCertificati;\n    const percentualeCompletamento = totaleCavi > 0 ? Math.round(caviCertificati / totaleCavi * 100) : 0;\n\n    // Calcola certificazioni di oggi\n    const oggi = new Date().toDateString();\n    const certificazioniOggi = certificazioni.filter(cert => new Date(cert.data_certificazione).toDateString() === oggi).length;\n\n    // Calcola certificazioni della settimana\n    const unaSettimanaFa = new Date();\n    unaSettimanaFa.setDate(unaSettimanaFa.getDate() - 7);\n    const certificazioniSettimana = certificazioni.filter(cert => new Date(cert.data_certificazione) >= unaSettimanaFa).length;\n    setStatistics({\n      totaleCavi,\n      caviCertificati,\n      caviNonCertificati,\n      percentualeCompletamento,\n      certificazioniOggi,\n      certificazioniSettimana\n    });\n  };\n\n  // Gestione snackbar\n  const showSnackbar = (message, severity = 'info') => {\n    setSnackbar({\n      open: true,\n      message,\n      severity\n    });\n  };\n  const closeSnackbar = () => {\n    setSnackbar({\n      ...snackbar,\n      open: false\n    });\n  };\n  const filterCavi = () => {\n    let filtered = cavi;\n\n    // Filtro per ricerca testuale avanzata\n    if (searchTerm) {\n      const searchLower = searchTerm.toLowerCase();\n      filtered = filtered.filter(cavo => {\n        var _cavo$tipologia, _cavo$ubicazione_part, _cavo$ubicazione_arri, _cavo$sezione, _cavo$utility;\n        return cavo.id_cavo.toLowerCase().includes(searchLower) || ((_cavo$tipologia = cavo.tipologia) === null || _cavo$tipologia === void 0 ? void 0 : _cavo$tipologia.toLowerCase().includes(searchLower)) || ((_cavo$ubicazione_part = cavo.ubicazione_partenza) === null || _cavo$ubicazione_part === void 0 ? void 0 : _cavo$ubicazione_part.toLowerCase().includes(searchLower)) || ((_cavo$ubicazione_arri = cavo.ubicazione_arrivo) === null || _cavo$ubicazione_arri === void 0 ? void 0 : _cavo$ubicazione_arri.toLowerCase().includes(searchLower)) || ((_cavo$sezione = cavo.sezione) === null || _cavo$sezione === void 0 ? void 0 : _cavo$sezione.toLowerCase().includes(searchLower)) || ((_cavo$utility = cavo.utility) === null || _cavo$utility === void 0 ? void 0 : _cavo$utility.toLowerCase().includes(searchLower));\n      });\n    }\n\n    // Filtri specifici avanzati\n    if (filters.stato) {\n      filtered = filtered.filter(cavo => cavo.stato_installazione === filters.stato);\n    }\n    if (filters.tipologia) {\n      filtered = filtered.filter(cavo => cavo.tipologia === filters.tipologia);\n    }\n\n    // Filtro per stato certificazione\n    if (filters.certificazione) {\n      if (filters.certificazione === 'CERTIFICATO') {\n        filtered = filtered.filter(cavo => certificazioni.some(cert => cert.id_cavo === cavo.id_cavo));\n      } else if (filters.certificazione === 'NON_CERTIFICATO') {\n        filtered = filtered.filter(cavo => !certificazioni.some(cert => cert.id_cavo === cavo.id_cavo));\n      }\n    }\n\n    // Ordinamento\n    filtered.sort((a, b) => {\n      let aValue = a[sortBy];\n      let bValue = b[sortBy];\n      if (typeof aValue === 'string') {\n        aValue = aValue.toLowerCase();\n        bValue = bValue.toLowerCase();\n      }\n      if (sortOrder === 'asc') {\n        return aValue > bValue ? 1 : -1;\n      } else {\n        return aValue < bValue ? 1 : -1;\n      }\n    });\n    setFilteredCavi(filtered);\n  };\n  const filterCertificazioni = () => {\n    let filtered = certificazioni;\n\n    // Ricerca testuale avanzata\n    if (searchTerm) {\n      const searchLower = searchTerm.toLowerCase();\n      filtered = filtered.filter(cert => {\n        var _cert$operatore, _cert$numero_certific, _cert$note;\n        return cert.id_cavo.toLowerCase().includes(searchLower) || ((_cert$operatore = cert.operatore) === null || _cert$operatore === void 0 ? void 0 : _cert$operatore.toLowerCase().includes(searchLower)) || ((_cert$numero_certific = cert.numero_certificato) === null || _cert$numero_certific === void 0 ? void 0 : _cert$numero_certific.toLowerCase().includes(searchLower)) || ((_cert$note = cert.note) === null || _cert$note === void 0 ? void 0 : _cert$note.toLowerCase().includes(searchLower));\n      });\n    }\n\n    // Filtri avanzati\n    if (filters.operatore) {\n      filtered = filtered.filter(cert => cert.operatore === filters.operatore);\n    }\n    if (filters.strumento) {\n      filtered = filtered.filter(cert => cert.strumento === filters.strumento);\n    }\n    if (filters.risultatoTest) {\n      filtered = filtered.filter(cert => cert.risultato_finale === filters.risultatoTest);\n    }\n    if (filters.dataInizio) {\n      filtered = filtered.filter(cert => new Date(cert.data_certificazione) >= new Date(filters.dataInizio));\n    }\n    if (filters.dataFine) {\n      filtered = filtered.filter(cert => new Date(cert.data_certificazione) <= new Date(filters.dataFine));\n    }\n    if (filters.valoreIsolamento) {\n      const valore = parseFloat(filters.valoreIsolamento);\n      filtered = filtered.filter(cert => parseFloat(cert.valore_isolamento) >= valore);\n    }\n\n    // Ordinamento\n    filtered.sort((a, b) => {\n      let aValue = a[sortBy];\n      let bValue = b[sortBy];\n      if (sortBy === 'data_certificazione') {\n        aValue = new Date(aValue);\n        bValue = new Date(bValue);\n      } else if (typeof aValue === 'string') {\n        aValue = aValue.toLowerCase();\n        bValue = bValue.toLowerCase();\n      }\n      if (sortOrder === 'asc') {\n        return aValue > bValue ? 1 : -1;\n      } else {\n        return aValue < bValue ? 1 : -1;\n      }\n    });\n    setFilteredCertificazioni(filtered);\n  };\n\n  // Gestione selezione multipla - SOLO per certificazioni\n  const toggleBulkMode = () => {\n    if (activeTab !== 1) {\n      showSnackbar('La selezione multipla è disponibile solo per le certificazioni', 'warning');\n      return;\n    }\n    setBulkMode(!bulkMode);\n    setBulkSelection([]);\n    showSnackbar(!bulkMode ? 'Modalità selezione attivata - Clicca sulle certificazioni per selezionarle' : 'Modalità selezione disattivata', 'info');\n  };\n  const toggleItemSelection = itemId => {\n    setBulkSelection(prev => {\n      const newSelection = prev.includes(itemId) ? prev.filter(id => id !== itemId) : [...prev, itemId];\n      showSnackbar(`${newSelection.length} certificazioni selezionate`, 'info');\n      return newSelection;\n    });\n  };\n  const selectAllItems = () => {\n    if (activeTab !== 1) return;\n    const allIds = filteredCertificazioni.map(cert => cert.id_certificazione);\n    setBulkSelection(allIds);\n    showSnackbar(`Tutte le ${allIds.length} certificazioni selezionate`, 'success');\n  };\n  const clearSelection = () => {\n    setBulkSelection([]);\n    showSnackbar('Selezione cancellata', 'info');\n  };\n\n  // Funzione per verificare se un cavo è certificato\n  const isCavoCertificato = idCavo => {\n    return certificazioni.some(cert => cert.id_cavo === idCavo);\n  };\n\n  // Funzione per verificare se un cavo può essere certificato\n  const puoEssereCertificato = cavo => {\n    // Verifica che il cavo sia installato\n    const isInstallato = cavo.stato_installazione === 'Installato' || cavo.stato_installazione === 'INSTALLATO' || cavo.stato_installazione === 'POSATO';\n\n    // Verifica che il cavo sia completamente collegato (collegamenti = 3)\n    // 3 = 1 (partenza) + 2 (arrivo) = completamente collegato\n    const isCollegato = cavo.collegamenti === 3;\n\n    // Verifica che abbia responsabili per entrambi i lati\n    const hasResponsabili = cavo.responsabile_partenza && cavo.responsabile_arrivo;\n    return isInstallato && isCollegato && hasResponsabili;\n  };\n\n  // Funzione per ottenere il messaggio di errore per cavi non certificabili\n  const getMessaggioErroreCertificazione = cavo => {\n    const isInstallato = cavo.stato_installazione === 'Installato' || cavo.stato_installazione === 'INSTALLATO' || cavo.stato_installazione === 'POSATO';\n    const isCollegato = cavo.collegamenti === 3;\n    const hasResponsabili = cavo.responsabile_partenza && cavo.responsabile_arrivo;\n    if (!isInstallato) {\n      return 'Il cavo deve essere installato prima di poter essere certificato';\n    }\n    if (!isCollegato) {\n      const statoCollegamento = cavo.collegamenti === 0 ? 'non collegato' : cavo.collegamenti === 1 ? 'collegato solo lato partenza' : cavo.collegamenti === 2 ? 'collegato solo lato arrivo' : 'stato collegamento sconosciuto';\n      return `Il cavo deve essere collegato da entrambi i lati (attualmente: ${statoCollegamento})`;\n    }\n    if (!hasResponsabili) {\n      const mancanti = [];\n      if (!cavo.responsabile_partenza) mancanti.push('partenza');\n      if (!cavo.responsabile_arrivo) mancanti.push('arrivo');\n      return `Mancano i responsabili per: ${mancanti.join(', ')}`;\n    }\n    return 'Cavo non certificabile per motivi sconosciuti';\n  };\n\n  // Gestione tabs\n  const handleTabChange = (event, newValue) => {\n    setActiveTab(newValue);\n    setCurrentPage(1);\n    setSearchTerm('');\n    setFilters({\n      stato: '',\n      tipologia: '',\n      operatore: ''\n    });\n  };\n\n  // Gestione dialogs\n  const openCreateDialog = (cavoPreselezionato = null) => {\n    setDialogType('create');\n    setSelectedItem(null);\n\n    // Se viene passato un cavo, precompila il form\n    if (cavoPreselezionato) {\n      setFormData({\n        id_cavo: cavoPreselezionato.id_cavo,\n        id_operatore: '',\n        id_strumento: '',\n        lunghezza_misurata: cavoPreselezionato.metratura_reale || cavoPreselezionato.metri_teorici || '',\n        valore_continuita: 'OK',\n        valore_isolamento: '',\n        valore_resistenza: 'OK',\n        note: '',\n        temperatura_ambiente: '',\n        umidita: '',\n        tensione_prova: '',\n        durata_prova: '',\n        risultato_finale: 'CONFORME'\n      });\n      showSnackbar(`Cavo ${cavoPreselezionato.id_cavo} selezionato automaticamente`, 'success');\n    } else {\n      // Reset form per nuova certificazione generica\n      setFormData({\n        id_cavo: '',\n        id_operatore: '',\n        id_strumento: '',\n        lunghezza_misurata: '',\n        valore_continuita: 'OK',\n        valore_isolamento: '',\n        valore_resistenza: 'OK',\n        note: '',\n        temperatura_ambiente: '',\n        umidita: '',\n        tensione_prova: '',\n        durata_prova: '',\n        risultato_finale: 'CONFORME'\n      });\n    }\n    setOpenDialog(true);\n  };\n  const closeDialog = () => {\n    setOpenDialog(false);\n    setSelectedItem(null);\n    setDialogType('');\n  };\n\n  // Gestione form\n  const handleFormChange = (field, value) => {\n    setFormData(prev => ({\n      ...prev,\n      [field]: value\n    }));\n  };\n  const handleCavoSelect = cavo => {\n    setFormData(prev => ({\n      ...prev,\n      id_cavo: cavo.id_cavo,\n      lunghezza_misurata: cavo.metratura_reale || cavo.metri_teorici || ''\n    }));\n  };\n\n  // Operazioni CRUD avanzate\n  const handleCreateCertificazione = async () => {\n    try {\n      if (!formData.id_cavo || !formData.id_operatore || !formData.id_strumento || !formData.valore_isolamento) {\n        showSnackbar('Compila tutti i campi obbligatori', 'warning');\n        return;\n      }\n\n      // Verifica che il cavo possa essere certificato\n      const cavo = cavi.find(c => c.id_cavo === formData.id_cavo);\n      if (!cavo) {\n        showSnackbar('Cavo non trovato', 'error');\n        return;\n      }\n      if (!puoEssereCertificato(cavo)) {\n        const messaggio = getMessaggioErroreCertificazione(cavo);\n        showSnackbar(`Impossibile certificare il cavo: ${messaggio}`, 'error');\n        return;\n      }\n\n      // Verifica che il cavo non sia già certificato\n      if (isCavoCertificato(formData.id_cavo)) {\n        showSnackbar('Il cavo è già stato certificato', 'warning');\n        return;\n      }\n      setOperationInProgress(true);\n      await certificazioneService.createCertificazione(cantiereId, formData);\n      showSnackbar('Certificazione creata con successo', 'success');\n      closeDialog();\n      await loadCertificazioni();\n      calculateStatistics();\n    } catch (error) {\n      showSnackbar('Errore nella creazione della certificazione: ' + (error.message || 'Errore sconosciuto'), 'error');\n    } finally {\n      setOperationInProgress(false);\n    }\n  };\n  const handleGeneratePdf = async certificazione => {\n    try {\n      setOperationInProgress(true);\n      showSnackbar('Generazione PDF in corso...', 'info');\n      const response = await certificazioneService.generatePdf(cantiereId, certificazione.id_certificazione);\n      if (response.file_url) {\n        // Apri il PDF in una nuova finestra\n        const newWindow = window.open(response.file_url, '_blank');\n        if (newWindow) {\n          showSnackbar('PDF generato e aperto in una nuova finestra', 'success');\n        } else {\n          // Se il popup è bloccato, offri il download diretto\n          const link = document.createElement('a');\n          link.href = response.file_url;\n          link.download = `certificazione_${certificazione.numero_certificato}.pdf`;\n          document.body.appendChild(link);\n          link.click();\n          document.body.removeChild(link);\n          showSnackbar('PDF scaricato nella cartella Download', 'success');\n        }\n      } else if (response.pdf_content) {\n        // Se il PDF viene restituito come contenuto base64\n        const blob = new Blob([atob(response.pdf_content)], {\n          type: 'application/pdf'\n        });\n        const url = URL.createObjectURL(blob);\n        const link = document.createElement('a');\n        link.href = url;\n        link.download = `certificazione_${certificazione.numero_certificato}.pdf`;\n        document.body.appendChild(link);\n        link.click();\n        document.body.removeChild(link);\n        URL.revokeObjectURL(url);\n        showSnackbar('PDF scaricato con successo', 'success');\n      } else {\n        showSnackbar('Errore: Formato PDF non riconosciuto', 'error');\n      }\n    } catch (error) {\n      console.error('Errore generazione PDF:', error);\n      showSnackbar('Errore nella generazione del PDF: ' + (error.message || 'Errore sconosciuto'), 'error');\n    } finally {\n      setOperationInProgress(false);\n    }\n  };\n  const handleDeleteCertificazione = async certificazione => {\n    if (window.confirm(`Sei sicuro di voler eliminare la certificazione ${certificazione.numero_certificato}?`)) {\n      try {\n        setOperationInProgress(true);\n        await certificazioneService.deleteCertificazione(cantiereId, certificazione.id_certificazione);\n        showSnackbar('Certificazione eliminata con successo', 'success');\n        await loadCertificazioni();\n        calculateStatistics();\n      } catch (error) {\n        showSnackbar('Errore nell\\'eliminazione della certificazione: ' + (error.message || 'Errore sconosciuto'), 'error');\n      } finally {\n        setOperationInProgress(false);\n      }\n    }\n  };\n\n  // Operazioni bulk\n  const handleBulkDelete = async () => {\n    if (bulkSelection.length === 0) {\n      showSnackbar('Seleziona almeno un elemento', 'warning');\n      return;\n    }\n    if (window.confirm(`Sei sicuro di voler eliminare ${bulkSelection.length} certificazioni?`)) {\n      try {\n        setOperationInProgress(true);\n        for (const id of bulkSelection) {\n          await certificazioneService.deleteCertificazione(cantiereId, id);\n        }\n        showSnackbar(`${bulkSelection.length} certificazioni eliminate con successo`, 'success');\n        setBulkSelection([]);\n        await loadCertificazioni();\n        calculateStatistics();\n      } catch (error) {\n        showSnackbar('Errore nell\\'eliminazione delle certificazioni', 'error');\n      } finally {\n        setOperationInProgress(false);\n      }\n    }\n  };\n  const handleBulkExport = async () => {\n    if (bulkSelection.length === 0) {\n      showSnackbar('Seleziona almeno un elemento', 'warning');\n      return;\n    }\n    try {\n      setOperationInProgress(true);\n      // Implementa export bulk\n      const selectedCerts = certificazioni.filter(cert => bulkSelection.includes(cert.id_certificazione));\n\n      // Crea CSV\n      const csvContent = generateCSV(selectedCerts);\n      downloadCSV(csvContent, `certificazioni_${new Date().toISOString().split('T')[0]}.csv`);\n      showSnackbar(`${bulkSelection.length} certificazioni esportate`, 'success');\n    } catch (error) {\n      showSnackbar('Errore nell\\'esportazione', 'error');\n    } finally {\n      setOperationInProgress(false);\n    }\n  };\n\n  // Funzioni di export\n  const generateCSV = data => {\n    const headers = ['ID Cavo', 'Numero Certificato', 'Data', 'Operatore', 'Strumento', 'Lunghezza', 'Isolamento', 'Risultato'];\n    const rows = data.map(cert => [cert.id_cavo, cert.numero_certificato, new Date(cert.data_certificazione).toLocaleDateString(), cert.operatore, cert.strumento, cert.lunghezza_misurata, cert.valore_isolamento, cert.risultato_finale]);\n    return [headers, ...rows].map(row => row.join(',')).join('\\n');\n  };\n  const downloadCSV = (content, filename) => {\n    const blob = new Blob([content], {\n      type: 'text/csv;charset=utf-8;'\n    });\n    const link = document.createElement('a');\n    if (link.download !== undefined) {\n      const url = URL.createObjectURL(blob);\n      link.setAttribute('href', url);\n      link.setAttribute('download', filename);\n      link.style.visibility = 'hidden';\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n    }\n  };\n  const handleExportAll = () => {\n    const csvContent = generateCSV(filteredCertificazioni);\n    downloadCSV(csvContent, `tutte_certificazioni_${new Date().toISOString().split('T')[0]}.csv`);\n    showSnackbar('Esportazione completata', 'success');\n  };\n\n  // Espone metodi tramite ref\n  useImperativeHandle(ref, () => ({\n    handleOptionSelect: option => {\n      if (option === 'creaCertificazione') {\n        openCreateDialog();\n      } else if (option === 'visualizzaCertificazioni') {\n        setActiveTab(1);\n      }\n    }\n  }));\n\n  // Calcola elementi per paginazione\n  const getCurrentPageItems = items => {\n    const startIndex = (currentPage - 1) * itemsPerPage;\n    const endIndex = startIndex + itemsPerPage;\n    return items.slice(startIndex, endIndex);\n  };\n  const getTotalPages = items => Math.ceil(items.length / itemsPerPage);\n\n  // Ottieni opzioni uniche per filtri\n  const getUniqueValues = (array, field) => {\n    return [...new Set(array.map(item => item[field]).filter(Boolean))];\n  };\n\n  // Componente Dashboard con statistiche\n  const renderDashboard = () => /*#__PURE__*/_jsxDEV(Grid, {\n    container: true,\n    spacing: 3,\n    sx: {\n      mb: 3\n    },\n    children: [/*#__PURE__*/_jsxDEV(Grid, {\n      item: true,\n      xs: 12,\n      md: 2,\n      children: /*#__PURE__*/_jsxDEV(Card, {\n        sx: {\n          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n          color: 'white'\n        },\n        children: /*#__PURE__*/_jsxDEV(CardContent, {\n          sx: {\n            textAlign: 'center',\n            py: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(CableIcon, {\n            sx: {\n              fontSize: 40,\n              mb: 1\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 784,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h4\",\n            fontWeight: \"bold\",\n            children: statistics.totaleCavi\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 785,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            children: \"Totale Cavi\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 788,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 783,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 782,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 781,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      item: true,\n      xs: 12,\n      md: 2,\n      children: /*#__PURE__*/_jsxDEV(Card, {\n        sx: {\n          background: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',\n          color: 'white'\n        },\n        children: /*#__PURE__*/_jsxDEV(CardContent, {\n          sx: {\n            textAlign: 'center',\n            py: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(CheckIcon, {\n            sx: {\n              fontSize: 40,\n              mb: 1\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 798,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h4\",\n            fontWeight: \"bold\",\n            children: statistics.caviCertificati\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 799,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            children: \"Certificati\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 802,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 797,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 796,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 795,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      item: true,\n      xs: 12,\n      md: 2,\n      children: /*#__PURE__*/_jsxDEV(Card, {\n        sx: {\n          background: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',\n          color: 'white'\n        },\n        children: /*#__PURE__*/_jsxDEV(CardContent, {\n          sx: {\n            textAlign: 'center',\n            py: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(WarningIcon, {\n            sx: {\n              fontSize: 40,\n              mb: 1\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 812,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h4\",\n            fontWeight: \"bold\",\n            children: statistics.caviNonCertificati\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 813,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            children: \"Da Certificare\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 816,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 811,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 810,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 809,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      item: true,\n      xs: 12,\n      md: 2,\n      children: /*#__PURE__*/_jsxDEV(Card, {\n        sx: {\n          background: 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',\n          color: 'white'\n        },\n        children: /*#__PURE__*/_jsxDEV(CardContent, {\n          sx: {\n            textAlign: 'center',\n            py: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(ReportIcon, {\n            sx: {\n              fontSize: 40,\n              mb: 1\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 826,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h4\",\n            fontWeight: \"bold\",\n            children: [statistics.percentualeCompletamento, \"%\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 827,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            children: \"Completamento\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 830,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 825,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 824,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 823,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      item: true,\n      xs: 12,\n      md: 2,\n      children: /*#__PURE__*/_jsxDEV(Card, {\n        sx: {\n          background: 'linear-gradient(135deg, #fa709a 0%, #fee140 100%)',\n          color: 'white'\n        },\n        children: /*#__PURE__*/_jsxDEV(CardContent, {\n          sx: {\n            textAlign: 'center',\n            py: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(ScheduleIcon, {\n            sx: {\n              fontSize: 40,\n              mb: 1\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 840,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h4\",\n            fontWeight: \"bold\",\n            children: statistics.certificazioniOggi\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 841,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            children: \"Oggi\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 844,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 839,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 838,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 837,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      item: true,\n      xs: 12,\n      md: 2,\n      children: /*#__PURE__*/_jsxDEV(Card, {\n        sx: {\n          background: 'linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)',\n          color: '#333'\n        },\n        children: /*#__PURE__*/_jsxDEV(CardContent, {\n          sx: {\n            textAlign: 'center',\n            py: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(ScienceIcon, {\n            sx: {\n              fontSize: 40,\n              mb: 1\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 854,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h4\",\n            fontWeight: \"bold\",\n            children: statistics.certificazioniSettimana\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 855,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            children: \"Questa Settimana\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 858,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 853,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 852,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 851,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 780,\n    columnNumber: 5\n  }, this);\n\n  // Componente barra di ricerca avanzata\n  const renderSearchAndFilters = () => /*#__PURE__*/_jsxDEV(Paper, {\n    sx: {\n      p: 2,\n      mb: 3\n    },\n    children: [/*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 2,\n      alignItems: \"center\",\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 4,\n        children: /*#__PURE__*/_jsxDEV(TextField, {\n          fullWidth: true,\n          placeholder: \"Cerca cavi, certificazioni, operatori...\",\n          value: searchTerm,\n          onChange: e => setSearchTerm(e.target.value),\n          InputProps: {\n            startAdornment: /*#__PURE__*/_jsxDEV(InputAdornment, {\n              position: \"start\",\n              children: /*#__PURE__*/_jsxDEV(SearchIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 880,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 879,\n              columnNumber: 17\n            }, this),\n            endAdornment: searchTerm && /*#__PURE__*/_jsxDEV(InputAdornment, {\n              position: \"end\",\n              children: /*#__PURE__*/_jsxDEV(IconButton, {\n                onClick: () => setSearchTerm(''),\n                size: \"small\",\n                children: /*#__PURE__*/_jsxDEV(ClearIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 886,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 885,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 884,\n              columnNumber: 17\n            }, this)\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 872,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 871,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 2,\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          fullWidth: true,\n          variant: \"outlined\",\n          startIcon: /*#__PURE__*/_jsxDEV(FilterIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 898,\n            columnNumber: 24\n          }, this),\n          onClick: () => setAdvancedFiltersOpen(!advancedFiltersOpen),\n          color: Object.values(filters).some(f => f) ? 'primary' : 'inherit',\n          children: [\"Filtri \", Object.values(filters).filter(f => f).length > 0 && `(${Object.values(filters).filter(f => f).length})`]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 895,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 894,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 2,\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          fullWidth: true,\n          variant: \"outlined\",\n          startIcon: bulkMode ? /*#__PURE__*/_jsxDEV(ClearIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 910,\n            columnNumber: 35\n          }, this) : /*#__PURE__*/_jsxDEV(CheckIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 910,\n            columnNumber: 51\n          }, this),\n          onClick: toggleBulkMode,\n          color: bulkMode ? 'secondary' : 'inherit',\n          disabled: activeTab === 0,\n          children: bulkMode ? 'Esci Selezione' : 'Selezione Multipla'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 907,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 906,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 2,\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          fullWidth: true,\n          variant: \"outlined\",\n          startIcon: /*#__PURE__*/_jsxDEV(ExportIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 923,\n            columnNumber: 24\n          }, this),\n          onClick: handleExportAll,\n          disabled: activeTab === 0 || filteredCertificazioni.length === 0,\n          children: activeTab === 0 ? 'Export (solo certificazioni)' : 'Esporta Certificazioni'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 920,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 919,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 2,\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          fullWidth: true,\n          variant: \"contained\",\n          startIcon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 935,\n            columnNumber: 24\n          }, this),\n          onClick: openCreateDialog,\n          children: \"Nuova Certificazione\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 932,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 931,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 870,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Collapse, {\n      in: advancedFiltersOpen,\n      children: [/*#__PURE__*/_jsxDEV(Divider, {\n        sx: {\n          my: 2\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 945,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body2\",\n        color: \"text.secondary\",\n        sx: {\n          mb: 2\n        },\n        children: activeTab === 0 ? 'Filtri per Cavi' : 'Filtri per Certificazioni'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 946,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 2,\n        children: [activeTab === 0 && /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(FormControl, {\n              fullWidth: true,\n              size: \"small\",\n              children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                children: \"Stato Installazione\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 956,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Select, {\n                value: filters.stato,\n                onChange: e => setFilters({\n                  ...filters,\n                  stato: e.target.value\n                }),\n                children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"\",\n                  children: \"Tutti\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 961,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"INSTALLATO\",\n                  children: \"Installato\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 962,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"NON_INSTALLATO\",\n                  children: \"Non Installato\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 963,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"IN_CORSO\",\n                  children: \"In Corso\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 964,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 957,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 955,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 954,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(FormControl, {\n              fullWidth: true,\n              size: \"small\",\n              children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                children: \"Tipologia\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 971,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Select, {\n                value: filters.tipologia,\n                onChange: e => setFilters({\n                  ...filters,\n                  tipologia: e.target.value\n                }),\n                children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"\",\n                  children: \"Tutte\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 976,\n                  columnNumber: 21\n                }, this), [...new Set(cavi.map(c => c.tipologia))].filter(Boolean).map(tip => /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: tip,\n                  children: tip\n                }, tip, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 978,\n                  columnNumber: 23\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 972,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 970,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 969,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(FormControl, {\n              fullWidth: true,\n              size: \"small\",\n              children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                children: \"Stato Certificazione\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 986,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Select, {\n                value: filters.certificazione,\n                onChange: e => setFilters({\n                  ...filters,\n                  certificazione: e.target.value\n                }),\n                children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"\",\n                  children: \"Tutti\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 991,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"CERTIFICATO\",\n                  children: \"Certificato\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 992,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"NON_CERTIFICATO\",\n                  children: \"Non Certificato\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 993,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 987,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 985,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 984,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true), activeTab === 1 && /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(FormControl, {\n              fullWidth: true,\n              size: \"small\",\n              children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                children: \"Operatore\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1005,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Select, {\n                value: filters.operatore,\n                onChange: e => setFilters({\n                  ...filters,\n                  operatore: e.target.value\n                }),\n                children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"\",\n                  children: \"Tutti\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1010,\n                  columnNumber: 21\n                }, this), [...new Set(certificazioni.map(c => c.operatore))].filter(Boolean).map(op => /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: op,\n                  children: op\n                }, op, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1012,\n                  columnNumber: 23\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1006,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1004,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1003,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(FormControl, {\n              fullWidth: true,\n              size: \"small\",\n              children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                children: \"Risultato Test\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1020,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Select, {\n                value: filters.risultatoTest,\n                onChange: e => setFilters({\n                  ...filters,\n                  risultatoTest: e.target.value\n                }),\n                children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"\",\n                  children: \"Tutti\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1025,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"CONFORME\",\n                  children: \"Conforme\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1026,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"NON_CONFORME\",\n                  children: \"Non Conforme\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1027,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"DA_VERIFICARE\",\n                  children: \"Da Verificare\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1028,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1021,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1019,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1018,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              size: \"small\",\n              label: \"Isolamento Min (M\\u03A9)\",\n              type: \"number\",\n              value: filters.valoreIsolamento,\n              onChange: e => setFilters({\n                ...filters,\n                valoreIsolamento: e.target.value\n              }),\n              placeholder: \"es. 500\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1034,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1033,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              size: \"small\",\n              label: \"Data Inizio\",\n              type: \"date\",\n              value: filters.dataInizio,\n              onChange: e => setFilters({\n                ...filters,\n                dataInizio: e.target.value\n              }),\n              InputLabelProps: {\n                shrink: true\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1046,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1045,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              size: \"small\",\n              label: \"Data Fine\",\n              type: \"date\",\n              value: filters.dataFine,\n              onChange: e => setFilters({\n                ...filters,\n                dataFine: e.target.value\n              }),\n              InputLabelProps: {\n                shrink: true\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1058,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1057,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          children: /*#__PURE__*/_jsxDEV(Stack, {\n            direction: \"row\",\n            spacing: 1,\n            justifyContent: \"flex-end\",\n            children: /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"outlined\",\n              size: \"small\",\n              onClick: () => setFilters({\n                stato: '',\n                tipologia: '',\n                operatore: '',\n                dataInizio: '',\n                dataFine: '',\n                valoreIsolamento: '',\n                risultatoTest: '',\n                strumento: '',\n                certificazione: ''\n              }),\n              children: \"Pulisci Tutti i Filtri\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1073,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1072,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1071,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 950,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 944,\n      columnNumber: 7\n    }, this), bulkMode && bulkSelection.length > 0 && /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(Divider, {\n        sx: {\n          my: 2\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1092,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Stack, {\n        direction: \"row\",\n        spacing: 2,\n        alignItems: \"center\",\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          children: [bulkSelection.length, \" elementi selezionati\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1094,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          size: \"small\",\n          variant: \"outlined\",\n          onClick: selectAllItems,\n          children: \"Seleziona Tutto\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1097,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          size: \"small\",\n          variant: \"outlined\",\n          onClick: clearSelection,\n          children: \"Deseleziona\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1104,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          size: \"small\",\n          variant: \"outlined\",\n          startIcon: /*#__PURE__*/_jsxDEV(ExportIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1114,\n            columnNumber: 26\n          }, this),\n          onClick: handleBulkExport,\n          children: \"Esporta Selezionati\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1111,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          size: \"small\",\n          variant: \"outlined\",\n          color: \"error\",\n          startIcon: /*#__PURE__*/_jsxDEV(DeleteIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1123,\n            columnNumber: 26\n          }, this),\n          onClick: handleBulkDelete,\n          children: \"Elimina Selezionati\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1119,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1093,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 869,\n    columnNumber: 5\n  }, this);\n\n  // Renderizza la tabella dei cavi\n  const renderCaviTable = () => {\n    const currentItems = getCurrentPageItems(filteredCavi);\n    if (filteredCavi.length === 0) {\n      return /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"info\",\n        children: searchTerm || filters.stato || filters.tipologia ? 'Nessun cavo trovato con i filtri applicati' : 'Nessun cavo disponibile'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1140,\n        columnNumber: 9\n      }, this);\n    }\n    return /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(TableContainer, {\n        component: Paper,\n        children: /*#__PURE__*/_jsxDEV(Table, {\n          size: \"small\",\n          children: [/*#__PURE__*/_jsxDEV(TableHead, {\n            children: /*#__PURE__*/_jsxDEV(TableRow, {\n              children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"ID Cavo\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1154,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Tipologia\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1155,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Sezione\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1156,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Partenza\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1157,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Arrivo\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1158,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Metri\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1159,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Stato\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1160,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Certificato\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1161,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Azioni\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1162,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1153,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1152,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n            children: currentItems.map(cavo => {\n              const isCertificato = isCavoCertificato(cavo.id_cavo);\n              const puoCertificare = puoEssereCertificato(cavo);\n              const messaggioErrore = !puoCertificare ? getMessaggioErroreCertificazione(cavo) : '';\n              return /*#__PURE__*/_jsxDEV(TableRow, {\n                children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                  children: /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    fontWeight: \"medium\",\n                    children: cavo.id_cavo\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1174,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1173,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: cavo.tipologia\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1178,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: cavo.sezione\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1179,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: cavo.ubicazione_partenza\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1180,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: cavo.ubicazione_arrivo\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1181,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: [cavo.metratura_reale || cavo.metri_teorici, \" m\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1182,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: /*#__PURE__*/_jsxDEV(Chip, {\n                    size: \"small\",\n                    label: cavo.stato_installazione,\n                    color: cavo.stato_installazione === 'INSTALLATO' ? 'success' : 'default'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1184,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1183,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: isCertificato ? /*#__PURE__*/_jsxDEV(Chip, {\n                    size: \"small\",\n                    icon: /*#__PURE__*/_jsxDEV(CheckIcon, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1194,\n                      columnNumber: 33\n                    }, this),\n                    label: \"Certificato\",\n                    color: \"success\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1192,\n                    columnNumber: 25\n                  }, this) : /*#__PURE__*/_jsxDEV(Chip, {\n                    size: \"small\",\n                    icon: /*#__PURE__*/_jsxDEV(WarningIcon, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1201,\n                      columnNumber: 33\n                    }, this),\n                    label: \"Non certificato\",\n                    color: \"warning\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1199,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1190,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: isCertificato ? /*#__PURE__*/_jsxDEV(Tooltip, {\n                    title: \"Cavo gi\\xE0 certificato\",\n                    children: /*#__PURE__*/_jsxDEV(Chip, {\n                      icon: /*#__PURE__*/_jsxDEV(CheckIcon, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1211,\n                        columnNumber: 35\n                      }, this),\n                      label: \"Certificato\",\n                      color: \"success\",\n                      size: \"small\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1210,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1209,\n                    columnNumber: 25\n                  }, this) : puoCertificare ? /*#__PURE__*/_jsxDEV(Tooltip, {\n                    title: \"Crea certificazione per questo cavo\",\n                    children: /*#__PURE__*/_jsxDEV(IconButton, {\n                      size: \"small\",\n                      onClick: () => openCreateDialog(cavo),\n                      color: \"primary\",\n                      children: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1224,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1219,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1218,\n                    columnNumber: 25\n                  }, this) : /*#__PURE__*/_jsxDEV(Tooltip, {\n                    title: messaggioErrore,\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: /*#__PURE__*/_jsxDEV(IconButton, {\n                        size: \"small\",\n                        disabled: true,\n                        onClick: () => showSnackbar(messaggioErrore, 'warning'),\n                        children: /*#__PURE__*/_jsxDEV(BlockIcon, {}, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1235,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1230,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1229,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1228,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1207,\n                  columnNumber: 21\n                }, this)]\n              }, cavo.id_cavo, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1172,\n                columnNumber: 19\n              }, this);\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1165,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1151,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1150,\n        columnNumber: 9\n      }, this), getTotalPages(filteredCavi) > 1 && /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          justifyContent: 'center',\n          mt: 2\n        },\n        children: /*#__PURE__*/_jsxDEV(Pagination, {\n          count: getTotalPages(filteredCavi),\n          page: currentPage,\n          onChange: (event, value) => setCurrentPage(value),\n          color: \"primary\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1250,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1249,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true);\n  };\n\n  // Renderizza la tabella delle certificazioni\n  const renderCertificazioniTable = () => {\n    const currentItems = getCurrentPageItems(filteredCertificazioni);\n    if (filteredCertificazioni.length === 0) {\n      return /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"info\",\n        children: searchTerm || filters.operatore ? 'Nessuna certificazione trovata con i filtri applicati' : 'Nessuna certificazione disponibile'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1268,\n        columnNumber: 9\n      }, this);\n    }\n    return /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(TableContainer, {\n        component: Paper,\n        children: /*#__PURE__*/_jsxDEV(Table, {\n          size: \"small\",\n          children: [/*#__PURE__*/_jsxDEV(TableHead, {\n            children: /*#__PURE__*/_jsxDEV(TableRow, {\n              children: [bulkMode && /*#__PURE__*/_jsxDEV(TableCell, {\n                padding: \"checkbox\",\n                children: /*#__PURE__*/_jsxDEV(IconButton, {\n                  size: \"small\",\n                  onClick: bulkSelection.length === filteredCertificazioni.length ? clearSelection : selectAllItems,\n                  children: bulkSelection.length === filteredCertificazioni.length ? /*#__PURE__*/_jsxDEV(ClearIcon, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1288,\n                    columnNumber: 81\n                  }, this) : /*#__PURE__*/_jsxDEV(CheckIcon, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1288,\n                    columnNumber: 97\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1284,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1283,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: /*#__PURE__*/_jsxDEV(Stack, {\n                  direction: \"row\",\n                  alignItems: \"center\",\n                  spacing: 1,\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    fontWeight: \"bold\",\n                    children: \"N\\xB0 Certificato\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1294,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n                    size: \"small\",\n                    onClick: () => {\n                      setSortBy('numero_certificato');\n                      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');\n                    },\n                    children: sortBy === 'numero_certificato' ? sortOrder === 'asc' ? /*#__PURE__*/_jsxDEV(ExpandLessIcon, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1299,\n                      columnNumber: 81\n                    }, this) : /*#__PURE__*/_jsxDEV(ExpandMoreIcon, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1299,\n                      columnNumber: 102\n                    }, this) : /*#__PURE__*/_jsxDEV(ExpandMoreIcon, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1299,\n                      columnNumber: 124\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1295,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1293,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1292,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"ID Cavo\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1303,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: /*#__PURE__*/_jsxDEV(Stack, {\n                  direction: \"row\",\n                  alignItems: \"center\",\n                  spacing: 1,\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    fontWeight: \"bold\",\n                    children: \"Data\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1306,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n                    size: \"small\",\n                    onClick: () => {\n                      setSortBy('data_certificazione');\n                      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');\n                    },\n                    children: sortBy === 'data_certificazione' ? sortOrder === 'asc' ? /*#__PURE__*/_jsxDEV(ExpandLessIcon, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1311,\n                      columnNumber: 82\n                    }, this) : /*#__PURE__*/_jsxDEV(ExpandMoreIcon, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1311,\n                      columnNumber: 103\n                    }, this) : /*#__PURE__*/_jsxDEV(ExpandMoreIcon, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1311,\n                      columnNumber: 125\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1307,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1305,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1304,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Operatore\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1315,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Strumento\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1316,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Lunghezza\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1317,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Isolamento\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1318,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Risultato\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1319,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Azioni\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1320,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1281,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1280,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n            children: currentItems.map(cert => /*#__PURE__*/_jsxDEV(TableRow, {\n              selected: bulkSelection.includes(cert.id_certificazione),\n              hover: true,\n              children: [bulkMode && /*#__PURE__*/_jsxDEV(TableCell, {\n                padding: \"checkbox\",\n                children: /*#__PURE__*/_jsxDEV(IconButton, {\n                  size: \"small\",\n                  onClick: () => toggleItemSelection(cert.id_certificazione),\n                  color: bulkSelection.includes(cert.id_certificazione) ? 'primary' : 'default',\n                  children: bulkSelection.includes(cert.id_certificazione) ? /*#__PURE__*/_jsxDEV(CheckIcon, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1337,\n                    columnNumber: 75\n                  }, this) : /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1337,\n                    columnNumber: 91\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1332,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1331,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  fontWeight: \"medium\",\n                  children: cert.numero_certificato\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1342,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1341,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: /*#__PURE__*/_jsxDEV(Chip, {\n                  size: \"small\",\n                  label: cert.id_cavo,\n                  variant: \"outlined\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1347,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1346,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: new Date(cert.data_certificazione).toLocaleDateString()\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1349,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: /*#__PURE__*/_jsxDEV(Stack, {\n                  direction: \"row\",\n                  alignItems: \"center\",\n                  spacing: 1,\n                  children: [/*#__PURE__*/_jsxDEV(PersonIcon, {\n                    fontSize: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1352,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    children: cert.operatore || cert.id_operatore\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1353,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1351,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1350,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  children: cert.id_strumento ? (() => {\n                    const strumento = strumenti.find(s => s.id_strumento === cert.id_strumento);\n                    return strumento ? `${strumento.nome} - ${strumento.marca}` : 'Strumento non trovato';\n                  })() : cert.strumento_utilizzato || 'N/A'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1357,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1356,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  children: [cert.lunghezza_misurata, \" m\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1368,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1367,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: /*#__PURE__*/_jsxDEV(Chip, {\n                  size: \"small\",\n                  label: `${cert.valore_isolamento} MΩ`,\n                  color: parseFloat(cert.valore_isolamento) >= 500 ? 'success' : 'warning',\n                  icon: parseFloat(cert.valore_isolamento) >= 500 ? /*#__PURE__*/_jsxDEV(CheckIcon, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1375,\n                    columnNumber: 73\n                  }, this) : /*#__PURE__*/_jsxDEV(WarningIcon, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1375,\n                    columnNumber: 89\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1371,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1370,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: /*#__PURE__*/_jsxDEV(Chip, {\n                  size: \"small\",\n                  label: cert.risultato_finale || 'CONFORME',\n                  color: cert.risultato_finale === 'CONFORME' ? 'success' : cert.risultato_finale === 'NON_CONFORME' ? 'error' : 'warning'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1379,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1378,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: /*#__PURE__*/_jsxDEV(Stack, {\n                  direction: \"row\",\n                  spacing: 0.5,\n                  children: [/*#__PURE__*/_jsxDEV(Tooltip, {\n                    title: \"Visualizza dettagli\",\n                    children: /*#__PURE__*/_jsxDEV(IconButton, {\n                      size: \"small\",\n                      onClick: () => {\n                        setSelectedItem(cert);\n                        setDialogType('view');\n                        setOpenDialog(true);\n                      },\n                      children: /*#__PURE__*/_jsxDEV(ViewIcon, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1396,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1388,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1387,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                    title: \"Genera PDF\",\n                    children: /*#__PURE__*/_jsxDEV(IconButton, {\n                      size: \"small\",\n                      onClick: () => handleGeneratePdf(cert),\n                      disabled: operationInProgress,\n                      children: /*#__PURE__*/_jsxDEV(PdfIcon, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1405,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1400,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1399,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                    title: \"Elimina\",\n                    children: /*#__PURE__*/_jsxDEV(IconButton, {\n                      size: \"small\",\n                      color: \"error\",\n                      onClick: () => handleDeleteCertificazione(cert),\n                      disabled: operationInProgress,\n                      children: /*#__PURE__*/_jsxDEV(DeleteIcon, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1415,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1409,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1408,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1386,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1385,\n                columnNumber: 19\n              }, this)]\n            }, cert.id_certificazione, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1325,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1323,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1279,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1278,\n        columnNumber: 9\n      }, this), getTotalPages(filteredCertificazioni) > 1 && /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          justifyContent: 'center',\n          mt: 2\n        },\n        children: /*#__PURE__*/_jsxDEV(Pagination, {\n          count: getTotalPages(filteredCertificazioni),\n          page: currentPage,\n          onChange: (event, value) => setCurrentPage(value),\n          color: \"primary\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1428,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1427,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true);\n  };\n\n  // Renderizza il dialog per creare/modificare certificazione\n  const renderCertificazioneDialog = () => {\n    if (dialogType !== 'create' && dialogType !== 'edit') return null;\n    return /*#__PURE__*/_jsxDEV(Dialog, {\n      open: openDialog,\n      onClose: closeDialog,\n      maxWidth: \"md\",\n      fullWidth: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: dialogType === 'create' ? 'Nuova Certificazione' : 'Modifica Certificazione'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1446,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 2,\n          sx: {\n            mt: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(Autocomplete, {\n              options: cavi.filter(cavo => !certificazioni.some(cert => cert.id_cavo === cavo.id_cavo) || cavo.id_cavo === formData.id_cavo),\n              getOptionLabel: option => `${option.id_cavo} - ${option.tipologia}`,\n              value: cavi.find(c => c.id_cavo === formData.id_cavo) || null,\n              onChange: (event, newValue) => {\n                if (newValue) {\n                  handleCavoSelect(newValue);\n                } else {\n                  setFormData(prev => ({\n                    ...prev,\n                    id_cavo: '',\n                    lunghezza_misurata: ''\n                  }));\n                }\n              },\n              renderInput: params => /*#__PURE__*/_jsxDEV(TextField, {\n                ...params,\n                label: \"Cavo *\",\n                placeholder: \"Seleziona un cavo\",\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1467,\n                columnNumber: 19\n              }, this),\n              renderOption: (props, option) => /*#__PURE__*/_jsxDEV(Box, {\n                component: \"li\",\n                ...props,\n                children: /*#__PURE__*/_jsxDEV(Box, {\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    fontWeight: \"medium\",\n                    children: option.id_cavo\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1477,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"caption\",\n                    color: \"text.secondary\",\n                    children: [option.tipologia, \" - \", option.ubicazione_partenza, \" \\u2192 \", option.ubicazione_arrivo]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1480,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1476,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1475,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1452,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1451,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Operatore *\",\n              value: formData.id_operatore,\n              onChange: e => handleFormChange('id_operatore', e.target.value),\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1490,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1489,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(FormControl, {\n              fullWidth: true,\n              required: true,\n              children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                children: \"Strumento *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1501,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Select, {\n                value: formData.id_strumento,\n                onChange: e => handleFormChange('id_strumento', e.target.value),\n                label: \"Strumento *\",\n                children: strumenti.map(strumento => /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: strumento.id_strumento,\n                  children: [strumento.nome, \" - \", strumento.marca, \" \", strumento.modello, \" (S/N: \", strumento.numero_serie, \")\"]\n                }, strumento.id_strumento, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1508,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1502,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1500,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1499,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Lunghezza Misurata (m) *\",\n              type: \"number\",\n              value: formData.lunghezza_misurata,\n              onChange: e => handleFormChange('lunghezza_misurata', e.target.value),\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1517,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1516,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 4,\n            children: /*#__PURE__*/_jsxDEV(FormControl, {\n              fullWidth: true,\n              children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                children: \"Continuit\\xE0\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1529,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Select, {\n                value: formData.valore_continuita,\n                onChange: e => handleFormChange('valore_continuita', e.target.value),\n                label: \"Continuit\\xE0\",\n                children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"OK\",\n                  children: \"OK\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1535,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"NOK\",\n                  children: \"NOK\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1536,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1530,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1528,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1527,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 4,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Isolamento (M\\u03A9) *\",\n              type: \"number\",\n              value: formData.valore_isolamento,\n              onChange: e => handleFormChange('valore_isolamento', e.target.value),\n              required: true,\n              helperText: \"Valore minimo consigliato: 500 M\\u03A9\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1542,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1541,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 4,\n            children: /*#__PURE__*/_jsxDEV(FormControl, {\n              fullWidth: true,\n              children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                children: \"Resistenza\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1555,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Select, {\n                value: formData.valore_resistenza,\n                onChange: e => handleFormChange('valore_resistenza', e.target.value),\n                label: \"Resistenza\",\n                children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"OK\",\n                  children: \"OK\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1561,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"NOK\",\n                  children: \"NOK\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1562,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1556,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1554,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1553,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            children: /*#__PURE__*/_jsxDEV(Divider, {\n              sx: {\n                my: 2\n              },\n              children: /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                color: \"text.secondary\",\n                children: \"Parametri Ambientali e Test Avanzati\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1570,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1569,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1568,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Temperatura Ambiente (\\xB0C)\",\n              type: \"number\",\n              value: formData.temperatura_ambiente,\n              onChange: e => handleFormChange('temperatura_ambiente', e.target.value),\n              helperText: \"Temperatura durante il test\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1577,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1576,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Umidit\\xE0 (%)\",\n              type: \"number\",\n              value: formData.umidita,\n              onChange: e => handleFormChange('umidita', e.target.value),\n              helperText: \"Umidit\\xE0 relativa\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1588,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1587,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Tensione di Prova (V)\",\n              type: \"number\",\n              value: formData.tensione_prova,\n              onChange: e => handleFormChange('tensione_prova', e.target.value),\n              helperText: \"Tensione applicata per il test\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1599,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1598,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Durata Prova (min)\",\n              type: \"number\",\n              value: formData.durata_prova,\n              onChange: e => handleFormChange('durata_prova', e.target.value),\n              helperText: \"Durata del test in minuti\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1610,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1609,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(FormControl, {\n              fullWidth: true,\n              children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                children: \"Risultato Finale\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1622,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Select, {\n                value: formData.risultato_finale,\n                onChange: e => handleFormChange('risultato_finale', e.target.value),\n                label: \"Risultato Finale\",\n                children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"CONFORME\",\n                  children: /*#__PURE__*/_jsxDEV(Stack, {\n                    direction: \"row\",\n                    alignItems: \"center\",\n                    spacing: 1,\n                    children: [/*#__PURE__*/_jsxDEV(CheckIcon, {\n                      color: \"success\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1630,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      children: \"Conforme\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1631,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1629,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1628,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"NON_CONFORME\",\n                  children: /*#__PURE__*/_jsxDEV(Stack, {\n                    direction: \"row\",\n                    alignItems: \"center\",\n                    spacing: 1,\n                    children: [/*#__PURE__*/_jsxDEV(ErrorIcon, {\n                      color: \"error\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1636,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      children: \"Non Conforme\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1637,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1635,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1634,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"DA_VERIFICARE\",\n                  children: /*#__PURE__*/_jsxDEV(Stack, {\n                    direction: \"row\",\n                    alignItems: \"center\",\n                    spacing: 1,\n                    children: [/*#__PURE__*/_jsxDEV(WarningIcon, {\n                      color: \"warning\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1642,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      children: \"Da Verificare\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1643,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1641,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1640,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1623,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1621,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1620,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Note\",\n              multiline: true,\n              rows: 3,\n              value: formData.note,\n              onChange: e => handleFormChange('note', e.target.value),\n              placeholder: \"Inserisci eventuali note, osservazioni o anomalie riscontrate durante il test...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1651,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1650,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1450,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1449,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: closeDialog,\n          children: \"Annulla\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1664,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleCreateCertificazione,\n          variant: \"contained\",\n          disabled: loading || !formData.id_cavo || !formData.id_operatore || !formData.id_strumento || !formData.valore_isolamento,\n          startIcon: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n            size: 20\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1669,\n            columnNumber: 34\n          }, this) : /*#__PURE__*/_jsxDEV(SaveIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1669,\n            columnNumber: 67\n          }, this),\n          children: dialogType === 'create' ? 'Crea Certificazione' : 'Salva Modifiche'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1665,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1663,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1445,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Renderizza il dialog di visualizzazione dettagli\n  const renderViewDialog = () => {\n    if (dialogType !== 'view' || !selectedItem) return null;\n    return /*#__PURE__*/_jsxDEV(Dialog, {\n      open: openDialog,\n      onClose: closeDialog,\n      maxWidth: \"md\",\n      fullWidth: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: [\"Dettagli Certificazione - \", selectedItem.numero_certificato]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1684,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 2,\n          sx: {\n            mt: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              variant: \"outlined\",\n              children: /*#__PURE__*/_jsxDEV(CardContent, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  gutterBottom: true,\n                  children: \"Informazioni Cavo\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1692,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  children: [\"ID Cavo: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: selectedItem.id_cavo\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1696,\n                    columnNumber: 30\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1695,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  children: [\"Lunghezza Misurata: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: [selectedItem.lunghezza_misurata, \" m\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1699,\n                    columnNumber: 41\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1698,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1691,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1690,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1689,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              variant: \"outlined\",\n              children: /*#__PURE__*/_jsxDEV(CardContent, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  gutterBottom: true,\n                  children: \"Informazioni Certificazione\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1708,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  children: [\"Numero: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: selectedItem.numero_certificato\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1712,\n                    columnNumber: 29\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1711,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  children: [\"Data: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: new Date(selectedItem.data_certificazione).toLocaleDateString()\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1715,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1714,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  children: [\"Operatore: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: selectedItem.operatore || selectedItem.id_operatore\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1718,\n                    columnNumber: 32\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1717,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1707,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1706,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1705,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              variant: \"outlined\",\n              children: /*#__PURE__*/_jsxDEV(CardContent, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  gutterBottom: true,\n                  children: \"Risultati Test\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1727,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                  container: true,\n                  spacing: 2,\n                  children: [/*#__PURE__*/_jsxDEV(Grid, {\n                    item: true,\n                    xs: 4,\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      color: \"text.secondary\",\n                      children: \"Continuit\\xE0\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1732,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                      size: \"small\",\n                      label: selectedItem.valore_continuita,\n                      color: selectedItem.valore_continuita === 'OK' ? 'success' : 'error'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1735,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1731,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                    item: true,\n                    xs: 4,\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      color: \"text.secondary\",\n                      children: \"Isolamento\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1742,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                      size: \"small\",\n                      label: `${selectedItem.valore_isolamento} MΩ`,\n                      color: parseFloat(selectedItem.valore_isolamento) >= 500 ? 'success' : 'warning'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1745,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1741,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                    item: true,\n                    xs: 4,\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      color: \"text.secondary\",\n                      children: \"Resistenza\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1752,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                      size: \"small\",\n                      label: selectedItem.valore_resistenza,\n                      color: selectedItem.valore_resistenza === 'OK' ? 'success' : 'error'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1755,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1751,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1730,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1726,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1725,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1724,\n            columnNumber: 13\n          }, this), selectedItem.note && /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              variant: \"outlined\",\n              children: /*#__PURE__*/_jsxDEV(CardContent, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  gutterBottom: true,\n                  children: \"Note\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1770,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  children: selectedItem.note\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1773,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1769,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1768,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1767,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1688,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1687,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: closeDialog,\n          children: \"Chiudi\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1783,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: () => handleGeneratePdf(selectedItem),\n          variant: \"contained\",\n          startIcon: /*#__PURE__*/_jsxDEV(PdfIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1787,\n            columnNumber: 24\n          }, this),\n          disabled: loading,\n          children: \"Genera PDF\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1784,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1782,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1683,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Renderizza le statistiche\n  const renderStats = () => {\n    const totalCavi = cavi.length;\n    const caviInstallati = cavi.filter(c => c.stato_installazione === 'INSTALLATO').length;\n    const caviCertificati = certificazioni.length;\n    const percentualeCertificazione = totalCavi > 0 ? Math.round(caviCertificati / caviInstallati * 100) : 0;\n    return /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 2,\n      sx: {\n        mb: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              color: \"text.secondary\",\n              gutterBottom: true,\n              children: \"Cavi Totali\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1809,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h4\",\n              children: totalCavi\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1812,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1808,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1807,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1806,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              color: \"text.secondary\",\n              gutterBottom: true,\n              children: \"Cavi Installati\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1821,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h4\",\n              children: caviInstallati\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1824,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1820,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1819,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1818,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              color: \"text.secondary\",\n              gutterBottom: true,\n              children: \"Certificazioni\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1833,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h4\",\n              children: caviCertificati\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1836,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1832,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1831,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1830,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              color: \"text.secondary\",\n              gutterBottom: true,\n              children: \"% Certificazione\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1845,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h4\",\n              color: percentualeCertificazione >= 80 ? 'success.main' : 'warning.main',\n              children: [percentualeCertificazione, \"%\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1848,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1844,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1843,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1842,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1805,\n      columnNumber: 7\n    }, this);\n  };\n  return /*#__PURE__*/_jsxDEV(Container, {\n    maxWidth: \"xl\",\n    sx: {\n      py: 3\n    },\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mb: 4\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h4\",\n        component: \"h1\",\n        gutterBottom: true,\n        fontWeight: \"bold\",\n        children: \"\\uD83D\\uDD0C Sistema di Certificazione Cavi\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1862,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body1\",\n        color: \"text.secondary\",\n        sx: {\n          mb: 2\n        },\n        children: \"Gestione completa delle certificazioni elettriche secondo standard CEI 64-8\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1865,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 2,\n          bgcolor: 'info.light',\n          color: 'info.contrastText',\n          mb: 2\n        },\n        children: /*#__PURE__*/_jsxDEV(Stack, {\n          direction: \"row\",\n          alignItems: \"center\",\n          spacing: 2,\n          children: [/*#__PURE__*/_jsxDEV(InfoIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1872,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              fontWeight: \"bold\",\n              children: \"Come utilizzare il sistema:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1874,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              children: [\"\\u2022 \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Tab \\\"Cavi da Certificare\\\":\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1878,\n                columnNumber: 19\n              }, this), \" Visualizza tutti i cavi e clicca sul \\\"+\\\" per creare una certificazione \\u2022 \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Tab \\\"Certificazioni Completate\\\":\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1879,\n                columnNumber: 19\n              }, this), \" Gestisci le certificazioni esistenti, attiva la \\\"Selezione\\\" per operazioni multiple \\u2022 \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Filtri:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1880,\n                columnNumber: 19\n              }, this), \" Usa i filtri specifici per ogni tab per trovare rapidamente quello che cerchi\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1877,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1873,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1871,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1870,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1861,\n      columnNumber: 7\n    }, this), renderDashboard(), (loading || operationInProgress) && /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mb: 2\n      },\n      children: [/*#__PURE__*/_jsxDEV(LinearProgress, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1893,\n        columnNumber: 11\n      }, this), progress > 0 && /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"caption\",\n        color: \"text.secondary\",\n        sx: {\n          mt: 1\n        },\n        children: [\"Caricamento... \", progress, \"%\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1895,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1892,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        mb: 3\n      },\n      children: /*#__PURE__*/_jsxDEV(Tabs, {\n        value: activeTab,\n        onChange: handleTabChange,\n        indicatorColor: \"primary\",\n        textColor: \"primary\",\n        variant: \"fullWidth\",\n        children: [/*#__PURE__*/_jsxDEV(Tab, {\n          label: /*#__PURE__*/_jsxDEV(Stack, {\n            direction: \"row\",\n            alignItems: \"center\",\n            spacing: 1,\n            children: [/*#__PURE__*/_jsxDEV(CableIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1914,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                fontWeight: \"bold\",\n                children: \"Cavi da Certificare\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1916,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"caption\",\n                color: \"text.secondary\",\n                children: [filteredCavi.length, \" cavi totali\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1919,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1915,\n              columnNumber: 17\n            }, this), statistics.caviNonCertificati > 0 && /*#__PURE__*/_jsxDEV(Badge, {\n              badgeContent: statistics.caviNonCertificati,\n              color: \"warning\",\n              children: /*#__PURE__*/_jsxDEV(WarningIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1925,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1924,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1913,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1911,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Tab, {\n          label: /*#__PURE__*/_jsxDEV(Stack, {\n            direction: \"row\",\n            alignItems: \"center\",\n            spacing: 1,\n            children: [/*#__PURE__*/_jsxDEV(ScienceIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1934,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                fontWeight: \"bold\",\n                children: \"Certificazioni Completate\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1936,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"caption\",\n                color: \"text.secondary\",\n                children: [filteredCertificazioni.length, \" certificazioni\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1939,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1935,\n              columnNumber: 17\n            }, this), statistics.certificazioniOggi > 0 && /*#__PURE__*/_jsxDEV(Badge, {\n              badgeContent: statistics.certificazioniOggi,\n              color: \"success\",\n              children: /*#__PURE__*/_jsxDEV(CheckIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1945,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1944,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1933,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1931,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1904,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1903,\n      columnNumber: 7\n    }, this), renderSearchAndFilters(), !loading && activeTab === 0 && renderCaviTable(), !loading && activeTab === 1 && renderCertificazioniTable(), renderCertificazioneDialog(), renderViewDialog(), /*#__PURE__*/_jsxDEV(Snackbar, {\n      open: snackbar.open,\n      autoHideDuration: 6000,\n      onClose: closeSnackbar,\n      anchorOrigin: {\n        vertical: 'bottom',\n        horizontal: 'right'\n      },\n      children: /*#__PURE__*/_jsxDEV(Alert, {\n        onClose: closeSnackbar,\n        severity: snackbar.severity,\n        sx: {\n          width: '100%'\n        },\n        children: snackbar.message\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1972,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1966,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(SpeedDial, {\n      ariaLabel: \"Azioni rapide\",\n      sx: {\n        position: 'fixed',\n        bottom: 16,\n        right: 16\n      },\n      icon: /*#__PURE__*/_jsxDEV(SpeedDialIcon, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1981,\n        columnNumber: 15\n      }, this),\n      children: [/*#__PURE__*/_jsxDEV(SpeedDialAction, {\n        icon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1984,\n          columnNumber: 17\n        }, this),\n        tooltipTitle: \"Nuova Certificazione\",\n        onClick: openCreateDialog\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1983,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(SpeedDialAction, {\n        icon: /*#__PURE__*/_jsxDEV(ExportIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1989,\n          columnNumber: 17\n        }, this),\n        tooltipTitle: \"Esporta Tutto\",\n        onClick: handleExportAll\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1988,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(SpeedDialAction, {\n        icon: /*#__PURE__*/_jsxDEV(RefreshIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1994,\n          columnNumber: 17\n        }, this),\n        tooltipTitle: \"Aggiorna Dati\",\n        onClick: loadInitialData\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1993,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(SpeedDialAction, {\n        icon: /*#__PURE__*/_jsxDEV(ReportIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1999,\n          columnNumber: 17\n        }, this),\n        tooltipTitle: \"Report Avanzato\",\n        onClick: () => showSnackbar('Funzionalità in sviluppo', 'info')\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1998,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1978,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 1859,\n    columnNumber: 5\n  }, this);\n}, \"Y6H2QcIXYjGgUXj6IRVp5RPodNQ=\")), \"Y6H2QcIXYjGgUXj6IRVp5RPodNQ=\");\n_c2 = CertificazioneCaviImproved;\nexport default CertificazioneCaviImproved;\nvar _c, _c2;\n$RefreshReg$(_c, \"CertificazioneCaviImproved$forwardRef\");\n$RefreshReg$(_c2, \"CertificazioneCaviImproved\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "forwardRef", "useImperativeHandle", "Box", "Typography", "<PERSON><PERSON>", "Paper", "Grid", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "TextField", "FormControl", "InputLabel", "Select", "MenuItem", "Autocomplete", "CircularProgress", "<PERSON><PERSON>", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "IconButton", "Tabs", "Tab", "Pagination", "InputAdornment", "Divider", "<PERSON><PERSON>", "Chip", "<PERSON><PERSON><PERSON>", "Badge", "LinearProgress", "Collapse", "List", "ListItem", "ListItemText", "ListItemIcon", "Snackbar", "AppBar", "<PERSON><PERSON><PERSON>", "Container", "Fab", "SpeedDial", "SpeedDialAction", "SpeedDialIcon", "Add", "AddIcon", "Search", "SearchIcon", "FilterList", "FilterIcon", "PictureAsPdf", "PdfIcon", "Download", "DownloadIcon", "Visibility", "ViewIcon", "Delete", "DeleteIcon", "Edit", "EditIcon", "Save", "SaveIcon", "Clear", "ClearIcon", "Build", "BuildIcon", "CheckCircle", "CheckIcon", "Warning", "WarningIcon", "GetApp", "ExportIcon", "Print", "PrintIcon", "Email", "EmailIcon", "CloudUpload", "UploadIcon", "Assessment", "ReportIcon", "Settings", "SettingsIcon", "Refresh", "RefreshIcon", "ExpandMore", "ExpandMoreIcon", "ExpandLess", "ExpandLessIcon", "Info", "InfoIcon", "Error", "ErrorIcon", "Schedule", "ScheduleIcon", "Person", "PersonIcon", "Cable", "CableIcon", "Science", "ScienceIcon", "Block", "BlockIcon", "certificazioneService", "caviService", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "CertificazioneCaviImproved", "_s", "_c", "cantiereId", "onSuccess", "onError", "ref", "loading", "setLoading", "activeTab", "setActiveTab", "certificazioni", "setCertificazioni", "cavi", "<PERSON><PERSON><PERSON>", "strumenti", "setStrumenti", "searchTerm", "setSearchTerm", "filteredCavi", "setFilteredCavi", "filteredCertificazioni", "setFilteredCertificazioni", "advancedFiltersOpen", "setAdvancedFiltersOpen", "filters", "setFilters", "stato", "tipologia", "operatore", "dataInizio", "dataFine", "valoreIsolamento", "risultatoTest", "strumento", "certificazione", "currentPage", "setCurrentPage", "itemsPerPage", "setItemsPerPage", "sortBy", "setSortBy", "sortOrder", "setSortOrder", "openDialog", "setOpenDialog", "dialogType", "setDialogType", "selectedItem", "setSelectedItem", "bulkSelection", "setBulkSelection", "bulkMode", "setBulkMode", "snackbar", "setSnackbar", "open", "message", "severity", "progress", "setProgress", "operationInProgress", "setOperationInProgress", "formData", "setFormData", "id_cavo", "id_operatore", "id_strumento", "<PERSON><PERSON><PERSON>_misurata", "valore_continuita", "valore_isolamento", "valore_resistenza", "note", "temperatura_ambiente", "<PERSON><PERSON><PERSON>", "tensione_prova", "durata_prova", "risultato_finale", "statistics", "setStatistics", "totaleCavi", "caviCertificati", "caviNonCertificati", "percentualeCompletamento", "certificazioniOggi", "certificazioniSettimana", "loadInitialData", "filterCavi", "filterCertificazioni", "calculateStatistics", "loadCavi", "loadCertificazioni", "loadStrumenti", "error", "showSnackbar", "data", "getCertificazioni", "console", "get<PERSON><PERSON>", "getStrumenti", "length", "Math", "round", "oggi", "Date", "toDateString", "filter", "cert", "data_certificazione", "unaSettimanaFa", "setDate", "getDate", "closeSnackbar", "filtered", "searchLower", "toLowerCase", "cavo", "_cavo$tipologia", "_cavo$ubicazione_part", "_cavo$ubicazione_arri", "_cavo$sezione", "_cavo$utility", "includes", "ubicazione_partenza", "ubicazione_arrivo", "sezione", "utility", "stato_installazione", "some", "sort", "a", "b", "aValue", "bValue", "_cert$operatore", "_cert$numero_certific", "_cert$note", "numero_certificato", "valore", "parseFloat", "toggleBulkMode", "toggleItemSelection", "itemId", "prev", "newSelection", "id", "selectAllItems", "allIds", "map", "id_certificazione", "clearSelection", "isCavoCertificato", "idCavo", "puoEssereCertificato", "isInstallato", "isCollegato", "colle<PERSON>nti", "hasResponsabili", "responsabile_partenza", "responsabile_arrivo", "getMessaggioErroreCertificazione", "statoCollegamento", "<PERSON><PERSON><PERSON>", "push", "join", "handleTabChange", "event", "newValue", "openCreateDialog", "cavoPreselezionato", "metratura_reale", "metri_te<PERSON>ci", "closeDialog", "handleFormChange", "field", "value", "handleCavoSelect", "handleCreateCertificazione", "find", "c", "messaggio", "createCertificazione", "handleGeneratePdf", "response", "generatePdf", "file_url", "newWindow", "window", "link", "document", "createElement", "href", "download", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "pdf_content", "blob", "Blob", "atob", "type", "url", "URL", "createObjectURL", "revokeObjectURL", "handleDeleteCertificazione", "confirm", "deleteCertificazione", "handleBulkDelete", "handleBulkExport", "<PERSON><PERSON><PERSON><PERSON>", "csv<PERSON><PERSON>nt", "generateCSV", "downloadCSV", "toISOString", "split", "headers", "rows", "toLocaleDateString", "row", "content", "filename", "undefined", "setAttribute", "style", "visibility", "handleExportAll", "handleOptionSelect", "option", "getCurrentPageItems", "items", "startIndex", "endIndex", "slice", "getTotalPages", "ceil", "getUniqueValues", "array", "Set", "item", "Boolean", "renderDashboard", "container", "spacing", "sx", "mb", "children", "xs", "md", "background", "color", "textAlign", "py", "fontSize", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "fontWeight", "renderSearchAndFilters", "p", "alignItems", "fullWidth", "placeholder", "onChange", "e", "target", "InputProps", "startAdornment", "position", "endAdornment", "onClick", "size", "startIcon", "Object", "values", "f", "disabled", "in", "my", "tip", "op", "label", "InputLabelProps", "shrink", "direction", "justifyContent", "renderCaviTable", "currentItems", "component", "isCertificato", "puoCertificare", "messaggioErrore", "icon", "title", "display", "mt", "count", "page", "renderCertificazioniTable", "padding", "selected", "hover", "s", "nome", "marca", "strumento_utilizzato", "renderCertificazioneDialog", "onClose", "max<PERSON><PERSON><PERSON>", "options", "getOptionLabel", "renderInput", "params", "required", "renderOption", "props", "modello", "numero_serie", "helperText", "multiline", "renderViewDialog", "gutterBottom", "renderStats", "totalCavi", "caviInstallati", "percentualeCertificazione", "sm", "bgcolor", "indicatorColor", "textColor", "badgeContent", "autoHideDuration", "anchor<PERSON><PERSON><PERSON>", "vertical", "horizontal", "width", "aria<PERSON><PERSON><PERSON>", "bottom", "right", "tooltipTitle", "_c2", "$RefreshReg$"], "sources": ["C:/CMS/webapp/frontend/src/components/cavi/CertificazioneCaviImproved.js"], "sourcesContent": ["import React, { useState, useEffect, forwardRef, useImperativeHandle } from 'react';\nimport {\n  Box,\n  Typo<PERSON>,\n  Button,\n  Paper,\n  Grid,\n  Card,\n  CardContent,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  TextField,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  Autocomplete,\n  CircularProgress,\n  Alert,\n  Table,\n  TableBody,\n  TableCell,\n  TableContainer,\n  TableHead,\n  TableRow,\n  IconButton,\n  Tabs,\n  Tab,\n  Pagination,\n  InputAdornment,\n  Divider,\n  Stack,\n  Chip,\n  Tooltip,\n  Badge,\n  LinearProgress,\n  Collapse,\n  List,\n  ListItem,\n  ListItemText,\n  ListItemIcon,\n  Snackbar,\n  AppBar,\n  Toolbar,\n  Container,\n  Fab,\n  SpeedDial,\n  SpeedDialAction,\n  SpeedDialIcon\n} from '@mui/material';\nimport {\n  Add as AddIcon,\n  Search as SearchIcon,\n  FilterList as FilterIcon,\n  PictureAsPdf as PdfIcon,\n  Download as DownloadIcon,\n  Visibility as ViewIcon,\n  Delete as DeleteIcon,\n  Edit as EditIcon,\n  Save as SaveIcon,\n  Clear as ClearIcon,\n  Build as BuildIcon,\n  CheckCircle as CheckIcon,\n  Warning as WarningIcon,\n  GetApp as ExportIcon,\n  Print as PrintIcon,\n  Email as EmailIcon,\n  CloudUpload as UploadIcon,\n  Assessment as ReportIcon,\n  Settings as SettingsIcon,\n  Refresh as RefreshIcon,\n  ExpandMore as ExpandMoreIcon,\n  ExpandLess as ExpandLessIcon,\n  Info as InfoIcon,\n  Error as ErrorIcon,\n  Schedule as ScheduleIcon,\n  Person as PersonIcon,\n  Cable as CableIcon,\n  Science as ScienceIcon,\n  Block as BlockIcon\n} from '@mui/icons-material';\n\nimport certificazioneService from '../../services/certificazioneService';\nimport caviService from '../../services/caviService';\n\nconst CertificazioneCaviImproved = forwardRef(({ cantiereId, onSuccess, onError }, ref) => {\n  // Stati principali\n  const [loading, setLoading] = useState(false);\n  const [activeTab, setActiveTab] = useState(0);\n  const [certificazioni, setCertificazioni] = useState([]);\n  const [cavi, setCavi] = useState([]);\n  const [strumenti, setStrumenti] = useState([]);\n\n  // Stati per ricerca e filtri avanzati\n  const [searchTerm, setSearchTerm] = useState('');\n  const [filteredCavi, setFilteredCavi] = useState([]);\n  const [filteredCertificazioni, setFilteredCertificazioni] = useState([]);\n  const [advancedFiltersOpen, setAdvancedFiltersOpen] = useState(false);\n  const [filters, setFilters] = useState({\n    stato: '',\n    tipologia: '',\n    operatore: '',\n    dataInizio: '',\n    dataFine: '',\n    valoreIsolamento: '',\n    risultatoTest: '',\n    strumento: '',\n    certificazione: ''\n  });\n\n  // Stati per paginazione e ordinamento\n  const [currentPage, setCurrentPage] = useState(1);\n  const [itemsPerPage, setItemsPerPage] = useState(10);\n  const [sortBy, setSortBy] = useState('data_certificazione');\n  const [sortOrder, setSortOrder] = useState('desc');\n\n  // Stati per dialogs e modali\n  const [openDialog, setOpenDialog] = useState(false);\n  const [dialogType, setDialogType] = useState('');\n  const [selectedItem, setSelectedItem] = useState(null);\n  const [bulkSelection, setBulkSelection] = useState([]);\n  const [bulkMode, setBulkMode] = useState(false);\n\n  // Stati per notifiche e feedback\n  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'info' });\n  const [progress, setProgress] = useState(0);\n  const [operationInProgress, setOperationInProgress] = useState(false);\n\n  // Stati per form certificazione avanzato\n  const [formData, setFormData] = useState({\n    id_cavo: '',\n    id_operatore: '',\n    id_strumento: '',\n    lunghezza_misurata: '',\n    valore_continuita: 'OK',\n    valore_isolamento: '',\n    valore_resistenza: 'OK',\n    note: '',\n    temperatura_ambiente: '',\n    umidita: '',\n    tensione_prova: '',\n    durata_prova: '',\n    risultato_finale: 'CONFORME'\n  });\n\n  // Stati per statistiche e dashboard\n  const [statistics, setStatistics] = useState({\n    totaleCavi: 0,\n    caviCertificati: 0,\n    caviNonCertificati: 0,\n    percentualeCompletamento: 0,\n    certificazioniOggi: 0,\n    certificazioniSettimana: 0\n  });\n\n  // Carica dati iniziali\n  useEffect(() => {\n    loadInitialData();\n  }, [cantiereId]);\n\n  // Filtra cavi in base alla ricerca\n  useEffect(() => {\n    filterCavi();\n  }, [cavi, searchTerm, filters, sortBy, sortOrder]);\n\n  // Filtra certificazioni\n  useEffect(() => {\n    filterCertificazioni();\n  }, [certificazioni, searchTerm, filters, sortBy, sortOrder]);\n\n  // Ricalcola statistiche quando cambiano i dati\n  useEffect(() => {\n    calculateStatistics();\n  }, [cavi, certificazioni]);\n\n  const loadInitialData = async () => {\n    try {\n      setLoading(true);\n      setProgress(0);\n\n      // Carica dati in sequenza con progress\n      setProgress(25);\n      await loadCavi();\n\n      setProgress(50);\n      await loadCertificazioni();\n\n      setProgress(75);\n      await loadStrumenti();\n\n      setProgress(100);\n      calculateStatistics();\n\n    } catch (error) {\n      showSnackbar('Errore nel caricamento dei dati iniziali', 'error');\n      onError('Errore nel caricamento dei dati iniziali');\n    } finally {\n      setLoading(false);\n      setProgress(0);\n    }\n  };\n\n  const loadCertificazioni = async () => {\n    try {\n      const data = await certificazioneService.getCertificazioni(cantiereId);\n      setCertificazioni(data);\n      return data;\n    } catch (error) {\n      console.error('Errore nel caricamento delle certificazioni:', error);\n      throw error;\n    }\n  };\n\n  const loadCavi = async () => {\n    try {\n      const data = await caviService.getCavi(cantiereId);\n      setCavi(data);\n      return data;\n    } catch (error) {\n      console.error('Errore nel caricamento dei cavi:', error);\n      throw error;\n    }\n  };\n\n  const loadStrumenti = async () => {\n    try {\n      const data = await certificazioneService.getStrumenti(cantiereId);\n      setStrumenti(data);\n      return data;\n    } catch (error) {\n      console.error('Errore nel caricamento degli strumenti:', error);\n      throw error;\n    }\n  };\n\n  // Calcola statistiche avanzate\n  const calculateStatistics = () => {\n    const totaleCavi = cavi.length;\n    const caviCertificati = certificazioni.length;\n    const caviNonCertificati = totaleCavi - caviCertificati;\n    const percentualeCompletamento = totaleCavi > 0 ? Math.round((caviCertificati / totaleCavi) * 100) : 0;\n\n    // Calcola certificazioni di oggi\n    const oggi = new Date().toDateString();\n    const certificazioniOggi = certificazioni.filter(cert =>\n      new Date(cert.data_certificazione).toDateString() === oggi\n    ).length;\n\n    // Calcola certificazioni della settimana\n    const unaSettimanaFa = new Date();\n    unaSettimanaFa.setDate(unaSettimanaFa.getDate() - 7);\n    const certificazioniSettimana = certificazioni.filter(cert =>\n      new Date(cert.data_certificazione) >= unaSettimanaFa\n    ).length;\n\n    setStatistics({\n      totaleCavi,\n      caviCertificati,\n      caviNonCertificati,\n      percentualeCompletamento,\n      certificazioniOggi,\n      certificazioniSettimana\n    });\n  };\n\n  // Gestione snackbar\n  const showSnackbar = (message, severity = 'info') => {\n    setSnackbar({ open: true, message, severity });\n  };\n\n  const closeSnackbar = () => {\n    setSnackbar({ ...snackbar, open: false });\n  };\n\n  const filterCavi = () => {\n    let filtered = cavi;\n\n    // Filtro per ricerca testuale avanzata\n    if (searchTerm) {\n      const searchLower = searchTerm.toLowerCase();\n      filtered = filtered.filter(cavo =>\n        cavo.id_cavo.toLowerCase().includes(searchLower) ||\n        cavo.tipologia?.toLowerCase().includes(searchLower) ||\n        cavo.ubicazione_partenza?.toLowerCase().includes(searchLower) ||\n        cavo.ubicazione_arrivo?.toLowerCase().includes(searchLower) ||\n        cavo.sezione?.toLowerCase().includes(searchLower) ||\n        cavo.utility?.toLowerCase().includes(searchLower)\n      );\n    }\n\n    // Filtri specifici avanzati\n    if (filters.stato) {\n      filtered = filtered.filter(cavo => cavo.stato_installazione === filters.stato);\n    }\n    if (filters.tipologia) {\n      filtered = filtered.filter(cavo => cavo.tipologia === filters.tipologia);\n    }\n\n    // Filtro per stato certificazione\n    if (filters.certificazione) {\n      if (filters.certificazione === 'CERTIFICATO') {\n        filtered = filtered.filter(cavo =>\n          certificazioni.some(cert => cert.id_cavo === cavo.id_cavo)\n        );\n      } else if (filters.certificazione === 'NON_CERTIFICATO') {\n        filtered = filtered.filter(cavo =>\n          !certificazioni.some(cert => cert.id_cavo === cavo.id_cavo)\n        );\n      }\n    }\n\n    // Ordinamento\n    filtered.sort((a, b) => {\n      let aValue = a[sortBy];\n      let bValue = b[sortBy];\n\n      if (typeof aValue === 'string') {\n        aValue = aValue.toLowerCase();\n        bValue = bValue.toLowerCase();\n      }\n\n      if (sortOrder === 'asc') {\n        return aValue > bValue ? 1 : -1;\n      } else {\n        return aValue < bValue ? 1 : -1;\n      }\n    });\n\n    setFilteredCavi(filtered);\n  };\n\n  const filterCertificazioni = () => {\n    let filtered = certificazioni;\n\n    // Ricerca testuale avanzata\n    if (searchTerm) {\n      const searchLower = searchTerm.toLowerCase();\n      filtered = filtered.filter(cert =>\n        cert.id_cavo.toLowerCase().includes(searchLower) ||\n        cert.operatore?.toLowerCase().includes(searchLower) ||\n        cert.numero_certificato?.toLowerCase().includes(searchLower) ||\n        cert.note?.toLowerCase().includes(searchLower)\n      );\n    }\n\n    // Filtri avanzati\n    if (filters.operatore) {\n      filtered = filtered.filter(cert => cert.operatore === filters.operatore);\n    }\n    if (filters.strumento) {\n      filtered = filtered.filter(cert => cert.strumento === filters.strumento);\n    }\n    if (filters.risultatoTest) {\n      filtered = filtered.filter(cert => cert.risultato_finale === filters.risultatoTest);\n    }\n    if (filters.dataInizio) {\n      filtered = filtered.filter(cert =>\n        new Date(cert.data_certificazione) >= new Date(filters.dataInizio)\n      );\n    }\n    if (filters.dataFine) {\n      filtered = filtered.filter(cert =>\n        new Date(cert.data_certificazione) <= new Date(filters.dataFine)\n      );\n    }\n    if (filters.valoreIsolamento) {\n      const valore = parseFloat(filters.valoreIsolamento);\n      filtered = filtered.filter(cert =>\n        parseFloat(cert.valore_isolamento) >= valore\n      );\n    }\n\n    // Ordinamento\n    filtered.sort((a, b) => {\n      let aValue = a[sortBy];\n      let bValue = b[sortBy];\n\n      if (sortBy === 'data_certificazione') {\n        aValue = new Date(aValue);\n        bValue = new Date(bValue);\n      } else if (typeof aValue === 'string') {\n        aValue = aValue.toLowerCase();\n        bValue = bValue.toLowerCase();\n      }\n\n      if (sortOrder === 'asc') {\n        return aValue > bValue ? 1 : -1;\n      } else {\n        return aValue < bValue ? 1 : -1;\n      }\n    });\n\n    setFilteredCertificazioni(filtered);\n  };\n\n  // Gestione selezione multipla - SOLO per certificazioni\n  const toggleBulkMode = () => {\n    if (activeTab !== 1) {\n      showSnackbar('La selezione multipla è disponibile solo per le certificazioni', 'warning');\n      return;\n    }\n    setBulkMode(!bulkMode);\n    setBulkSelection([]);\n    showSnackbar(\n      !bulkMode\n        ? 'Modalità selezione attivata - Clicca sulle certificazioni per selezionarle'\n        : 'Modalità selezione disattivata',\n      'info'\n    );\n  };\n\n  const toggleItemSelection = (itemId) => {\n    setBulkSelection(prev => {\n      const newSelection = prev.includes(itemId)\n        ? prev.filter(id => id !== itemId)\n        : [...prev, itemId];\n\n      showSnackbar(\n        `${newSelection.length} certificazioni selezionate`,\n        'info'\n      );\n      return newSelection;\n    });\n  };\n\n  const selectAllItems = () => {\n    if (activeTab !== 1) return;\n\n    const allIds = filteredCertificazioni.map(cert => cert.id_certificazione);\n    setBulkSelection(allIds);\n    showSnackbar(`Tutte le ${allIds.length} certificazioni selezionate`, 'success');\n  };\n\n  const clearSelection = () => {\n    setBulkSelection([]);\n    showSnackbar('Selezione cancellata', 'info');\n  };\n\n  // Funzione per verificare se un cavo è certificato\n  const isCavoCertificato = (idCavo) => {\n    return certificazioni.some(cert => cert.id_cavo === idCavo);\n  };\n\n  // Funzione per verificare se un cavo può essere certificato\n  const puoEssereCertificato = (cavo) => {\n    // Verifica che il cavo sia installato\n    const isInstallato = cavo.stato_installazione === 'Installato' ||\n                        cavo.stato_installazione === 'INSTALLATO' ||\n                        cavo.stato_installazione === 'POSATO';\n\n    // Verifica che il cavo sia completamente collegato (collegamenti = 3)\n    // 3 = 1 (partenza) + 2 (arrivo) = completamente collegato\n    const isCollegato = cavo.collegamenti === 3;\n\n    // Verifica che abbia responsabili per entrambi i lati\n    const hasResponsabili = cavo.responsabile_partenza && cavo.responsabile_arrivo;\n\n    return isInstallato && isCollegato && hasResponsabili;\n  };\n\n  // Funzione per ottenere il messaggio di errore per cavi non certificabili\n  const getMessaggioErroreCertificazione = (cavo) => {\n    const isInstallato = cavo.stato_installazione === 'Installato' ||\n                        cavo.stato_installazione === 'INSTALLATO' ||\n                        cavo.stato_installazione === 'POSATO';\n    const isCollegato = cavo.collegamenti === 3;\n    const hasResponsabili = cavo.responsabile_partenza && cavo.responsabile_arrivo;\n\n    if (!isInstallato) {\n      return 'Il cavo deve essere installato prima di poter essere certificato';\n    }\n    if (!isCollegato) {\n      const statoCollegamento = cavo.collegamenti === 0 ? 'non collegato' :\n                               cavo.collegamenti === 1 ? 'collegato solo lato partenza' :\n                               cavo.collegamenti === 2 ? 'collegato solo lato arrivo' :\n                               'stato collegamento sconosciuto';\n      return `Il cavo deve essere collegato da entrambi i lati (attualmente: ${statoCollegamento})`;\n    }\n    if (!hasResponsabili) {\n      const mancanti = [];\n      if (!cavo.responsabile_partenza) mancanti.push('partenza');\n      if (!cavo.responsabile_arrivo) mancanti.push('arrivo');\n      return `Mancano i responsabili per: ${mancanti.join(', ')}`;\n    }\n    return 'Cavo non certificabile per motivi sconosciuti';\n  };\n\n  // Gestione tabs\n  const handleTabChange = (event, newValue) => {\n    setActiveTab(newValue);\n    setCurrentPage(1);\n    setSearchTerm('');\n    setFilters({ stato: '', tipologia: '', operatore: '' });\n  };\n\n  // Gestione dialogs\n  const openCreateDialog = (cavoPreselezionato = null) => {\n    setDialogType('create');\n    setSelectedItem(null);\n\n    // Se viene passato un cavo, precompila il form\n    if (cavoPreselezionato) {\n      setFormData({\n        id_cavo: cavoPreselezionato.id_cavo,\n        id_operatore: '',\n        id_strumento: '',\n        lunghezza_misurata: cavoPreselezionato.metratura_reale || cavoPreselezionato.metri_teorici || '',\n        valore_continuita: 'OK',\n        valore_isolamento: '',\n        valore_resistenza: 'OK',\n        note: '',\n        temperatura_ambiente: '',\n        umidita: '',\n        tensione_prova: '',\n        durata_prova: '',\n        risultato_finale: 'CONFORME'\n      });\n      showSnackbar(`Cavo ${cavoPreselezionato.id_cavo} selezionato automaticamente`, 'success');\n    } else {\n      // Reset form per nuova certificazione generica\n      setFormData({\n        id_cavo: '',\n        id_operatore: '',\n        id_strumento: '',\n        lunghezza_misurata: '',\n        valore_continuita: 'OK',\n        valore_isolamento: '',\n        valore_resistenza: 'OK',\n        note: '',\n        temperatura_ambiente: '',\n        umidita: '',\n        tensione_prova: '',\n        durata_prova: '',\n        risultato_finale: 'CONFORME'\n      });\n    }\n\n    setOpenDialog(true);\n  };\n\n  const closeDialog = () => {\n    setOpenDialog(false);\n    setSelectedItem(null);\n    setDialogType('');\n  };\n\n  // Gestione form\n  const handleFormChange = (field, value) => {\n    setFormData(prev => ({\n      ...prev,\n      [field]: value\n    }));\n  };\n\n  const handleCavoSelect = (cavo) => {\n    setFormData(prev => ({\n      ...prev,\n      id_cavo: cavo.id_cavo,\n      lunghezza_misurata: cavo.metratura_reale || cavo.metri_teorici || ''\n    }));\n  };\n\n  // Operazioni CRUD avanzate\n  const handleCreateCertificazione = async () => {\n    try {\n      if (!formData.id_cavo || !formData.id_operatore || !formData.id_strumento || !formData.valore_isolamento) {\n        showSnackbar('Compila tutti i campi obbligatori', 'warning');\n        return;\n      }\n\n      // Verifica che il cavo possa essere certificato\n      const cavo = cavi.find(c => c.id_cavo === formData.id_cavo);\n      if (!cavo) {\n        showSnackbar('Cavo non trovato', 'error');\n        return;\n      }\n\n      if (!puoEssereCertificato(cavo)) {\n        const messaggio = getMessaggioErroreCertificazione(cavo);\n        showSnackbar(`Impossibile certificare il cavo: ${messaggio}`, 'error');\n        return;\n      }\n\n      // Verifica che il cavo non sia già certificato\n      if (isCavoCertificato(formData.id_cavo)) {\n        showSnackbar('Il cavo è già stato certificato', 'warning');\n        return;\n      }\n\n      setOperationInProgress(true);\n      await certificazioneService.createCertificazione(cantiereId, formData);\n      showSnackbar('Certificazione creata con successo', 'success');\n      closeDialog();\n      await loadCertificazioni();\n      calculateStatistics();\n    } catch (error) {\n      showSnackbar('Errore nella creazione della certificazione: ' + (error.message || 'Errore sconosciuto'), 'error');\n    } finally {\n      setOperationInProgress(false);\n    }\n  };\n\n  const handleGeneratePdf = async (certificazione) => {\n    try {\n      setOperationInProgress(true);\n      showSnackbar('Generazione PDF in corso...', 'info');\n\n      const response = await certificazioneService.generatePdf(cantiereId, certificazione.id_certificazione);\n\n      if (response.file_url) {\n        // Apri il PDF in una nuova finestra\n        const newWindow = window.open(response.file_url, '_blank');\n        if (newWindow) {\n          showSnackbar('PDF generato e aperto in una nuova finestra', 'success');\n        } else {\n          // Se il popup è bloccato, offri il download diretto\n          const link = document.createElement('a');\n          link.href = response.file_url;\n          link.download = `certificazione_${certificazione.numero_certificato}.pdf`;\n          document.body.appendChild(link);\n          link.click();\n          document.body.removeChild(link);\n          showSnackbar('PDF scaricato nella cartella Download', 'success');\n        }\n      } else if (response.pdf_content) {\n        // Se il PDF viene restituito come contenuto base64\n        const blob = new Blob([atob(response.pdf_content)], { type: 'application/pdf' });\n        const url = URL.createObjectURL(blob);\n        const link = document.createElement('a');\n        link.href = url;\n        link.download = `certificazione_${certificazione.numero_certificato}.pdf`;\n        document.body.appendChild(link);\n        link.click();\n        document.body.removeChild(link);\n        URL.revokeObjectURL(url);\n        showSnackbar('PDF scaricato con successo', 'success');\n      } else {\n        showSnackbar('Errore: Formato PDF non riconosciuto', 'error');\n      }\n    } catch (error) {\n      console.error('Errore generazione PDF:', error);\n      showSnackbar('Errore nella generazione del PDF: ' + (error.message || 'Errore sconosciuto'), 'error');\n    } finally {\n      setOperationInProgress(false);\n    }\n  };\n\n  const handleDeleteCertificazione = async (certificazione) => {\n    if (window.confirm(`Sei sicuro di voler eliminare la certificazione ${certificazione.numero_certificato}?`)) {\n      try {\n        setOperationInProgress(true);\n        await certificazioneService.deleteCertificazione(cantiereId, certificazione.id_certificazione);\n        showSnackbar('Certificazione eliminata con successo', 'success');\n        await loadCertificazioni();\n        calculateStatistics();\n      } catch (error) {\n        showSnackbar('Errore nell\\'eliminazione della certificazione: ' + (error.message || 'Errore sconosciuto'), 'error');\n      } finally {\n        setOperationInProgress(false);\n      }\n    }\n  };\n\n  // Operazioni bulk\n  const handleBulkDelete = async () => {\n    if (bulkSelection.length === 0) {\n      showSnackbar('Seleziona almeno un elemento', 'warning');\n      return;\n    }\n\n    if (window.confirm(`Sei sicuro di voler eliminare ${bulkSelection.length} certificazioni?`)) {\n      try {\n        setOperationInProgress(true);\n        for (const id of bulkSelection) {\n          await certificazioneService.deleteCertificazione(cantiereId, id);\n        }\n        showSnackbar(`${bulkSelection.length} certificazioni eliminate con successo`, 'success');\n        setBulkSelection([]);\n        await loadCertificazioni();\n        calculateStatistics();\n      } catch (error) {\n        showSnackbar('Errore nell\\'eliminazione delle certificazioni', 'error');\n      } finally {\n        setOperationInProgress(false);\n      }\n    }\n  };\n\n  const handleBulkExport = async () => {\n    if (bulkSelection.length === 0) {\n      showSnackbar('Seleziona almeno un elemento', 'warning');\n      return;\n    }\n\n    try {\n      setOperationInProgress(true);\n      // Implementa export bulk\n      const selectedCerts = certificazioni.filter(cert =>\n        bulkSelection.includes(cert.id_certificazione)\n      );\n\n      // Crea CSV\n      const csvContent = generateCSV(selectedCerts);\n      downloadCSV(csvContent, `certificazioni_${new Date().toISOString().split('T')[0]}.csv`);\n\n      showSnackbar(`${bulkSelection.length} certificazioni esportate`, 'success');\n    } catch (error) {\n      showSnackbar('Errore nell\\'esportazione', 'error');\n    } finally {\n      setOperationInProgress(false);\n    }\n  };\n\n  // Funzioni di export\n  const generateCSV = (data) => {\n    const headers = ['ID Cavo', 'Numero Certificato', 'Data', 'Operatore', 'Strumento', 'Lunghezza', 'Isolamento', 'Risultato'];\n    const rows = data.map(cert => [\n      cert.id_cavo,\n      cert.numero_certificato,\n      new Date(cert.data_certificazione).toLocaleDateString(),\n      cert.operatore,\n      cert.strumento,\n      cert.lunghezza_misurata,\n      cert.valore_isolamento,\n      cert.risultato_finale\n    ]);\n\n    return [headers, ...rows].map(row => row.join(',')).join('\\n');\n  };\n\n  const downloadCSV = (content, filename) => {\n    const blob = new Blob([content], { type: 'text/csv;charset=utf-8;' });\n    const link = document.createElement('a');\n    if (link.download !== undefined) {\n      const url = URL.createObjectURL(blob);\n      link.setAttribute('href', url);\n      link.setAttribute('download', filename);\n      link.style.visibility = 'hidden';\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n    }\n  };\n\n  const handleExportAll = () => {\n    const csvContent = generateCSV(filteredCertificazioni);\n    downloadCSV(csvContent, `tutte_certificazioni_${new Date().toISOString().split('T')[0]}.csv`);\n    showSnackbar('Esportazione completata', 'success');\n  };\n\n  // Espone metodi tramite ref\n  useImperativeHandle(ref, () => ({\n    handleOptionSelect: (option) => {\n      if (option === 'creaCertificazione') {\n        openCreateDialog();\n      } else if (option === 'visualizzaCertificazioni') {\n        setActiveTab(1);\n      }\n    }\n  }));\n\n  // Calcola elementi per paginazione\n  const getCurrentPageItems = (items) => {\n    const startIndex = (currentPage - 1) * itemsPerPage;\n    const endIndex = startIndex + itemsPerPage;\n    return items.slice(startIndex, endIndex);\n  };\n\n  const getTotalPages = (items) => Math.ceil(items.length / itemsPerPage);\n\n  // Ottieni opzioni uniche per filtri\n  const getUniqueValues = (array, field) => {\n    return [...new Set(array.map(item => item[field]).filter(Boolean))];\n  };\n\n  // Componente Dashboard con statistiche\n  const renderDashboard = () => (\n    <Grid container spacing={3} sx={{ mb: 3 }}>\n      <Grid item xs={12} md={2}>\n        <Card sx={{ background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)', color: 'white' }}>\n          <CardContent sx={{ textAlign: 'center', py: 2 }}>\n            <CableIcon sx={{ fontSize: 40, mb: 1 }} />\n            <Typography variant=\"h4\" fontWeight=\"bold\">\n              {statistics.totaleCavi}\n            </Typography>\n            <Typography variant=\"body2\">\n              Totale Cavi\n            </Typography>\n          </CardContent>\n        </Card>\n      </Grid>\n\n      <Grid item xs={12} md={2}>\n        <Card sx={{ background: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)', color: 'white' }}>\n          <CardContent sx={{ textAlign: 'center', py: 2 }}>\n            <CheckIcon sx={{ fontSize: 40, mb: 1 }} />\n            <Typography variant=\"h4\" fontWeight=\"bold\">\n              {statistics.caviCertificati}\n            </Typography>\n            <Typography variant=\"body2\">\n              Certificati\n            </Typography>\n          </CardContent>\n        </Card>\n      </Grid>\n\n      <Grid item xs={12} md={2}>\n        <Card sx={{ background: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)', color: 'white' }}>\n          <CardContent sx={{ textAlign: 'center', py: 2 }}>\n            <WarningIcon sx={{ fontSize: 40, mb: 1 }} />\n            <Typography variant=\"h4\" fontWeight=\"bold\">\n              {statistics.caviNonCertificati}\n            </Typography>\n            <Typography variant=\"body2\">\n              Da Certificare\n            </Typography>\n          </CardContent>\n        </Card>\n      </Grid>\n\n      <Grid item xs={12} md={2}>\n        <Card sx={{ background: 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)', color: 'white' }}>\n          <CardContent sx={{ textAlign: 'center', py: 2 }}>\n            <ReportIcon sx={{ fontSize: 40, mb: 1 }} />\n            <Typography variant=\"h4\" fontWeight=\"bold\">\n              {statistics.percentualeCompletamento}%\n            </Typography>\n            <Typography variant=\"body2\">\n              Completamento\n            </Typography>\n          </CardContent>\n        </Card>\n      </Grid>\n\n      <Grid item xs={12} md={2}>\n        <Card sx={{ background: 'linear-gradient(135deg, #fa709a 0%, #fee140 100%)', color: 'white' }}>\n          <CardContent sx={{ textAlign: 'center', py: 2 }}>\n            <ScheduleIcon sx={{ fontSize: 40, mb: 1 }} />\n            <Typography variant=\"h4\" fontWeight=\"bold\">\n              {statistics.certificazioniOggi}\n            </Typography>\n            <Typography variant=\"body2\">\n              Oggi\n            </Typography>\n          </CardContent>\n        </Card>\n      </Grid>\n\n      <Grid item xs={12} md={2}>\n        <Card sx={{ background: 'linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)', color: '#333' }}>\n          <CardContent sx={{ textAlign: 'center', py: 2 }}>\n            <ScienceIcon sx={{ fontSize: 40, mb: 1 }} />\n            <Typography variant=\"h4\" fontWeight=\"bold\">\n              {statistics.certificazioniSettimana}\n            </Typography>\n            <Typography variant=\"body2\">\n              Questa Settimana\n            </Typography>\n          </CardContent>\n        </Card>\n      </Grid>\n    </Grid>\n  );\n\n  // Componente barra di ricerca avanzata\n  const renderSearchAndFilters = () => (\n    <Paper sx={{ p: 2, mb: 3 }}>\n      <Grid container spacing={2} alignItems=\"center\">\n        <Grid item xs={12} md={4}>\n          <TextField\n            fullWidth\n            placeholder=\"Cerca cavi, certificazioni, operatori...\"\n            value={searchTerm}\n            onChange={(e) => setSearchTerm(e.target.value)}\n            InputProps={{\n              startAdornment: (\n                <InputAdornment position=\"start\">\n                  <SearchIcon />\n                </InputAdornment>\n              ),\n              endAdornment: searchTerm && (\n                <InputAdornment position=\"end\">\n                  <IconButton onClick={() => setSearchTerm('')} size=\"small\">\n                    <ClearIcon />\n                  </IconButton>\n                </InputAdornment>\n              )\n            }}\n          />\n        </Grid>\n\n        <Grid item xs={12} md={2}>\n          <Button\n            fullWidth\n            variant=\"outlined\"\n            startIcon={<FilterIcon />}\n            onClick={() => setAdvancedFiltersOpen(!advancedFiltersOpen)}\n            color={Object.values(filters).some(f => f) ? 'primary' : 'inherit'}\n          >\n            Filtri {Object.values(filters).filter(f => f).length > 0 && `(${Object.values(filters).filter(f => f).length})`}\n          </Button>\n        </Grid>\n\n        <Grid item xs={12} md={2}>\n          <Button\n            fullWidth\n            variant=\"outlined\"\n            startIcon={bulkMode ? <ClearIcon /> : <CheckIcon />}\n            onClick={toggleBulkMode}\n            color={bulkMode ? 'secondary' : 'inherit'}\n            disabled={activeTab === 0}\n          >\n            {bulkMode ? 'Esci Selezione' : 'Selezione Multipla'}\n          </Button>\n        </Grid>\n\n        <Grid item xs={12} md={2}>\n          <Button\n            fullWidth\n            variant=\"outlined\"\n            startIcon={<ExportIcon />}\n            onClick={handleExportAll}\n            disabled={activeTab === 0 || filteredCertificazioni.length === 0}\n          >\n            {activeTab === 0 ? 'Export (solo certificazioni)' : 'Esporta Certificazioni'}\n          </Button>\n        </Grid>\n\n        <Grid item xs={12} md={2}>\n          <Button\n            fullWidth\n            variant=\"contained\"\n            startIcon={<AddIcon />}\n            onClick={openCreateDialog}\n          >\n            Nuova Certificazione\n          </Button>\n        </Grid>\n      </Grid>\n\n      {/* Filtri avanzati - Diversi per ogni tab */}\n      <Collapse in={advancedFiltersOpen}>\n        <Divider sx={{ my: 2 }} />\n        <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mb: 2 }}>\n          {activeTab === 0 ? 'Filtri per Cavi' : 'Filtri per Certificazioni'}\n        </Typography>\n\n        <Grid container spacing={2}>\n          {/* Filtri per tab Cavi */}\n          {activeTab === 0 && (\n            <>\n              <Grid item xs={12} md={3}>\n                <FormControl fullWidth size=\"small\">\n                  <InputLabel>Stato Installazione</InputLabel>\n                  <Select\n                    value={filters.stato}\n                    onChange={(e) => setFilters({...filters, stato: e.target.value})}\n                  >\n                    <MenuItem value=\"\">Tutti</MenuItem>\n                    <MenuItem value=\"INSTALLATO\">Installato</MenuItem>\n                    <MenuItem value=\"NON_INSTALLATO\">Non Installato</MenuItem>\n                    <MenuItem value=\"IN_CORSO\">In Corso</MenuItem>\n                  </Select>\n                </FormControl>\n              </Grid>\n\n              <Grid item xs={12} md={3}>\n                <FormControl fullWidth size=\"small\">\n                  <InputLabel>Tipologia</InputLabel>\n                  <Select\n                    value={filters.tipologia}\n                    onChange={(e) => setFilters({...filters, tipologia: e.target.value})}\n                  >\n                    <MenuItem value=\"\">Tutte</MenuItem>\n                    {[...new Set(cavi.map(c => c.tipologia))].filter(Boolean).map(tip => (\n                      <MenuItem key={tip} value={tip}>{tip}</MenuItem>\n                    ))}\n                  </Select>\n                </FormControl>\n              </Grid>\n\n              <Grid item xs={12} md={3}>\n                <FormControl fullWidth size=\"small\">\n                  <InputLabel>Stato Certificazione</InputLabel>\n                  <Select\n                    value={filters.certificazione}\n                    onChange={(e) => setFilters({...filters, certificazione: e.target.value})}\n                  >\n                    <MenuItem value=\"\">Tutti</MenuItem>\n                    <MenuItem value=\"CERTIFICATO\">Certificato</MenuItem>\n                    <MenuItem value=\"NON_CERTIFICATO\">Non Certificato</MenuItem>\n                  </Select>\n                </FormControl>\n              </Grid>\n            </>\n          )}\n\n          {/* Filtri per tab Certificazioni */}\n          {activeTab === 1 && (\n            <>\n              <Grid item xs={12} md={3}>\n                <FormControl fullWidth size=\"small\">\n                  <InputLabel>Operatore</InputLabel>\n                  <Select\n                    value={filters.operatore}\n                    onChange={(e) => setFilters({...filters, operatore: e.target.value})}\n                  >\n                    <MenuItem value=\"\">Tutti</MenuItem>\n                    {[...new Set(certificazioni.map(c => c.operatore))].filter(Boolean).map(op => (\n                      <MenuItem key={op} value={op}>{op}</MenuItem>\n                    ))}\n                  </Select>\n                </FormControl>\n              </Grid>\n\n              <Grid item xs={12} md={3}>\n                <FormControl fullWidth size=\"small\">\n                  <InputLabel>Risultato Test</InputLabel>\n                  <Select\n                    value={filters.risultatoTest}\n                    onChange={(e) => setFilters({...filters, risultatoTest: e.target.value})}\n                  >\n                    <MenuItem value=\"\">Tutti</MenuItem>\n                    <MenuItem value=\"CONFORME\">Conforme</MenuItem>\n                    <MenuItem value=\"NON_CONFORME\">Non Conforme</MenuItem>\n                    <MenuItem value=\"DA_VERIFICARE\">Da Verificare</MenuItem>\n                  </Select>\n                </FormControl>\n              </Grid>\n\n              <Grid item xs={12} md={3}>\n                <TextField\n                  fullWidth\n                  size=\"small\"\n                  label=\"Isolamento Min (MΩ)\"\n                  type=\"number\"\n                  value={filters.valoreIsolamento}\n                  onChange={(e) => setFilters({...filters, valoreIsolamento: e.target.value})}\n                  placeholder=\"es. 500\"\n                />\n              </Grid>\n\n              <Grid item xs={12} md={3}>\n                <TextField\n                  fullWidth\n                  size=\"small\"\n                  label=\"Data Inizio\"\n                  type=\"date\"\n                  value={filters.dataInizio}\n                  onChange={(e) => setFilters({...filters, dataInizio: e.target.value})}\n                  InputLabelProps={{ shrink: true }}\n                />\n              </Grid>\n\n              <Grid item xs={12} md={3}>\n                <TextField\n                  fullWidth\n                  size=\"small\"\n                  label=\"Data Fine\"\n                  type=\"date\"\n                  value={filters.dataFine}\n                  onChange={(e) => setFilters({...filters, dataFine: e.target.value})}\n                  InputLabelProps={{ shrink: true }}\n                />\n              </Grid>\n            </>\n          )}\n\n          <Grid item xs={12}>\n            <Stack direction=\"row\" spacing={1} justifyContent=\"flex-end\">\n              <Button\n                variant=\"outlined\"\n                size=\"small\"\n                onClick={() => setFilters({\n                  stato: '', tipologia: '', operatore: '', dataInizio: '',\n                  dataFine: '', valoreIsolamento: '', risultatoTest: '', strumento: '',\n                  certificazione: ''\n                })}\n              >\n                Pulisci Tutti i Filtri\n              </Button>\n            </Stack>\n          </Grid>\n        </Grid>\n      </Collapse>\n\n      {/* Barra azioni bulk */}\n      {bulkMode && bulkSelection.length > 0 && (\n        <>\n          <Divider sx={{ my: 2 }} />\n          <Stack direction=\"row\" spacing={2} alignItems=\"center\">\n            <Typography variant=\"body2\">\n              {bulkSelection.length} elementi selezionati\n            </Typography>\n            <Button\n              size=\"small\"\n              variant=\"outlined\"\n              onClick={selectAllItems}\n            >\n              Seleziona Tutto\n            </Button>\n            <Button\n              size=\"small\"\n              variant=\"outlined\"\n              onClick={clearSelection}\n            >\n              Deseleziona\n            </Button>\n            <Button\n              size=\"small\"\n              variant=\"outlined\"\n              startIcon={<ExportIcon />}\n              onClick={handleBulkExport}\n            >\n              Esporta Selezionati\n            </Button>\n            <Button\n              size=\"small\"\n              variant=\"outlined\"\n              color=\"error\"\n              startIcon={<DeleteIcon />}\n              onClick={handleBulkDelete}\n            >\n              Elimina Selezionati\n            </Button>\n          </Stack>\n        </>\n      )}\n    </Paper>\n  );\n\n  // Renderizza la tabella dei cavi\n  const renderCaviTable = () => {\n    const currentItems = getCurrentPageItems(filteredCavi);\n\n    if (filteredCavi.length === 0) {\n      return (\n        <Alert severity=\"info\">\n          {searchTerm || filters.stato || filters.tipologia\n            ? 'Nessun cavo trovato con i filtri applicati'\n            : 'Nessun cavo disponibile'}\n        </Alert>\n      );\n    }\n\n    return (\n      <>\n        <TableContainer component={Paper}>\n          <Table size=\"small\">\n            <TableHead>\n              <TableRow>\n                <TableCell>ID Cavo</TableCell>\n                <TableCell>Tipologia</TableCell>\n                <TableCell>Sezione</TableCell>\n                <TableCell>Partenza</TableCell>\n                <TableCell>Arrivo</TableCell>\n                <TableCell>Metri</TableCell>\n                <TableCell>Stato</TableCell>\n                <TableCell>Certificato</TableCell>\n                <TableCell>Azioni</TableCell>\n              </TableRow>\n            </TableHead>\n            <TableBody>\n              {currentItems.map((cavo) => {\n                const isCertificato = isCavoCertificato(cavo.id_cavo);\n                const puoCertificare = puoEssereCertificato(cavo);\n                const messaggioErrore = !puoCertificare ? getMessaggioErroreCertificazione(cavo) : '';\n\n                return (\n                  <TableRow key={cavo.id_cavo}>\n                    <TableCell>\n                      <Typography variant=\"body2\" fontWeight=\"medium\">\n                        {cavo.id_cavo}\n                      </Typography>\n                    </TableCell>\n                    <TableCell>{cavo.tipologia}</TableCell>\n                    <TableCell>{cavo.sezione}</TableCell>\n                    <TableCell>{cavo.ubicazione_partenza}</TableCell>\n                    <TableCell>{cavo.ubicazione_arrivo}</TableCell>\n                    <TableCell>{cavo.metratura_reale || cavo.metri_teorici} m</TableCell>\n                    <TableCell>\n                      <Chip\n                        size=\"small\"\n                        label={cavo.stato_installazione}\n                        color={cavo.stato_installazione === 'INSTALLATO' ? 'success' : 'default'}\n                      />\n                    </TableCell>\n                    <TableCell>\n                      {isCertificato ? (\n                        <Chip\n                          size=\"small\"\n                          icon={<CheckIcon />}\n                          label=\"Certificato\"\n                          color=\"success\"\n                        />\n                      ) : (\n                        <Chip\n                          size=\"small\"\n                          icon={<WarningIcon />}\n                          label=\"Non certificato\"\n                          color=\"warning\"\n                        />\n                      )}\n                    </TableCell>\n                    <TableCell>\n                      {isCertificato ? (\n                        <Tooltip title=\"Cavo già certificato\">\n                          <Chip\n                            icon={<CheckIcon />}\n                            label=\"Certificato\"\n                            color=\"success\"\n                            size=\"small\"\n                          />\n                        </Tooltip>\n                      ) : puoCertificare ? (\n                        <Tooltip title=\"Crea certificazione per questo cavo\">\n                          <IconButton\n                            size=\"small\"\n                            onClick={() => openCreateDialog(cavo)}\n                            color=\"primary\"\n                          >\n                            <AddIcon />\n                          </IconButton>\n                        </Tooltip>\n                      ) : (\n                        <Tooltip title={messaggioErrore}>\n                          <span>\n                            <IconButton\n                              size=\"small\"\n                              disabled\n                              onClick={() => showSnackbar(messaggioErrore, 'warning')}\n                            >\n                              <BlockIcon />\n                            </IconButton>\n                          </span>\n                        </Tooltip>\n                      )}\n                    </TableCell>\n                  </TableRow>\n                );\n              })}\n            </TableBody>\n          </Table>\n        </TableContainer>\n\n        {getTotalPages(filteredCavi) > 1 && (\n          <Box sx={{ display: 'flex', justifyContent: 'center', mt: 2 }}>\n            <Pagination\n              count={getTotalPages(filteredCavi)}\n              page={currentPage}\n              onChange={(event, value) => setCurrentPage(value)}\n              color=\"primary\"\n            />\n          </Box>\n        )}\n      </>\n    );\n  };\n\n  // Renderizza la tabella delle certificazioni\n  const renderCertificazioniTable = () => {\n    const currentItems = getCurrentPageItems(filteredCertificazioni);\n\n    if (filteredCertificazioni.length === 0) {\n      return (\n        <Alert severity=\"info\">\n          {searchTerm || filters.operatore\n            ? 'Nessuna certificazione trovata con i filtri applicati'\n            : 'Nessuna certificazione disponibile'}\n        </Alert>\n      );\n    }\n\n    return (\n      <>\n        <TableContainer component={Paper}>\n          <Table size=\"small\">\n            <TableHead>\n              <TableRow>\n                {bulkMode && (\n                  <TableCell padding=\"checkbox\">\n                    <IconButton\n                      size=\"small\"\n                      onClick={bulkSelection.length === filteredCertificazioni.length ? clearSelection : selectAllItems}\n                    >\n                      {bulkSelection.length === filteredCertificazioni.length ? <ClearIcon /> : <CheckIcon />}\n                    </IconButton>\n                  </TableCell>\n                )}\n                <TableCell>\n                  <Stack direction=\"row\" alignItems=\"center\" spacing={1}>\n                    <Typography variant=\"body2\" fontWeight=\"bold\">N° Certificato</Typography>\n                    <IconButton size=\"small\" onClick={() => {\n                      setSortBy('numero_certificato');\n                      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');\n                    }}>\n                      {sortBy === 'numero_certificato' ? (sortOrder === 'asc' ? <ExpandLessIcon /> : <ExpandMoreIcon />) : <ExpandMoreIcon />}\n                    </IconButton>\n                  </Stack>\n                </TableCell>\n                <TableCell>ID Cavo</TableCell>\n                <TableCell>\n                  <Stack direction=\"row\" alignItems=\"center\" spacing={1}>\n                    <Typography variant=\"body2\" fontWeight=\"bold\">Data</Typography>\n                    <IconButton size=\"small\" onClick={() => {\n                      setSortBy('data_certificazione');\n                      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');\n                    }}>\n                      {sortBy === 'data_certificazione' ? (sortOrder === 'asc' ? <ExpandLessIcon /> : <ExpandMoreIcon />) : <ExpandMoreIcon />}\n                    </IconButton>\n                  </Stack>\n                </TableCell>\n                <TableCell>Operatore</TableCell>\n                <TableCell>Strumento</TableCell>\n                <TableCell>Lunghezza</TableCell>\n                <TableCell>Isolamento</TableCell>\n                <TableCell>Risultato</TableCell>\n                <TableCell>Azioni</TableCell>\n              </TableRow>\n            </TableHead>\n            <TableBody>\n              {currentItems.map((cert) => (\n                <TableRow\n                  key={cert.id_certificazione}\n                  selected={bulkSelection.includes(cert.id_certificazione)}\n                  hover\n                >\n                  {bulkMode && (\n                    <TableCell padding=\"checkbox\">\n                      <IconButton\n                        size=\"small\"\n                        onClick={() => toggleItemSelection(cert.id_certificazione)}\n                        color={bulkSelection.includes(cert.id_certificazione) ? 'primary' : 'default'}\n                      >\n                        {bulkSelection.includes(cert.id_certificazione) ? <CheckIcon /> : <AddIcon />}\n                      </IconButton>\n                    </TableCell>\n                  )}\n                  <TableCell>\n                    <Typography variant=\"body2\" fontWeight=\"medium\">\n                      {cert.numero_certificato}\n                    </Typography>\n                  </TableCell>\n                  <TableCell>\n                    <Chip size=\"small\" label={cert.id_cavo} variant=\"outlined\" />\n                  </TableCell>\n                  <TableCell>{new Date(cert.data_certificazione).toLocaleDateString()}</TableCell>\n                  <TableCell>\n                    <Stack direction=\"row\" alignItems=\"center\" spacing={1}>\n                      <PersonIcon fontSize=\"small\" />\n                      <Typography variant=\"body2\">{cert.operatore || cert.id_operatore}</Typography>\n                    </Stack>\n                  </TableCell>\n                  <TableCell>\n                    <Typography variant=\"body2\">\n                      {cert.id_strumento ?\n                        (() => {\n                          const strumento = strumenti.find(s => s.id_strumento === cert.id_strumento);\n                          return strumento ? `${strumento.nome} - ${strumento.marca}` : 'Strumento non trovato';\n                        })()\n                        : (cert.strumento_utilizzato || 'N/A')\n                      }\n                    </Typography>\n                  </TableCell>\n                  <TableCell>\n                    <Typography variant=\"body2\">{cert.lunghezza_misurata} m</Typography>\n                  </TableCell>\n                  <TableCell>\n                    <Chip\n                      size=\"small\"\n                      label={`${cert.valore_isolamento} MΩ`}\n                      color={parseFloat(cert.valore_isolamento) >= 500 ? 'success' : 'warning'}\n                      icon={parseFloat(cert.valore_isolamento) >= 500 ? <CheckIcon /> : <WarningIcon />}\n                    />\n                  </TableCell>\n                  <TableCell>\n                    <Chip\n                      size=\"small\"\n                      label={cert.risultato_finale || 'CONFORME'}\n                      color={cert.risultato_finale === 'CONFORME' ? 'success' : cert.risultato_finale === 'NON_CONFORME' ? 'error' : 'warning'}\n                    />\n                  </TableCell>\n                  <TableCell>\n                    <Stack direction=\"row\" spacing={0.5}>\n                      <Tooltip title=\"Visualizza dettagli\">\n                        <IconButton\n                          size=\"small\"\n                          onClick={() => {\n                            setSelectedItem(cert);\n                            setDialogType('view');\n                            setOpenDialog(true);\n                          }}\n                        >\n                          <ViewIcon />\n                        </IconButton>\n                      </Tooltip>\n                      <Tooltip title=\"Genera PDF\">\n                        <IconButton\n                          size=\"small\"\n                          onClick={() => handleGeneratePdf(cert)}\n                          disabled={operationInProgress}\n                        >\n                          <PdfIcon />\n                        </IconButton>\n                      </Tooltip>\n                      <Tooltip title=\"Elimina\">\n                        <IconButton\n                          size=\"small\"\n                          color=\"error\"\n                          onClick={() => handleDeleteCertificazione(cert)}\n                          disabled={operationInProgress}\n                        >\n                          <DeleteIcon />\n                        </IconButton>\n                      </Tooltip>\n                    </Stack>\n                  </TableCell>\n                </TableRow>\n              ))}\n            </TableBody>\n          </Table>\n        </TableContainer>\n\n        {getTotalPages(filteredCertificazioni) > 1 && (\n          <Box sx={{ display: 'flex', justifyContent: 'center', mt: 2 }}>\n            <Pagination\n              count={getTotalPages(filteredCertificazioni)}\n              page={currentPage}\n              onChange={(event, value) => setCurrentPage(value)}\n              color=\"primary\"\n            />\n          </Box>\n        )}\n      </>\n    );\n  };\n\n  // Renderizza il dialog per creare/modificare certificazione\n  const renderCertificazioneDialog = () => {\n    if (dialogType !== 'create' && dialogType !== 'edit') return null;\n\n    return (\n      <Dialog open={openDialog} onClose={closeDialog} maxWidth=\"md\" fullWidth>\n        <DialogTitle>\n          {dialogType === 'create' ? 'Nuova Certificazione' : 'Modifica Certificazione'}\n        </DialogTitle>\n        <DialogContent>\n          <Grid container spacing={2} sx={{ mt: 1 }}>\n            <Grid item xs={12} md={6}>\n              <Autocomplete\n                options={cavi.filter(cavo =>\n                  !certificazioni.some(cert => cert.id_cavo === cavo.id_cavo) ||\n                  cavo.id_cavo === formData.id_cavo\n                )}\n                getOptionLabel={(option) => `${option.id_cavo} - ${option.tipologia}`}\n                value={cavi.find(c => c.id_cavo === formData.id_cavo) || null}\n                onChange={(event, newValue) => {\n                  if (newValue) {\n                    handleCavoSelect(newValue);\n                  } else {\n                    setFormData(prev => ({ ...prev, id_cavo: '', lunghezza_misurata: '' }));\n                  }\n                }}\n                renderInput={(params) => (\n                  <TextField\n                    {...params}\n                    label=\"Cavo *\"\n                    placeholder=\"Seleziona un cavo\"\n                    required\n                  />\n                )}\n                renderOption={(props, option) => (\n                  <Box component=\"li\" {...props}>\n                    <Box>\n                      <Typography variant=\"body2\" fontWeight=\"medium\">\n                        {option.id_cavo}\n                      </Typography>\n                      <Typography variant=\"caption\" color=\"text.secondary\">\n                        {option.tipologia} - {option.ubicazione_partenza} → {option.ubicazione_arrivo}\n                      </Typography>\n                    </Box>\n                  </Box>\n                )}\n              />\n            </Grid>\n\n            <Grid item xs={12} md={6}>\n              <TextField\n                fullWidth\n                label=\"Operatore *\"\n                value={formData.id_operatore}\n                onChange={(e) => handleFormChange('id_operatore', e.target.value)}\n                required\n              />\n            </Grid>\n\n            <Grid item xs={12} md={6}>\n              <FormControl fullWidth required>\n                <InputLabel>Strumento *</InputLabel>\n                <Select\n                  value={formData.id_strumento}\n                  onChange={(e) => handleFormChange('id_strumento', e.target.value)}\n                  label=\"Strumento *\"\n                >\n                  {strumenti.map((strumento) => (\n                    <MenuItem key={strumento.id_strumento} value={strumento.id_strumento}>\n                      {strumento.nome} - {strumento.marca} {strumento.modello} (S/N: {strumento.numero_serie})\n                    </MenuItem>\n                  ))}\n                </Select>\n              </FormControl>\n            </Grid>\n\n            <Grid item xs={12} md={6}>\n              <TextField\n                fullWidth\n                label=\"Lunghezza Misurata (m) *\"\n                type=\"number\"\n                value={formData.lunghezza_misurata}\n                onChange={(e) => handleFormChange('lunghezza_misurata', e.target.value)}\n                required\n              />\n            </Grid>\n\n            <Grid item xs={12} md={4}>\n              <FormControl fullWidth>\n                <InputLabel>Continuità</InputLabel>\n                <Select\n                  value={formData.valore_continuita}\n                  onChange={(e) => handleFormChange('valore_continuita', e.target.value)}\n                  label=\"Continuità\"\n                >\n                  <MenuItem value=\"OK\">OK</MenuItem>\n                  <MenuItem value=\"NOK\">NOK</MenuItem>\n                </Select>\n              </FormControl>\n            </Grid>\n\n            <Grid item xs={12} md={4}>\n              <TextField\n                fullWidth\n                label=\"Isolamento (MΩ) *\"\n                type=\"number\"\n                value={formData.valore_isolamento}\n                onChange={(e) => handleFormChange('valore_isolamento', e.target.value)}\n                required\n                helperText=\"Valore minimo consigliato: 500 MΩ\"\n              />\n            </Grid>\n\n            <Grid item xs={12} md={4}>\n              <FormControl fullWidth>\n                <InputLabel>Resistenza</InputLabel>\n                <Select\n                  value={formData.valore_resistenza}\n                  onChange={(e) => handleFormChange('valore_resistenza', e.target.value)}\n                  label=\"Resistenza\"\n                >\n                  <MenuItem value=\"OK\">OK</MenuItem>\n                  <MenuItem value=\"NOK\">NOK</MenuItem>\n                </Select>\n              </FormControl>\n            </Grid>\n\n            {/* Campi avanzati */}\n            <Grid item xs={12}>\n              <Divider sx={{ my: 2 }}>\n                <Typography variant=\"body2\" color=\"text.secondary\">\n                  Parametri Ambientali e Test Avanzati\n                </Typography>\n              </Divider>\n            </Grid>\n\n            <Grid item xs={12} md={3}>\n              <TextField\n                fullWidth\n                label=\"Temperatura Ambiente (°C)\"\n                type=\"number\"\n                value={formData.temperatura_ambiente}\n                onChange={(e) => handleFormChange('temperatura_ambiente', e.target.value)}\n                helperText=\"Temperatura durante il test\"\n              />\n            </Grid>\n\n            <Grid item xs={12} md={3}>\n              <TextField\n                fullWidth\n                label=\"Umidità (%)\"\n                type=\"number\"\n                value={formData.umidita}\n                onChange={(e) => handleFormChange('umidita', e.target.value)}\n                helperText=\"Umidità relativa\"\n              />\n            </Grid>\n\n            <Grid item xs={12} md={3}>\n              <TextField\n                fullWidth\n                label=\"Tensione di Prova (V)\"\n                type=\"number\"\n                value={formData.tensione_prova}\n                onChange={(e) => handleFormChange('tensione_prova', e.target.value)}\n                helperText=\"Tensione applicata per il test\"\n              />\n            </Grid>\n\n            <Grid item xs={12} md={3}>\n              <TextField\n                fullWidth\n                label=\"Durata Prova (min)\"\n                type=\"number\"\n                value={formData.durata_prova}\n                onChange={(e) => handleFormChange('durata_prova', e.target.value)}\n                helperText=\"Durata del test in minuti\"\n              />\n            </Grid>\n\n            <Grid item xs={12} md={6}>\n              <FormControl fullWidth>\n                <InputLabel>Risultato Finale</InputLabel>\n                <Select\n                  value={formData.risultato_finale}\n                  onChange={(e) => handleFormChange('risultato_finale', e.target.value)}\n                  label=\"Risultato Finale\"\n                >\n                  <MenuItem value=\"CONFORME\">\n                    <Stack direction=\"row\" alignItems=\"center\" spacing={1}>\n                      <CheckIcon color=\"success\" />\n                      <Typography>Conforme</Typography>\n                    </Stack>\n                  </MenuItem>\n                  <MenuItem value=\"NON_CONFORME\">\n                    <Stack direction=\"row\" alignItems=\"center\" spacing={1}>\n                      <ErrorIcon color=\"error\" />\n                      <Typography>Non Conforme</Typography>\n                    </Stack>\n                  </MenuItem>\n                  <MenuItem value=\"DA_VERIFICARE\">\n                    <Stack direction=\"row\" alignItems=\"center\" spacing={1}>\n                      <WarningIcon color=\"warning\" />\n                      <Typography>Da Verificare</Typography>\n                    </Stack>\n                  </MenuItem>\n                </Select>\n              </FormControl>\n            </Grid>\n\n            <Grid item xs={12}>\n              <TextField\n                fullWidth\n                label=\"Note\"\n                multiline\n                rows={3}\n                value={formData.note}\n                onChange={(e) => handleFormChange('note', e.target.value)}\n                placeholder=\"Inserisci eventuali note, osservazioni o anomalie riscontrate durante il test...\"\n              />\n            </Grid>\n          </Grid>\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={closeDialog}>Annulla</Button>\n          <Button\n            onClick={handleCreateCertificazione}\n            variant=\"contained\"\n            disabled={loading || !formData.id_cavo || !formData.id_operatore || !formData.id_strumento || !formData.valore_isolamento}\n            startIcon={loading ? <CircularProgress size={20} /> : <SaveIcon />}\n          >\n            {dialogType === 'create' ? 'Crea Certificazione' : 'Salva Modifiche'}\n          </Button>\n        </DialogActions>\n      </Dialog>\n    );\n  };\n\n  // Renderizza il dialog di visualizzazione dettagli\n  const renderViewDialog = () => {\n    if (dialogType !== 'view' || !selectedItem) return null;\n\n    return (\n      <Dialog open={openDialog} onClose={closeDialog} maxWidth=\"md\" fullWidth>\n        <DialogTitle>\n          Dettagli Certificazione - {selectedItem.numero_certificato}\n        </DialogTitle>\n        <DialogContent>\n          <Grid container spacing={2} sx={{ mt: 1 }}>\n            <Grid item xs={12} md={6}>\n              <Card variant=\"outlined\">\n                <CardContent>\n                  <Typography variant=\"h6\" gutterBottom>\n                    Informazioni Cavo\n                  </Typography>\n                  <Typography variant=\"body2\" color=\"text.secondary\">\n                    ID Cavo: <strong>{selectedItem.id_cavo}</strong>\n                  </Typography>\n                  <Typography variant=\"body2\" color=\"text.secondary\">\n                    Lunghezza Misurata: <strong>{selectedItem.lunghezza_misurata} m</strong>\n                  </Typography>\n                </CardContent>\n              </Card>\n            </Grid>\n\n            <Grid item xs={12} md={6}>\n              <Card variant=\"outlined\">\n                <CardContent>\n                  <Typography variant=\"h6\" gutterBottom>\n                    Informazioni Certificazione\n                  </Typography>\n                  <Typography variant=\"body2\" color=\"text.secondary\">\n                    Numero: <strong>{selectedItem.numero_certificato}</strong>\n                  </Typography>\n                  <Typography variant=\"body2\" color=\"text.secondary\">\n                    Data: <strong>{new Date(selectedItem.data_certificazione).toLocaleDateString()}</strong>\n                  </Typography>\n                  <Typography variant=\"body2\" color=\"text.secondary\">\n                    Operatore: <strong>{selectedItem.operatore || selectedItem.id_operatore}</strong>\n                  </Typography>\n                </CardContent>\n              </Card>\n            </Grid>\n\n            <Grid item xs={12}>\n              <Card variant=\"outlined\">\n                <CardContent>\n                  <Typography variant=\"h6\" gutterBottom>\n                    Risultati Test\n                  </Typography>\n                  <Grid container spacing={2}>\n                    <Grid item xs={4}>\n                      <Typography variant=\"body2\" color=\"text.secondary\">\n                        Continuità\n                      </Typography>\n                      <Chip\n                        size=\"small\"\n                        label={selectedItem.valore_continuita}\n                        color={selectedItem.valore_continuita === 'OK' ? 'success' : 'error'}\n                      />\n                    </Grid>\n                    <Grid item xs={4}>\n                      <Typography variant=\"body2\" color=\"text.secondary\">\n                        Isolamento\n                      </Typography>\n                      <Chip\n                        size=\"small\"\n                        label={`${selectedItem.valore_isolamento} MΩ`}\n                        color={parseFloat(selectedItem.valore_isolamento) >= 500 ? 'success' : 'warning'}\n                      />\n                    </Grid>\n                    <Grid item xs={4}>\n                      <Typography variant=\"body2\" color=\"text.secondary\">\n                        Resistenza\n                      </Typography>\n                      <Chip\n                        size=\"small\"\n                        label={selectedItem.valore_resistenza}\n                        color={selectedItem.valore_resistenza === 'OK' ? 'success' : 'error'}\n                      />\n                    </Grid>\n                  </Grid>\n                </CardContent>\n              </Card>\n            </Grid>\n\n            {selectedItem.note && (\n              <Grid item xs={12}>\n                <Card variant=\"outlined\">\n                  <CardContent>\n                    <Typography variant=\"h6\" gutterBottom>\n                      Note\n                    </Typography>\n                    <Typography variant=\"body2\">\n                      {selectedItem.note}\n                    </Typography>\n                  </CardContent>\n                </Card>\n              </Grid>\n            )}\n          </Grid>\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={closeDialog}>Chiudi</Button>\n          <Button\n            onClick={() => handleGeneratePdf(selectedItem)}\n            variant=\"contained\"\n            startIcon={<PdfIcon />}\n            disabled={loading}\n          >\n            Genera PDF\n          </Button>\n        </DialogActions>\n      </Dialog>\n    );\n  };\n\n  // Renderizza le statistiche\n  const renderStats = () => {\n    const totalCavi = cavi.length;\n    const caviInstallati = cavi.filter(c => c.stato_installazione === 'INSTALLATO').length;\n    const caviCertificati = certificazioni.length;\n    const percentualeCertificazione = totalCavi > 0 ? Math.round((caviCertificati / caviInstallati) * 100) : 0;\n\n    return (\n      <Grid container spacing={2} sx={{ mb: 3 }}>\n        <Grid item xs={12} sm={6} md={3}>\n          <Card>\n            <CardContent>\n              <Typography color=\"text.secondary\" gutterBottom>\n                Cavi Totali\n              </Typography>\n              <Typography variant=\"h4\">\n                {totalCavi}\n              </Typography>\n            </CardContent>\n          </Card>\n        </Grid>\n        <Grid item xs={12} sm={6} md={3}>\n          <Card>\n            <CardContent>\n              <Typography color=\"text.secondary\" gutterBottom>\n                Cavi Installati\n              </Typography>\n              <Typography variant=\"h4\">\n                {caviInstallati}\n              </Typography>\n            </CardContent>\n          </Card>\n        </Grid>\n        <Grid item xs={12} sm={6} md={3}>\n          <Card>\n            <CardContent>\n              <Typography color=\"text.secondary\" gutterBottom>\n                Certificazioni\n              </Typography>\n              <Typography variant=\"h4\">\n                {caviCertificati}\n              </Typography>\n            </CardContent>\n          </Card>\n        </Grid>\n        <Grid item xs={12} sm={6} md={3}>\n          <Card>\n            <CardContent>\n              <Typography color=\"text.secondary\" gutterBottom>\n                % Certificazione\n              </Typography>\n              <Typography variant=\"h4\" color={percentualeCertificazione >= 80 ? 'success.main' : 'warning.main'}>\n                {percentualeCertificazione}%\n              </Typography>\n            </CardContent>\n          </Card>\n        </Grid>\n      </Grid>\n    );\n  };\n\n  return (\n    <Container maxWidth=\"xl\" sx={{ py: 3 }}>\n      {/* Header con titolo e azioni rapide */}\n      <Box sx={{ mb: 4 }}>\n        <Typography variant=\"h4\" component=\"h1\" gutterBottom fontWeight=\"bold\">\n          🔌 Sistema di Certificazione Cavi\n        </Typography>\n        <Typography variant=\"body1\" color=\"text.secondary\" sx={{ mb: 2 }}>\n          Gestione completa delle certificazioni elettriche secondo standard CEI 64-8\n        </Typography>\n\n        {/* Messaggio di aiuto */}\n        <Paper sx={{ p: 2, bgcolor: 'info.light', color: 'info.contrastText', mb: 2 }}>\n          <Stack direction=\"row\" alignItems=\"center\" spacing={2}>\n            <InfoIcon />\n            <Box>\n              <Typography variant=\"body2\" fontWeight=\"bold\">\n                Come utilizzare il sistema:\n              </Typography>\n              <Typography variant=\"caption\">\n                • <strong>Tab \"Cavi da Certificare\":</strong> Visualizza tutti i cavi e clicca sul \"+\" per creare una certificazione\n                • <strong>Tab \"Certificazioni Completate\":</strong> Gestisci le certificazioni esistenti, attiva la \"Selezione\" per operazioni multiple\n                • <strong>Filtri:</strong> Usa i filtri specifici per ogni tab per trovare rapidamente quello che cerchi\n              </Typography>\n            </Box>\n          </Stack>\n        </Paper>\n      </Box>\n\n      {/* Dashboard con statistiche */}\n      {renderDashboard()}\n\n      {/* Progress bar per operazioni in corso */}\n      {(loading || operationInProgress) && (\n        <Box sx={{ mb: 2 }}>\n          <LinearProgress />\n          {progress > 0 && (\n            <Typography variant=\"caption\" color=\"text.secondary\" sx={{ mt: 1 }}>\n              Caricamento... {progress}%\n            </Typography>\n          )}\n        </Box>\n      )}\n\n      {/* Tabs per navigazione */}\n      <Paper sx={{ mb: 3 }}>\n        <Tabs\n          value={activeTab}\n          onChange={handleTabChange}\n          indicatorColor=\"primary\"\n          textColor=\"primary\"\n          variant=\"fullWidth\"\n        >\n          <Tab\n            label={\n              <Stack direction=\"row\" alignItems=\"center\" spacing={1}>\n                <CableIcon />\n                <Box>\n                  <Typography variant=\"body2\" fontWeight=\"bold\">\n                    Cavi da Certificare\n                  </Typography>\n                  <Typography variant=\"caption\" color=\"text.secondary\">\n                    {filteredCavi.length} cavi totali\n                  </Typography>\n                </Box>\n                {statistics.caviNonCertificati > 0 && (\n                  <Badge badgeContent={statistics.caviNonCertificati} color=\"warning\">\n                    <WarningIcon />\n                  </Badge>\n                )}\n              </Stack>\n            }\n          />\n          <Tab\n            label={\n              <Stack direction=\"row\" alignItems=\"center\" spacing={1}>\n                <ScienceIcon />\n                <Box>\n                  <Typography variant=\"body2\" fontWeight=\"bold\">\n                    Certificazioni Completate\n                  </Typography>\n                  <Typography variant=\"caption\" color=\"text.secondary\">\n                    {filteredCertificazioni.length} certificazioni\n                  </Typography>\n                </Box>\n                {statistics.certificazioniOggi > 0 && (\n                  <Badge badgeContent={statistics.certificazioniOggi} color=\"success\">\n                    <CheckIcon />\n                  </Badge>\n                )}\n              </Stack>\n            }\n          />\n        </Tabs>\n      </Paper>\n\n      {/* Barra di ricerca e filtri avanzati */}\n      {renderSearchAndFilters()}\n\n      {/* Contenuto delle tabs */}\n      {!loading && activeTab === 0 && renderCaviTable()}\n      {!loading && activeTab === 1 && renderCertificazioniTable()}\n\n      {/* Dialogs */}\n      {renderCertificazioneDialog()}\n      {renderViewDialog()}\n\n      {/* Snackbar per notifiche */}\n      <Snackbar\n        open={snackbar.open}\n        autoHideDuration={6000}\n        onClose={closeSnackbar}\n        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}\n      >\n        <Alert onClose={closeSnackbar} severity={snackbar.severity} sx={{ width: '100%' }}>\n          {snackbar.message}\n        </Alert>\n      </Snackbar>\n\n      {/* Speed Dial per azioni rapide */}\n      <SpeedDial\n        ariaLabel=\"Azioni rapide\"\n        sx={{ position: 'fixed', bottom: 16, right: 16 }}\n        icon={<SpeedDialIcon />}\n      >\n        <SpeedDialAction\n          icon={<AddIcon />}\n          tooltipTitle=\"Nuova Certificazione\"\n          onClick={openCreateDialog}\n        />\n        <SpeedDialAction\n          icon={<ExportIcon />}\n          tooltipTitle=\"Esporta Tutto\"\n          onClick={handleExportAll}\n        />\n        <SpeedDialAction\n          icon={<RefreshIcon />}\n          tooltipTitle=\"Aggiorna Dati\"\n          onClick={loadInitialData}\n        />\n        <SpeedDialAction\n          icon={<ReportIcon />}\n          tooltipTitle=\"Report Avanzato\"\n          onClick={() => showSnackbar('Funzionalità in sviluppo', 'info')}\n        />\n      </SpeedDial>\n    </Container>\n  );\n});\n\nexport default CertificazioneCaviImproved;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,UAAU,EAAEC,mBAAmB,QAAQ,OAAO;AACnF,SACEC,GAAG,EACHC,UAAU,EACVC,MAAM,EACNC,KAAK,EACLC,IAAI,EACJC,IAAI,EACJC,WAAW,EACXC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,SAAS,EACTC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,YAAY,EACZC,gBAAgB,EAChBC,KAAK,EACLC,KAAK,EACLC,SAAS,EACTC,SAAS,EACTC,cAAc,EACdC,SAAS,EACTC,QAAQ,EACRC,UAAU,EACVC,IAAI,EACJC,GAAG,EACHC,UAAU,EACVC,cAAc,EACdC,OAAO,EACPC,KAAK,EACLC,IAAI,EACJC,OAAO,EACPC,KAAK,EACLC,cAAc,EACdC,QAAQ,EACRC,IAAI,EACJC,QAAQ,EACRC,YAAY,EACZC,YAAY,EACZC,QAAQ,EACRC,MAAM,EACNC,OAAO,EACPC,SAAS,EACTC,GAAG,EACHC,SAAS,EACTC,eAAe,EACfC,aAAa,QACR,eAAe;AACtB,SACEC,GAAG,IAAIC,OAAO,EACdC,MAAM,IAAIC,UAAU,EACpBC,UAAU,IAAIC,UAAU,EACxBC,YAAY,IAAIC,OAAO,EACvBC,QAAQ,IAAIC,YAAY,EACxBC,UAAU,IAAIC,QAAQ,EACtBC,MAAM,IAAIC,UAAU,EACpBC,IAAI,IAAIC,QAAQ,EAChBC,IAAI,IAAIC,QAAQ,EAChBC,KAAK,IAAIC,SAAS,EAClBC,KAAK,IAAIC,SAAS,EAClBC,WAAW,IAAIC,SAAS,EACxBC,OAAO,IAAIC,WAAW,EACtBC,MAAM,IAAIC,UAAU,EACpBC,KAAK,IAAIC,SAAS,EAClBC,KAAK,IAAIC,SAAS,EAClBC,WAAW,IAAIC,UAAU,EACzBC,UAAU,IAAIC,UAAU,EACxBC,QAAQ,IAAIC,YAAY,EACxBC,OAAO,IAAIC,WAAW,EACtBC,UAAU,IAAIC,cAAc,EAC5BC,UAAU,IAAIC,cAAc,EAC5BC,IAAI,IAAIC,QAAQ,EAChBC,KAAK,IAAIC,SAAS,EAClBC,QAAQ,IAAIC,YAAY,EACxBC,MAAM,IAAIC,UAAU,EACpBC,KAAK,IAAIC,SAAS,EAClBC,OAAO,IAAIC,WAAW,EACtBC,KAAK,IAAIC,SAAS,QACb,qBAAqB;AAE5B,OAAOC,qBAAqB,MAAM,sCAAsC;AACxE,OAAOC,WAAW,MAAM,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAErD,MAAMC,0BAA0B,gBAAAC,EAAA,cAAGpH,UAAU,CAAAqH,EAAA,GAAAD,EAAA,CAAC,CAAC;EAAEE,UAAU;EAAEC,SAAS;EAAEC;AAAQ,CAAC,EAAEC,GAAG,KAAK;EAAAL,EAAA;EACzF;EACA,MAAM,CAACM,OAAO,EAAEC,UAAU,CAAC,GAAG7H,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC8H,SAAS,EAAEC,YAAY,CAAC,GAAG/H,QAAQ,CAAC,CAAC,CAAC;EAC7C,MAAM,CAACgI,cAAc,EAAEC,iBAAiB,CAAC,GAAGjI,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAACkI,IAAI,EAAEC,OAAO,CAAC,GAAGnI,QAAQ,CAAC,EAAE,CAAC;EACpC,MAAM,CAACoI,SAAS,EAAEC,YAAY,CAAC,GAAGrI,QAAQ,CAAC,EAAE,CAAC;;EAE9C;EACA,MAAM,CAACsI,UAAU,EAAEC,aAAa,CAAC,GAAGvI,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACwI,YAAY,EAAEC,eAAe,CAAC,GAAGzI,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAAC0I,sBAAsB,EAAEC,yBAAyB,CAAC,GAAG3I,QAAQ,CAAC,EAAE,CAAC;EACxE,MAAM,CAAC4I,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG7I,QAAQ,CAAC,KAAK,CAAC;EACrE,MAAM,CAAC8I,OAAO,EAAEC,UAAU,CAAC,GAAG/I,QAAQ,CAAC;IACrCgJ,KAAK,EAAE,EAAE;IACTC,SAAS,EAAE,EAAE;IACbC,SAAS,EAAE,EAAE;IACbC,UAAU,EAAE,EAAE;IACdC,QAAQ,EAAE,EAAE;IACZC,gBAAgB,EAAE,EAAE;IACpBC,aAAa,EAAE,EAAE;IACjBC,SAAS,EAAE,EAAE;IACbC,cAAc,EAAE;EAClB,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAG1J,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAAC2J,YAAY,EAAEC,eAAe,CAAC,GAAG5J,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAAC6J,MAAM,EAAEC,SAAS,CAAC,GAAG9J,QAAQ,CAAC,qBAAqB,CAAC;EAC3D,MAAM,CAAC+J,SAAS,EAAEC,YAAY,CAAC,GAAGhK,QAAQ,CAAC,MAAM,CAAC;;EAElD;EACA,MAAM,CAACiK,UAAU,EAAEC,aAAa,CAAC,GAAGlK,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACmK,UAAU,EAAEC,aAAa,CAAC,GAAGpK,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACqK,YAAY,EAAEC,eAAe,CAAC,GAAGtK,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACuK,aAAa,EAAEC,gBAAgB,CAAC,GAAGxK,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACyK,QAAQ,EAAEC,WAAW,CAAC,GAAG1K,QAAQ,CAAC,KAAK,CAAC;;EAE/C;EACA,MAAM,CAAC2K,QAAQ,EAAEC,WAAW,CAAC,GAAG5K,QAAQ,CAAC;IAAE6K,IAAI,EAAE,KAAK;IAAEC,OAAO,EAAE,EAAE;IAAEC,QAAQ,EAAE;EAAO,CAAC,CAAC;EACxF,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGjL,QAAQ,CAAC,CAAC,CAAC;EAC3C,MAAM,CAACkL,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGnL,QAAQ,CAAC,KAAK,CAAC;;EAErE;EACA,MAAM,CAACoL,QAAQ,EAAEC,WAAW,CAAC,GAAGrL,QAAQ,CAAC;IACvCsL,OAAO,EAAE,EAAE;IACXC,YAAY,EAAE,EAAE;IAChBC,YAAY,EAAE,EAAE;IAChBC,kBAAkB,EAAE,EAAE;IACtBC,iBAAiB,EAAE,IAAI;IACvBC,iBAAiB,EAAE,EAAE;IACrBC,iBAAiB,EAAE,IAAI;IACvBC,IAAI,EAAE,EAAE;IACRC,oBAAoB,EAAE,EAAE;IACxBC,OAAO,EAAE,EAAE;IACXC,cAAc,EAAE,EAAE;IAClBC,YAAY,EAAE,EAAE;IAChBC,gBAAgB,EAAE;EACpB,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGpM,QAAQ,CAAC;IAC3CqM,UAAU,EAAE,CAAC;IACbC,eAAe,EAAE,CAAC;IAClBC,kBAAkB,EAAE,CAAC;IACrBC,wBAAwB,EAAE,CAAC;IAC3BC,kBAAkB,EAAE,CAAC;IACrBC,uBAAuB,EAAE;EAC3B,CAAC,CAAC;;EAEF;EACAzM,SAAS,CAAC,MAAM;IACd0M,eAAe,CAAC,CAAC;EACnB,CAAC,EAAE,CAACnF,UAAU,CAAC,CAAC;;EAEhB;EACAvH,SAAS,CAAC,MAAM;IACd2M,UAAU,CAAC,CAAC;EACd,CAAC,EAAE,CAAC1E,IAAI,EAAEI,UAAU,EAAEQ,OAAO,EAAEe,MAAM,EAAEE,SAAS,CAAC,CAAC;;EAElD;EACA9J,SAAS,CAAC,MAAM;IACd4M,oBAAoB,CAAC,CAAC;EACxB,CAAC,EAAE,CAAC7E,cAAc,EAAEM,UAAU,EAAEQ,OAAO,EAAEe,MAAM,EAAEE,SAAS,CAAC,CAAC;;EAE5D;EACA9J,SAAS,CAAC,MAAM;IACd6M,mBAAmB,CAAC,CAAC;EACvB,CAAC,EAAE,CAAC5E,IAAI,EAAEF,cAAc,CAAC,CAAC;EAE1B,MAAM2E,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACF9E,UAAU,CAAC,IAAI,CAAC;MAChBoD,WAAW,CAAC,CAAC,CAAC;;MAEd;MACAA,WAAW,CAAC,EAAE,CAAC;MACf,MAAM8B,QAAQ,CAAC,CAAC;MAEhB9B,WAAW,CAAC,EAAE,CAAC;MACf,MAAM+B,kBAAkB,CAAC,CAAC;MAE1B/B,WAAW,CAAC,EAAE,CAAC;MACf,MAAMgC,aAAa,CAAC,CAAC;MAErBhC,WAAW,CAAC,GAAG,CAAC;MAChB6B,mBAAmB,CAAC,CAAC;IAEvB,CAAC,CAAC,OAAOI,KAAK,EAAE;MACdC,YAAY,CAAC,0CAA0C,EAAE,OAAO,CAAC;MACjEzF,OAAO,CAAC,0CAA0C,CAAC;IACrD,CAAC,SAAS;MACRG,UAAU,CAAC,KAAK,CAAC;MACjBoD,WAAW,CAAC,CAAC,CAAC;IAChB;EACF,CAAC;EAED,MAAM+B,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrC,IAAI;MACF,MAAMI,IAAI,GAAG,MAAMrG,qBAAqB,CAACsG,iBAAiB,CAAC7F,UAAU,CAAC;MACtES,iBAAiB,CAACmF,IAAI,CAAC;MACvB,OAAOA,IAAI;IACb,CAAC,CAAC,OAAOF,KAAK,EAAE;MACdI,OAAO,CAACJ,KAAK,CAAC,8CAA8C,EAAEA,KAAK,CAAC;MACpE,MAAMA,KAAK;IACb;EACF,CAAC;EAED,MAAMH,QAAQ,GAAG,MAAAA,CAAA,KAAY;IAC3B,IAAI;MACF,MAAMK,IAAI,GAAG,MAAMpG,WAAW,CAACuG,OAAO,CAAC/F,UAAU,CAAC;MAClDW,OAAO,CAACiF,IAAI,CAAC;MACb,OAAOA,IAAI;IACb,CAAC,CAAC,OAAOF,KAAK,EAAE;MACdI,OAAO,CAACJ,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;MACxD,MAAMA,KAAK;IACb;EACF,CAAC;EAED,MAAMD,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACF,MAAMG,IAAI,GAAG,MAAMrG,qBAAqB,CAACyG,YAAY,CAAChG,UAAU,CAAC;MACjEa,YAAY,CAAC+E,IAAI,CAAC;MAClB,OAAOA,IAAI;IACb,CAAC,CAAC,OAAOF,KAAK,EAAE;MACdI,OAAO,CAACJ,KAAK,CAAC,yCAAyC,EAAEA,KAAK,CAAC;MAC/D,MAAMA,KAAK;IACb;EACF,CAAC;;EAED;EACA,MAAMJ,mBAAmB,GAAGA,CAAA,KAAM;IAChC,MAAMT,UAAU,GAAGnE,IAAI,CAACuF,MAAM;IAC9B,MAAMnB,eAAe,GAAGtE,cAAc,CAACyF,MAAM;IAC7C,MAAMlB,kBAAkB,GAAGF,UAAU,GAAGC,eAAe;IACvD,MAAME,wBAAwB,GAAGH,UAAU,GAAG,CAAC,GAAGqB,IAAI,CAACC,KAAK,CAAErB,eAAe,GAAGD,UAAU,GAAI,GAAG,CAAC,GAAG,CAAC;;IAEtG;IACA,MAAMuB,IAAI,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACC,YAAY,CAAC,CAAC;IACtC,MAAMrB,kBAAkB,GAAGzE,cAAc,CAAC+F,MAAM,CAACC,IAAI,IACnD,IAAIH,IAAI,CAACG,IAAI,CAACC,mBAAmB,CAAC,CAACH,YAAY,CAAC,CAAC,KAAKF,IACxD,CAAC,CAACH,MAAM;;IAER;IACA,MAAMS,cAAc,GAAG,IAAIL,IAAI,CAAC,CAAC;IACjCK,cAAc,CAACC,OAAO,CAACD,cAAc,CAACE,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC;IACpD,MAAM1B,uBAAuB,GAAG1E,cAAc,CAAC+F,MAAM,CAACC,IAAI,IACxD,IAAIH,IAAI,CAACG,IAAI,CAACC,mBAAmB,CAAC,IAAIC,cACxC,CAAC,CAACT,MAAM;IAERrB,aAAa,CAAC;MACZC,UAAU;MACVC,eAAe;MACfC,kBAAkB;MAClBC,wBAAwB;MACxBC,kBAAkB;MAClBC;IACF,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMS,YAAY,GAAGA,CAACrC,OAAO,EAAEC,QAAQ,GAAG,MAAM,KAAK;IACnDH,WAAW,CAAC;MAAEC,IAAI,EAAE,IAAI;MAAEC,OAAO;MAAEC;IAAS,CAAC,CAAC;EAChD,CAAC;EAED,MAAMsD,aAAa,GAAGA,CAAA,KAAM;IAC1BzD,WAAW,CAAC;MAAE,GAAGD,QAAQ;MAAEE,IAAI,EAAE;IAAM,CAAC,CAAC;EAC3C,CAAC;EAED,MAAM+B,UAAU,GAAGA,CAAA,KAAM;IACvB,IAAI0B,QAAQ,GAAGpG,IAAI;;IAEnB;IACA,IAAII,UAAU,EAAE;MACd,MAAMiG,WAAW,GAAGjG,UAAU,CAACkG,WAAW,CAAC,CAAC;MAC5CF,QAAQ,GAAGA,QAAQ,CAACP,MAAM,CAACU,IAAI;QAAA,IAAAC,eAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,aAAA,EAAAC,aAAA;QAAA,OAC7BL,IAAI,CAACnD,OAAO,CAACkD,WAAW,CAAC,CAAC,CAACO,QAAQ,CAACR,WAAW,CAAC,MAAAG,eAAA,GAChDD,IAAI,CAACxF,SAAS,cAAAyF,eAAA,uBAAdA,eAAA,CAAgBF,WAAW,CAAC,CAAC,CAACO,QAAQ,CAACR,WAAW,CAAC,OAAAI,qBAAA,GACnDF,IAAI,CAACO,mBAAmB,cAAAL,qBAAA,uBAAxBA,qBAAA,CAA0BH,WAAW,CAAC,CAAC,CAACO,QAAQ,CAACR,WAAW,CAAC,OAAAK,qBAAA,GAC7DH,IAAI,CAACQ,iBAAiB,cAAAL,qBAAA,uBAAtBA,qBAAA,CAAwBJ,WAAW,CAAC,CAAC,CAACO,QAAQ,CAACR,WAAW,CAAC,OAAAM,aAAA,GAC3DJ,IAAI,CAACS,OAAO,cAAAL,aAAA,uBAAZA,aAAA,CAAcL,WAAW,CAAC,CAAC,CAACO,QAAQ,CAACR,WAAW,CAAC,OAAAO,aAAA,GACjDL,IAAI,CAACU,OAAO,cAAAL,aAAA,uBAAZA,aAAA,CAAcN,WAAW,CAAC,CAAC,CAACO,QAAQ,CAACR,WAAW,CAAC;MAAA,CACnD,CAAC;IACH;;IAEA;IACA,IAAIzF,OAAO,CAACE,KAAK,EAAE;MACjBsF,QAAQ,GAAGA,QAAQ,CAACP,MAAM,CAACU,IAAI,IAAIA,IAAI,CAACW,mBAAmB,KAAKtG,OAAO,CAACE,KAAK,CAAC;IAChF;IACA,IAAIF,OAAO,CAACG,SAAS,EAAE;MACrBqF,QAAQ,GAAGA,QAAQ,CAACP,MAAM,CAACU,IAAI,IAAIA,IAAI,CAACxF,SAAS,KAAKH,OAAO,CAACG,SAAS,CAAC;IAC1E;;IAEA;IACA,IAAIH,OAAO,CAACU,cAAc,EAAE;MAC1B,IAAIV,OAAO,CAACU,cAAc,KAAK,aAAa,EAAE;QAC5C8E,QAAQ,GAAGA,QAAQ,CAACP,MAAM,CAACU,IAAI,IAC7BzG,cAAc,CAACqH,IAAI,CAACrB,IAAI,IAAIA,IAAI,CAAC1C,OAAO,KAAKmD,IAAI,CAACnD,OAAO,CAC3D,CAAC;MACH,CAAC,MAAM,IAAIxC,OAAO,CAACU,cAAc,KAAK,iBAAiB,EAAE;QACvD8E,QAAQ,GAAGA,QAAQ,CAACP,MAAM,CAACU,IAAI,IAC7B,CAACzG,cAAc,CAACqH,IAAI,CAACrB,IAAI,IAAIA,IAAI,CAAC1C,OAAO,KAAKmD,IAAI,CAACnD,OAAO,CAC5D,CAAC;MACH;IACF;;IAEA;IACAgD,QAAQ,CAACgB,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;MACtB,IAAIC,MAAM,GAAGF,CAAC,CAAC1F,MAAM,CAAC;MACtB,IAAI6F,MAAM,GAAGF,CAAC,CAAC3F,MAAM,CAAC;MAEtB,IAAI,OAAO4F,MAAM,KAAK,QAAQ,EAAE;QAC9BA,MAAM,GAAGA,MAAM,CAACjB,WAAW,CAAC,CAAC;QAC7BkB,MAAM,GAAGA,MAAM,CAAClB,WAAW,CAAC,CAAC;MAC/B;MAEA,IAAIzE,SAAS,KAAK,KAAK,EAAE;QACvB,OAAO0F,MAAM,GAAGC,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC;MACjC,CAAC,MAAM;QACL,OAAOD,MAAM,GAAGC,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC;MACjC;IACF,CAAC,CAAC;IAEFjH,eAAe,CAAC6F,QAAQ,CAAC;EAC3B,CAAC;EAED,MAAMzB,oBAAoB,GAAGA,CAAA,KAAM;IACjC,IAAIyB,QAAQ,GAAGtG,cAAc;;IAE7B;IACA,IAAIM,UAAU,EAAE;MACd,MAAMiG,WAAW,GAAGjG,UAAU,CAACkG,WAAW,CAAC,CAAC;MAC5CF,QAAQ,GAAGA,QAAQ,CAACP,MAAM,CAACC,IAAI;QAAA,IAAA2B,eAAA,EAAAC,qBAAA,EAAAC,UAAA;QAAA,OAC7B7B,IAAI,CAAC1C,OAAO,CAACkD,WAAW,CAAC,CAAC,CAACO,QAAQ,CAACR,WAAW,CAAC,MAAAoB,eAAA,GAChD3B,IAAI,CAAC9E,SAAS,cAAAyG,eAAA,uBAAdA,eAAA,CAAgBnB,WAAW,CAAC,CAAC,CAACO,QAAQ,CAACR,WAAW,CAAC,OAAAqB,qBAAA,GACnD5B,IAAI,CAAC8B,kBAAkB,cAAAF,qBAAA,uBAAvBA,qBAAA,CAAyBpB,WAAW,CAAC,CAAC,CAACO,QAAQ,CAACR,WAAW,CAAC,OAAAsB,UAAA,GAC5D7B,IAAI,CAACnC,IAAI,cAAAgE,UAAA,uBAATA,UAAA,CAAWrB,WAAW,CAAC,CAAC,CAACO,QAAQ,CAACR,WAAW,CAAC;MAAA,CAChD,CAAC;IACH;;IAEA;IACA,IAAIzF,OAAO,CAACI,SAAS,EAAE;MACrBoF,QAAQ,GAAGA,QAAQ,CAACP,MAAM,CAACC,IAAI,IAAIA,IAAI,CAAC9E,SAAS,KAAKJ,OAAO,CAACI,SAAS,CAAC;IAC1E;IACA,IAAIJ,OAAO,CAACS,SAAS,EAAE;MACrB+E,QAAQ,GAAGA,QAAQ,CAACP,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACzE,SAAS,KAAKT,OAAO,CAACS,SAAS,CAAC;IAC1E;IACA,IAAIT,OAAO,CAACQ,aAAa,EAAE;MACzBgF,QAAQ,GAAGA,QAAQ,CAACP,MAAM,CAACC,IAAI,IAAIA,IAAI,CAAC9B,gBAAgB,KAAKpD,OAAO,CAACQ,aAAa,CAAC;IACrF;IACA,IAAIR,OAAO,CAACK,UAAU,EAAE;MACtBmF,QAAQ,GAAGA,QAAQ,CAACP,MAAM,CAACC,IAAI,IAC7B,IAAIH,IAAI,CAACG,IAAI,CAACC,mBAAmB,CAAC,IAAI,IAAIJ,IAAI,CAAC/E,OAAO,CAACK,UAAU,CACnE,CAAC;IACH;IACA,IAAIL,OAAO,CAACM,QAAQ,EAAE;MACpBkF,QAAQ,GAAGA,QAAQ,CAACP,MAAM,CAACC,IAAI,IAC7B,IAAIH,IAAI,CAACG,IAAI,CAACC,mBAAmB,CAAC,IAAI,IAAIJ,IAAI,CAAC/E,OAAO,CAACM,QAAQ,CACjE,CAAC;IACH;IACA,IAAIN,OAAO,CAACO,gBAAgB,EAAE;MAC5B,MAAM0G,MAAM,GAAGC,UAAU,CAAClH,OAAO,CAACO,gBAAgB,CAAC;MACnDiF,QAAQ,GAAGA,QAAQ,CAACP,MAAM,CAACC,IAAI,IAC7BgC,UAAU,CAAChC,IAAI,CAACrC,iBAAiB,CAAC,IAAIoE,MACxC,CAAC;IACH;;IAEA;IACAzB,QAAQ,CAACgB,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;MACtB,IAAIC,MAAM,GAAGF,CAAC,CAAC1F,MAAM,CAAC;MACtB,IAAI6F,MAAM,GAAGF,CAAC,CAAC3F,MAAM,CAAC;MAEtB,IAAIA,MAAM,KAAK,qBAAqB,EAAE;QACpC4F,MAAM,GAAG,IAAI5B,IAAI,CAAC4B,MAAM,CAAC;QACzBC,MAAM,GAAG,IAAI7B,IAAI,CAAC6B,MAAM,CAAC;MAC3B,CAAC,MAAM,IAAI,OAAOD,MAAM,KAAK,QAAQ,EAAE;QACrCA,MAAM,GAAGA,MAAM,CAACjB,WAAW,CAAC,CAAC;QAC7BkB,MAAM,GAAGA,MAAM,CAAClB,WAAW,CAAC,CAAC;MAC/B;MAEA,IAAIzE,SAAS,KAAK,KAAK,EAAE;QACvB,OAAO0F,MAAM,GAAGC,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC;MACjC,CAAC,MAAM;QACL,OAAOD,MAAM,GAAGC,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC;MACjC;IACF,CAAC,CAAC;IAEF/G,yBAAyB,CAAC2F,QAAQ,CAAC;EACrC,CAAC;;EAED;EACA,MAAM2B,cAAc,GAAGA,CAAA,KAAM;IAC3B,IAAInI,SAAS,KAAK,CAAC,EAAE;MACnBqF,YAAY,CAAC,gEAAgE,EAAE,SAAS,CAAC;MACzF;IACF;IACAzC,WAAW,CAAC,CAACD,QAAQ,CAAC;IACtBD,gBAAgB,CAAC,EAAE,CAAC;IACpB2C,YAAY,CACV,CAAC1C,QAAQ,GACL,4EAA4E,GAC5E,gCAAgC,EACpC,MACF,CAAC;EACH,CAAC;EAED,MAAMyF,mBAAmB,GAAIC,MAAM,IAAK;IACtC3F,gBAAgB,CAAC4F,IAAI,IAAI;MACvB,MAAMC,YAAY,GAAGD,IAAI,CAACrB,QAAQ,CAACoB,MAAM,CAAC,GACtCC,IAAI,CAACrC,MAAM,CAACuC,EAAE,IAAIA,EAAE,KAAKH,MAAM,CAAC,GAChC,CAAC,GAAGC,IAAI,EAAED,MAAM,CAAC;MAErBhD,YAAY,CACV,GAAGkD,YAAY,CAAC5C,MAAM,6BAA6B,EACnD,MACF,CAAC;MACD,OAAO4C,YAAY;IACrB,CAAC,CAAC;EACJ,CAAC;EAED,MAAME,cAAc,GAAGA,CAAA,KAAM;IAC3B,IAAIzI,SAAS,KAAK,CAAC,EAAE;IAErB,MAAM0I,MAAM,GAAG9H,sBAAsB,CAAC+H,GAAG,CAACzC,IAAI,IAAIA,IAAI,CAAC0C,iBAAiB,CAAC;IACzElG,gBAAgB,CAACgG,MAAM,CAAC;IACxBrD,YAAY,CAAC,YAAYqD,MAAM,CAAC/C,MAAM,6BAA6B,EAAE,SAAS,CAAC;EACjF,CAAC;EAED,MAAMkD,cAAc,GAAGA,CAAA,KAAM;IAC3BnG,gBAAgB,CAAC,EAAE,CAAC;IACpB2C,YAAY,CAAC,sBAAsB,EAAE,MAAM,CAAC;EAC9C,CAAC;;EAED;EACA,MAAMyD,iBAAiB,GAAIC,MAAM,IAAK;IACpC,OAAO7I,cAAc,CAACqH,IAAI,CAACrB,IAAI,IAAIA,IAAI,CAAC1C,OAAO,KAAKuF,MAAM,CAAC;EAC7D,CAAC;;EAED;EACA,MAAMC,oBAAoB,GAAIrC,IAAI,IAAK;IACrC;IACA,MAAMsC,YAAY,GAAGtC,IAAI,CAACW,mBAAmB,KAAK,YAAY,IAC1CX,IAAI,CAACW,mBAAmB,KAAK,YAAY,IACzCX,IAAI,CAACW,mBAAmB,KAAK,QAAQ;;IAEzD;IACA;IACA,MAAM4B,WAAW,GAAGvC,IAAI,CAACwC,YAAY,KAAK,CAAC;;IAE3C;IACA,MAAMC,eAAe,GAAGzC,IAAI,CAAC0C,qBAAqB,IAAI1C,IAAI,CAAC2C,mBAAmB;IAE9E,OAAOL,YAAY,IAAIC,WAAW,IAAIE,eAAe;EACvD,CAAC;;EAED;EACA,MAAMG,gCAAgC,GAAI5C,IAAI,IAAK;IACjD,MAAMsC,YAAY,GAAGtC,IAAI,CAACW,mBAAmB,KAAK,YAAY,IAC1CX,IAAI,CAACW,mBAAmB,KAAK,YAAY,IACzCX,IAAI,CAACW,mBAAmB,KAAK,QAAQ;IACzD,MAAM4B,WAAW,GAAGvC,IAAI,CAACwC,YAAY,KAAK,CAAC;IAC3C,MAAMC,eAAe,GAAGzC,IAAI,CAAC0C,qBAAqB,IAAI1C,IAAI,CAAC2C,mBAAmB;IAE9E,IAAI,CAACL,YAAY,EAAE;MACjB,OAAO,kEAAkE;IAC3E;IACA,IAAI,CAACC,WAAW,EAAE;MAChB,MAAMM,iBAAiB,GAAG7C,IAAI,CAACwC,YAAY,KAAK,CAAC,GAAG,eAAe,GAC1CxC,IAAI,CAACwC,YAAY,KAAK,CAAC,GAAG,8BAA8B,GACxDxC,IAAI,CAACwC,YAAY,KAAK,CAAC,GAAG,4BAA4B,GACtD,gCAAgC;MACzD,OAAO,kEAAkEK,iBAAiB,GAAG;IAC/F;IACA,IAAI,CAACJ,eAAe,EAAE;MACpB,MAAMK,QAAQ,GAAG,EAAE;MACnB,IAAI,CAAC9C,IAAI,CAAC0C,qBAAqB,EAAEI,QAAQ,CAACC,IAAI,CAAC,UAAU,CAAC;MAC1D,IAAI,CAAC/C,IAAI,CAAC2C,mBAAmB,EAAEG,QAAQ,CAACC,IAAI,CAAC,QAAQ,CAAC;MACtD,OAAO,+BAA+BD,QAAQ,CAACE,IAAI,CAAC,IAAI,CAAC,EAAE;IAC7D;IACA,OAAO,+CAA+C;EACxD,CAAC;;EAED;EACA,MAAMC,eAAe,GAAGA,CAACC,KAAK,EAAEC,QAAQ,KAAK;IAC3C7J,YAAY,CAAC6J,QAAQ,CAAC;IACtBlI,cAAc,CAAC,CAAC,CAAC;IACjBnB,aAAa,CAAC,EAAE,CAAC;IACjBQ,UAAU,CAAC;MAAEC,KAAK,EAAE,EAAE;MAAEC,SAAS,EAAE,EAAE;MAAEC,SAAS,EAAE;IAAG,CAAC,CAAC;EACzD,CAAC;;EAED;EACA,MAAM2I,gBAAgB,GAAGA,CAACC,kBAAkB,GAAG,IAAI,KAAK;IACtD1H,aAAa,CAAC,QAAQ,CAAC;IACvBE,eAAe,CAAC,IAAI,CAAC;;IAErB;IACA,IAAIwH,kBAAkB,EAAE;MACtBzG,WAAW,CAAC;QACVC,OAAO,EAAEwG,kBAAkB,CAACxG,OAAO;QACnCC,YAAY,EAAE,EAAE;QAChBC,YAAY,EAAE,EAAE;QAChBC,kBAAkB,EAAEqG,kBAAkB,CAACC,eAAe,IAAID,kBAAkB,CAACE,aAAa,IAAI,EAAE;QAChGtG,iBAAiB,EAAE,IAAI;QACvBC,iBAAiB,EAAE,EAAE;QACrBC,iBAAiB,EAAE,IAAI;QACvBC,IAAI,EAAE,EAAE;QACRC,oBAAoB,EAAE,EAAE;QACxBC,OAAO,EAAE,EAAE;QACXC,cAAc,EAAE,EAAE;QAClBC,YAAY,EAAE,EAAE;QAChBC,gBAAgB,EAAE;MACpB,CAAC,CAAC;MACFiB,YAAY,CAAC,QAAQ2E,kBAAkB,CAACxG,OAAO,8BAA8B,EAAE,SAAS,CAAC;IAC3F,CAAC,MAAM;MACL;MACAD,WAAW,CAAC;QACVC,OAAO,EAAE,EAAE;QACXC,YAAY,EAAE,EAAE;QAChBC,YAAY,EAAE,EAAE;QAChBC,kBAAkB,EAAE,EAAE;QACtBC,iBAAiB,EAAE,IAAI;QACvBC,iBAAiB,EAAE,EAAE;QACrBC,iBAAiB,EAAE,IAAI;QACvBC,IAAI,EAAE,EAAE;QACRC,oBAAoB,EAAE,EAAE;QACxBC,OAAO,EAAE,EAAE;QACXC,cAAc,EAAE,EAAE;QAClBC,YAAY,EAAE,EAAE;QAChBC,gBAAgB,EAAE;MACpB,CAAC,CAAC;IACJ;IAEAhC,aAAa,CAAC,IAAI,CAAC;EACrB,CAAC;EAED,MAAM+H,WAAW,GAAGA,CAAA,KAAM;IACxB/H,aAAa,CAAC,KAAK,CAAC;IACpBI,eAAe,CAAC,IAAI,CAAC;IACrBF,aAAa,CAAC,EAAE,CAAC;EACnB,CAAC;;EAED;EACA,MAAM8H,gBAAgB,GAAGA,CAACC,KAAK,EAAEC,KAAK,KAAK;IACzC/G,WAAW,CAAC+E,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAAC+B,KAAK,GAAGC;IACX,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMC,gBAAgB,GAAI5D,IAAI,IAAK;IACjCpD,WAAW,CAAC+E,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP9E,OAAO,EAAEmD,IAAI,CAACnD,OAAO;MACrBG,kBAAkB,EAAEgD,IAAI,CAACsD,eAAe,IAAItD,IAAI,CAACuD,aAAa,IAAI;IACpE,CAAC,CAAC,CAAC;EACL,CAAC;;EAED;EACA,MAAMM,0BAA0B,GAAG,MAAAA,CAAA,KAAY;IAC7C,IAAI;MACF,IAAI,CAAClH,QAAQ,CAACE,OAAO,IAAI,CAACF,QAAQ,CAACG,YAAY,IAAI,CAACH,QAAQ,CAACI,YAAY,IAAI,CAACJ,QAAQ,CAACO,iBAAiB,EAAE;QACxGwB,YAAY,CAAC,mCAAmC,EAAE,SAAS,CAAC;QAC5D;MACF;;MAEA;MACA,MAAMsB,IAAI,GAAGvG,IAAI,CAACqK,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAClH,OAAO,KAAKF,QAAQ,CAACE,OAAO,CAAC;MAC3D,IAAI,CAACmD,IAAI,EAAE;QACTtB,YAAY,CAAC,kBAAkB,EAAE,OAAO,CAAC;QACzC;MACF;MAEA,IAAI,CAAC2D,oBAAoB,CAACrC,IAAI,CAAC,EAAE;QAC/B,MAAMgE,SAAS,GAAGpB,gCAAgC,CAAC5C,IAAI,CAAC;QACxDtB,YAAY,CAAC,oCAAoCsF,SAAS,EAAE,EAAE,OAAO,CAAC;QACtE;MACF;;MAEA;MACA,IAAI7B,iBAAiB,CAACxF,QAAQ,CAACE,OAAO,CAAC,EAAE;QACvC6B,YAAY,CAAC,iCAAiC,EAAE,SAAS,CAAC;QAC1D;MACF;MAEAhC,sBAAsB,CAAC,IAAI,CAAC;MAC5B,MAAMpE,qBAAqB,CAAC2L,oBAAoB,CAAClL,UAAU,EAAE4D,QAAQ,CAAC;MACtE+B,YAAY,CAAC,oCAAoC,EAAE,SAAS,CAAC;MAC7D8E,WAAW,CAAC,CAAC;MACb,MAAMjF,kBAAkB,CAAC,CAAC;MAC1BF,mBAAmB,CAAC,CAAC;IACvB,CAAC,CAAC,OAAOI,KAAK,EAAE;MACdC,YAAY,CAAC,+CAA+C,IAAID,KAAK,CAACpC,OAAO,IAAI,oBAAoB,CAAC,EAAE,OAAO,CAAC;IAClH,CAAC,SAAS;MACRK,sBAAsB,CAAC,KAAK,CAAC;IAC/B;EACF,CAAC;EAED,MAAMwH,iBAAiB,GAAG,MAAOnJ,cAAc,IAAK;IAClD,IAAI;MACF2B,sBAAsB,CAAC,IAAI,CAAC;MAC5BgC,YAAY,CAAC,6BAA6B,EAAE,MAAM,CAAC;MAEnD,MAAMyF,QAAQ,GAAG,MAAM7L,qBAAqB,CAAC8L,WAAW,CAACrL,UAAU,EAAEgC,cAAc,CAACkH,iBAAiB,CAAC;MAEtG,IAAIkC,QAAQ,CAACE,QAAQ,EAAE;QACrB;QACA,MAAMC,SAAS,GAAGC,MAAM,CAACnI,IAAI,CAAC+H,QAAQ,CAACE,QAAQ,EAAE,QAAQ,CAAC;QAC1D,IAAIC,SAAS,EAAE;UACb5F,YAAY,CAAC,6CAA6C,EAAE,SAAS,CAAC;QACxE,CAAC,MAAM;UACL;UACA,MAAM8F,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;UACxCF,IAAI,CAACG,IAAI,GAAGR,QAAQ,CAACE,QAAQ;UAC7BG,IAAI,CAACI,QAAQ,GAAG,kBAAkB7J,cAAc,CAACsG,kBAAkB,MAAM;UACzEoD,QAAQ,CAACI,IAAI,CAACC,WAAW,CAACN,IAAI,CAAC;UAC/BA,IAAI,CAACO,KAAK,CAAC,CAAC;UACZN,QAAQ,CAACI,IAAI,CAACG,WAAW,CAACR,IAAI,CAAC;UAC/B9F,YAAY,CAAC,uCAAuC,EAAE,SAAS,CAAC;QAClE;MACF,CAAC,MAAM,IAAIyF,QAAQ,CAACc,WAAW,EAAE;QAC/B;QACA,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACC,IAAI,CAACjB,QAAQ,CAACc,WAAW,CAAC,CAAC,EAAE;UAAEI,IAAI,EAAE;QAAkB,CAAC,CAAC;QAChF,MAAMC,GAAG,GAAGC,GAAG,CAACC,eAAe,CAACN,IAAI,CAAC;QACrC,MAAMV,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;QACxCF,IAAI,CAACG,IAAI,GAAGW,GAAG;QACfd,IAAI,CAACI,QAAQ,GAAG,kBAAkB7J,cAAc,CAACsG,kBAAkB,MAAM;QACzEoD,QAAQ,CAACI,IAAI,CAACC,WAAW,CAACN,IAAI,CAAC;QAC/BA,IAAI,CAACO,KAAK,CAAC,CAAC;QACZN,QAAQ,CAACI,IAAI,CAACG,WAAW,CAACR,IAAI,CAAC;QAC/Be,GAAG,CAACE,eAAe,CAACH,GAAG,CAAC;QACxB5G,YAAY,CAAC,4BAA4B,EAAE,SAAS,CAAC;MACvD,CAAC,MAAM;QACLA,YAAY,CAAC,sCAAsC,EAAE,OAAO,CAAC;MAC/D;IACF,CAAC,CAAC,OAAOD,KAAK,EAAE;MACdI,OAAO,CAACJ,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/CC,YAAY,CAAC,oCAAoC,IAAID,KAAK,CAACpC,OAAO,IAAI,oBAAoB,CAAC,EAAE,OAAO,CAAC;IACvG,CAAC,SAAS;MACRK,sBAAsB,CAAC,KAAK,CAAC;IAC/B;EACF,CAAC;EAED,MAAMgJ,0BAA0B,GAAG,MAAO3K,cAAc,IAAK;IAC3D,IAAIwJ,MAAM,CAACoB,OAAO,CAAC,mDAAmD5K,cAAc,CAACsG,kBAAkB,GAAG,CAAC,EAAE;MAC3G,IAAI;QACF3E,sBAAsB,CAAC,IAAI,CAAC;QAC5B,MAAMpE,qBAAqB,CAACsN,oBAAoB,CAAC7M,UAAU,EAAEgC,cAAc,CAACkH,iBAAiB,CAAC;QAC9FvD,YAAY,CAAC,uCAAuC,EAAE,SAAS,CAAC;QAChE,MAAMH,kBAAkB,CAAC,CAAC;QAC1BF,mBAAmB,CAAC,CAAC;MACvB,CAAC,CAAC,OAAOI,KAAK,EAAE;QACdC,YAAY,CAAC,kDAAkD,IAAID,KAAK,CAACpC,OAAO,IAAI,oBAAoB,CAAC,EAAE,OAAO,CAAC;MACrH,CAAC,SAAS;QACRK,sBAAsB,CAAC,KAAK,CAAC;MAC/B;IACF;EACF,CAAC;;EAED;EACA,MAAMmJ,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI/J,aAAa,CAACkD,MAAM,KAAK,CAAC,EAAE;MAC9BN,YAAY,CAAC,8BAA8B,EAAE,SAAS,CAAC;MACvD;IACF;IAEA,IAAI6F,MAAM,CAACoB,OAAO,CAAC,iCAAiC7J,aAAa,CAACkD,MAAM,kBAAkB,CAAC,EAAE;MAC3F,IAAI;QACFtC,sBAAsB,CAAC,IAAI,CAAC;QAC5B,KAAK,MAAMmF,EAAE,IAAI/F,aAAa,EAAE;UAC9B,MAAMxD,qBAAqB,CAACsN,oBAAoB,CAAC7M,UAAU,EAAE8I,EAAE,CAAC;QAClE;QACAnD,YAAY,CAAC,GAAG5C,aAAa,CAACkD,MAAM,wCAAwC,EAAE,SAAS,CAAC;QACxFjD,gBAAgB,CAAC,EAAE,CAAC;QACpB,MAAMwC,kBAAkB,CAAC,CAAC;QAC1BF,mBAAmB,CAAC,CAAC;MACvB,CAAC,CAAC,OAAOI,KAAK,EAAE;QACdC,YAAY,CAAC,gDAAgD,EAAE,OAAO,CAAC;MACzE,CAAC,SAAS;QACRhC,sBAAsB,CAAC,KAAK,CAAC;MAC/B;IACF;EACF,CAAC;EAED,MAAMoJ,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAIhK,aAAa,CAACkD,MAAM,KAAK,CAAC,EAAE;MAC9BN,YAAY,CAAC,8BAA8B,EAAE,SAAS,CAAC;MACvD;IACF;IAEA,IAAI;MACFhC,sBAAsB,CAAC,IAAI,CAAC;MAC5B;MACA,MAAMqJ,aAAa,GAAGxM,cAAc,CAAC+F,MAAM,CAACC,IAAI,IAC9CzD,aAAa,CAACwE,QAAQ,CAACf,IAAI,CAAC0C,iBAAiB,CAC/C,CAAC;;MAED;MACA,MAAM+D,UAAU,GAAGC,WAAW,CAACF,aAAa,CAAC;MAC7CG,WAAW,CAACF,UAAU,EAAE,kBAAkB,IAAI5G,IAAI,CAAC,CAAC,CAAC+G,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;MAEvF1H,YAAY,CAAC,GAAG5C,aAAa,CAACkD,MAAM,2BAA2B,EAAE,SAAS,CAAC;IAC7E,CAAC,CAAC,OAAOP,KAAK,EAAE;MACdC,YAAY,CAAC,2BAA2B,EAAE,OAAO,CAAC;IACpD,CAAC,SAAS;MACRhC,sBAAsB,CAAC,KAAK,CAAC;IAC/B;EACF,CAAC;;EAED;EACA,MAAMuJ,WAAW,GAAItH,IAAI,IAAK;IAC5B,MAAM0H,OAAO,GAAG,CAAC,SAAS,EAAE,oBAAoB,EAAE,MAAM,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,YAAY,EAAE,WAAW,CAAC;IAC3H,MAAMC,IAAI,GAAG3H,IAAI,CAACqD,GAAG,CAACzC,IAAI,IAAI,CAC5BA,IAAI,CAAC1C,OAAO,EACZ0C,IAAI,CAAC8B,kBAAkB,EACvB,IAAIjC,IAAI,CAACG,IAAI,CAACC,mBAAmB,CAAC,CAAC+G,kBAAkB,CAAC,CAAC,EACvDhH,IAAI,CAAC9E,SAAS,EACd8E,IAAI,CAACzE,SAAS,EACdyE,IAAI,CAACvC,kBAAkB,EACvBuC,IAAI,CAACrC,iBAAiB,EACtBqC,IAAI,CAAC9B,gBAAgB,CACtB,CAAC;IAEF,OAAO,CAAC4I,OAAO,EAAE,GAAGC,IAAI,CAAC,CAACtE,GAAG,CAACwE,GAAG,IAAIA,GAAG,CAACxD,IAAI,CAAC,GAAG,CAAC,CAAC,CAACA,IAAI,CAAC,IAAI,CAAC;EAChE,CAAC;EAED,MAAMkD,WAAW,GAAGA,CAACO,OAAO,EAAEC,QAAQ,KAAK;IACzC,MAAMxB,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACsB,OAAO,CAAC,EAAE;MAAEpB,IAAI,EAAE;IAA0B,CAAC,CAAC;IACrE,MAAMb,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;IACxC,IAAIF,IAAI,CAACI,QAAQ,KAAK+B,SAAS,EAAE;MAC/B,MAAMrB,GAAG,GAAGC,GAAG,CAACC,eAAe,CAACN,IAAI,CAAC;MACrCV,IAAI,CAACoC,YAAY,CAAC,MAAM,EAAEtB,GAAG,CAAC;MAC9Bd,IAAI,CAACoC,YAAY,CAAC,UAAU,EAAEF,QAAQ,CAAC;MACvClC,IAAI,CAACqC,KAAK,CAACC,UAAU,GAAG,QAAQ;MAChCrC,QAAQ,CAACI,IAAI,CAACC,WAAW,CAACN,IAAI,CAAC;MAC/BA,IAAI,CAACO,KAAK,CAAC,CAAC;MACZN,QAAQ,CAACI,IAAI,CAACG,WAAW,CAACR,IAAI,CAAC;IACjC;EACF,CAAC;EAED,MAAMuC,eAAe,GAAGA,CAAA,KAAM;IAC5B,MAAMf,UAAU,GAAGC,WAAW,CAAChM,sBAAsB,CAAC;IACtDiM,WAAW,CAACF,UAAU,EAAE,wBAAwB,IAAI5G,IAAI,CAAC,CAAC,CAAC+G,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;IAC7F1H,YAAY,CAAC,yBAAyB,EAAE,SAAS,CAAC;EACpD,CAAC;;EAED;EACAhN,mBAAmB,CAACwH,GAAG,EAAE,OAAO;IAC9B8N,kBAAkB,EAAGC,MAAM,IAAK;MAC9B,IAAIA,MAAM,KAAK,oBAAoB,EAAE;QACnC7D,gBAAgB,CAAC,CAAC;MACpB,CAAC,MAAM,IAAI6D,MAAM,KAAK,0BAA0B,EAAE;QAChD3N,YAAY,CAAC,CAAC,CAAC;MACjB;IACF;EACF,CAAC,CAAC,CAAC;;EAEH;EACA,MAAM4N,mBAAmB,GAAIC,KAAK,IAAK;IACrC,MAAMC,UAAU,GAAG,CAACpM,WAAW,GAAG,CAAC,IAAIE,YAAY;IACnD,MAAMmM,QAAQ,GAAGD,UAAU,GAAGlM,YAAY;IAC1C,OAAOiM,KAAK,CAACG,KAAK,CAACF,UAAU,EAAEC,QAAQ,CAAC;EAC1C,CAAC;EAED,MAAME,aAAa,GAAIJ,KAAK,IAAKlI,IAAI,CAACuI,IAAI,CAACL,KAAK,CAACnI,MAAM,GAAG9D,YAAY,CAAC;;EAEvE;EACA,MAAMuM,eAAe,GAAGA,CAACC,KAAK,EAAEhE,KAAK,KAAK;IACxC,OAAO,CAAC,GAAG,IAAIiE,GAAG,CAACD,KAAK,CAAC1F,GAAG,CAAC4F,IAAI,IAAIA,IAAI,CAAClE,KAAK,CAAC,CAAC,CAACpE,MAAM,CAACuI,OAAO,CAAC,CAAC,CAAC;EACrE,CAAC;;EAED;EACA,MAAMC,eAAe,GAAGA,CAAA,kBACtBrP,OAAA,CAAC1G,IAAI;IAACgW,SAAS;IAACC,OAAO,EAAE,CAAE;IAACC,EAAE,EAAE;MAAEC,EAAE,EAAE;IAAE,CAAE;IAAAC,QAAA,gBACxC1P,OAAA,CAAC1G,IAAI;MAAC6V,IAAI;MAACQ,EAAE,EAAE,EAAG;MAACC,EAAE,EAAE,CAAE;MAAAF,QAAA,eACvB1P,OAAA,CAACzG,IAAI;QAACiW,EAAE,EAAE;UAAEK,UAAU,EAAE,mDAAmD;UAAEC,KAAK,EAAE;QAAQ,CAAE;QAAAJ,QAAA,eAC5F1P,OAAA,CAACxG,WAAW;UAACgW,EAAE,EAAE;YAAEO,SAAS,EAAE,QAAQ;YAAEC,EAAE,EAAE;UAAE,CAAE;UAAAN,QAAA,gBAC9C1P,OAAA,CAACR,SAAS;YAACgQ,EAAE,EAAE;cAAES,QAAQ,EAAE,EAAE;cAAER,EAAE,EAAE;YAAE;UAAE;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC1CrQ,OAAA,CAAC7G,UAAU;YAACmX,OAAO,EAAC,IAAI;YAACC,UAAU,EAAC,MAAM;YAAAb,QAAA,EACvCzK,UAAU,CAACE;UAAU;YAAA+K,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACZ,CAAC,eACbrQ,OAAA,CAAC7G,UAAU;YAACmX,OAAO,EAAC,OAAO;YAAAZ,QAAA,EAAC;UAE5B;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAEPrQ,OAAA,CAAC1G,IAAI;MAAC6V,IAAI;MAACQ,EAAE,EAAE,EAAG;MAACC,EAAE,EAAE,CAAE;MAAAF,QAAA,eACvB1P,OAAA,CAACzG,IAAI;QAACiW,EAAE,EAAE;UAAEK,UAAU,EAAE,mDAAmD;UAAEC,KAAK,EAAE;QAAQ,CAAE;QAAAJ,QAAA,eAC5F1P,OAAA,CAACxG,WAAW;UAACgW,EAAE,EAAE;YAAEO,SAAS,EAAE,QAAQ;YAAEC,EAAE,EAAE;UAAE,CAAE;UAAAN,QAAA,gBAC9C1P,OAAA,CAACtC,SAAS;YAAC8R,EAAE,EAAE;cAAES,QAAQ,EAAE,EAAE;cAAER,EAAE,EAAE;YAAE;UAAE;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC1CrQ,OAAA,CAAC7G,UAAU;YAACmX,OAAO,EAAC,IAAI;YAACC,UAAU,EAAC,MAAM;YAAAb,QAAA,EACvCzK,UAAU,CAACG;UAAe;YAAA8K,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjB,CAAC,eACbrQ,OAAA,CAAC7G,UAAU;YAACmX,OAAO,EAAC,OAAO;YAAAZ,QAAA,EAAC;UAE5B;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAEPrQ,OAAA,CAAC1G,IAAI;MAAC6V,IAAI;MAACQ,EAAE,EAAE,EAAG;MAACC,EAAE,EAAE,CAAE;MAAAF,QAAA,eACvB1P,OAAA,CAACzG,IAAI;QAACiW,EAAE,EAAE;UAAEK,UAAU,EAAE,mDAAmD;UAAEC,KAAK,EAAE;QAAQ,CAAE;QAAAJ,QAAA,eAC5F1P,OAAA,CAACxG,WAAW;UAACgW,EAAE,EAAE;YAAEO,SAAS,EAAE,QAAQ;YAAEC,EAAE,EAAE;UAAE,CAAE;UAAAN,QAAA,gBAC9C1P,OAAA,CAACpC,WAAW;YAAC4R,EAAE,EAAE;cAAES,QAAQ,EAAE,EAAE;cAAER,EAAE,EAAE;YAAE;UAAE;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC5CrQ,OAAA,CAAC7G,UAAU;YAACmX,OAAO,EAAC,IAAI;YAACC,UAAU,EAAC,MAAM;YAAAb,QAAA,EACvCzK,UAAU,CAACI;UAAkB;YAAA6K,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpB,CAAC,eACbrQ,OAAA,CAAC7G,UAAU;YAACmX,OAAO,EAAC,OAAO;YAAAZ,QAAA,EAAC;UAE5B;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAEPrQ,OAAA,CAAC1G,IAAI;MAAC6V,IAAI;MAACQ,EAAE,EAAE,EAAG;MAACC,EAAE,EAAE,CAAE;MAAAF,QAAA,eACvB1P,OAAA,CAACzG,IAAI;QAACiW,EAAE,EAAE;UAAEK,UAAU,EAAE,mDAAmD;UAAEC,KAAK,EAAE;QAAQ,CAAE;QAAAJ,QAAA,eAC5F1P,OAAA,CAACxG,WAAW;UAACgW,EAAE,EAAE;YAAEO,SAAS,EAAE,QAAQ;YAAEC,EAAE,EAAE;UAAE,CAAE;UAAAN,QAAA,gBAC9C1P,OAAA,CAAC1B,UAAU;YAACkR,EAAE,EAAE;cAAES,QAAQ,EAAE,EAAE;cAAER,EAAE,EAAE;YAAE;UAAE;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC3CrQ,OAAA,CAAC7G,UAAU;YAACmX,OAAO,EAAC,IAAI;YAACC,UAAU,EAAC,MAAM;YAAAb,QAAA,GACvCzK,UAAU,CAACK,wBAAwB,EAAC,GACvC;UAAA;YAAA4K,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbrQ,OAAA,CAAC7G,UAAU;YAACmX,OAAO,EAAC,OAAO;YAAAZ,QAAA,EAAC;UAE5B;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAEPrQ,OAAA,CAAC1G,IAAI;MAAC6V,IAAI;MAACQ,EAAE,EAAE,EAAG;MAACC,EAAE,EAAE,CAAE;MAAAF,QAAA,eACvB1P,OAAA,CAACzG,IAAI;QAACiW,EAAE,EAAE;UAAEK,UAAU,EAAE,mDAAmD;UAAEC,KAAK,EAAE;QAAQ,CAAE;QAAAJ,QAAA,eAC5F1P,OAAA,CAACxG,WAAW;UAACgW,EAAE,EAAE;YAAEO,SAAS,EAAE,QAAQ;YAAEC,EAAE,EAAE;UAAE,CAAE;UAAAN,QAAA,gBAC9C1P,OAAA,CAACZ,YAAY;YAACoQ,EAAE,EAAE;cAAES,QAAQ,EAAE,EAAE;cAAER,EAAE,EAAE;YAAE;UAAE;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC7CrQ,OAAA,CAAC7G,UAAU;YAACmX,OAAO,EAAC,IAAI;YAACC,UAAU,EAAC,MAAM;YAAAb,QAAA,EACvCzK,UAAU,CAACM;UAAkB;YAAA2K,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpB,CAAC,eACbrQ,OAAA,CAAC7G,UAAU;YAACmX,OAAO,EAAC,OAAO;YAAAZ,QAAA,EAAC;UAE5B;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAEPrQ,OAAA,CAAC1G,IAAI;MAAC6V,IAAI;MAACQ,EAAE,EAAE,EAAG;MAACC,EAAE,EAAE,CAAE;MAAAF,QAAA,eACvB1P,OAAA,CAACzG,IAAI;QAACiW,EAAE,EAAE;UAAEK,UAAU,EAAE,mDAAmD;UAAEC,KAAK,EAAE;QAAO,CAAE;QAAAJ,QAAA,eAC3F1P,OAAA,CAACxG,WAAW;UAACgW,EAAE,EAAE;YAAEO,SAAS,EAAE,QAAQ;YAAEC,EAAE,EAAE;UAAE,CAAE;UAAAN,QAAA,gBAC9C1P,OAAA,CAACN,WAAW;YAAC8P,EAAE,EAAE;cAAES,QAAQ,EAAE,EAAE;cAAER,EAAE,EAAE;YAAE;UAAE;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC5CrQ,OAAA,CAAC7G,UAAU;YAACmX,OAAO,EAAC,IAAI;YAACC,UAAU,EAAC,MAAM;YAAAb,QAAA,EACvCzK,UAAU,CAACO;UAAuB;YAAA0K,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzB,CAAC,eACbrQ,OAAA,CAAC7G,UAAU;YAACmX,OAAO,EAAC,OAAO;YAAAZ,QAAA,EAAC;UAE5B;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACP;;EAED;EACA,MAAMG,sBAAsB,GAAGA,CAAA,kBAC7BxQ,OAAA,CAAC3G,KAAK;IAACmW,EAAE,EAAE;MAAEiB,CAAC,EAAE,CAAC;MAAEhB,EAAE,EAAE;IAAE,CAAE;IAAAC,QAAA,gBACzB1P,OAAA,CAAC1G,IAAI;MAACgW,SAAS;MAACC,OAAO,EAAE,CAAE;MAACmB,UAAU,EAAC,QAAQ;MAAAhB,QAAA,gBAC7C1P,OAAA,CAAC1G,IAAI;QAAC6V,IAAI;QAACQ,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAF,QAAA,eACvB1P,OAAA,CAACnG,SAAS;UACR8W,SAAS;UACTC,WAAW,EAAC,0CAA0C;UACtD1F,KAAK,EAAE9J,UAAW;UAClByP,QAAQ,EAAGC,CAAC,IAAKzP,aAAa,CAACyP,CAAC,CAACC,MAAM,CAAC7F,KAAK,CAAE;UAC/C8F,UAAU,EAAE;YACVC,cAAc,eACZjR,OAAA,CAACjF,cAAc;cAACmW,QAAQ,EAAC,OAAO;cAAAxB,QAAA,eAC9B1P,OAAA,CAAC1D,UAAU;gBAAA4T,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CACjB;YACDc,YAAY,EAAE/P,UAAU,iBACtBpB,OAAA,CAACjF,cAAc;cAACmW,QAAQ,EAAC,KAAK;cAAAxB,QAAA,eAC5B1P,OAAA,CAACrF,UAAU;gBAACyW,OAAO,EAAEA,CAAA,KAAM/P,aAAa,CAAC,EAAE,CAAE;gBAACgQ,IAAI,EAAC,OAAO;gBAAA3B,QAAA,eACxD1P,OAAA,CAAC1C,SAAS;kBAAA4S,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UAEpB;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAEPrQ,OAAA,CAAC1G,IAAI;QAAC6V,IAAI;QAACQ,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAF,QAAA,eACvB1P,OAAA,CAAC5G,MAAM;UACLuX,SAAS;UACTL,OAAO,EAAC,UAAU;UAClBgB,SAAS,eAAEtR,OAAA,CAACxD,UAAU;YAAA0T,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC1Be,OAAO,EAAEA,CAAA,KAAMzP,sBAAsB,CAAC,CAACD,mBAAmB,CAAE;UAC5DoO,KAAK,EAAEyB,MAAM,CAACC,MAAM,CAAC5P,OAAO,CAAC,CAACuG,IAAI,CAACsJ,CAAC,IAAIA,CAAC,CAAC,GAAG,SAAS,GAAG,SAAU;UAAA/B,QAAA,GACpE,SACQ,EAAC6B,MAAM,CAACC,MAAM,CAAC5P,OAAO,CAAC,CAACiF,MAAM,CAAC4K,CAAC,IAAIA,CAAC,CAAC,CAAClL,MAAM,GAAG,CAAC,IAAI,IAAIgL,MAAM,CAACC,MAAM,CAAC5P,OAAO,CAAC,CAACiF,MAAM,CAAC4K,CAAC,IAAIA,CAAC,CAAC,CAAClL,MAAM,GAAG;QAAA;UAAA2J,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAEPrQ,OAAA,CAAC1G,IAAI;QAAC6V,IAAI;QAACQ,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAF,QAAA,eACvB1P,OAAA,CAAC5G,MAAM;UACLuX,SAAS;UACTL,OAAO,EAAC,UAAU;UAClBgB,SAAS,EAAE/N,QAAQ,gBAAGvD,OAAA,CAAC1C,SAAS;YAAA4S,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAAGrQ,OAAA,CAACtC,SAAS;YAAAwS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACpDe,OAAO,EAAErI,cAAe;UACxB+G,KAAK,EAAEvM,QAAQ,GAAG,WAAW,GAAG,SAAU;UAC1CmO,QAAQ,EAAE9Q,SAAS,KAAK,CAAE;UAAA8O,QAAA,EAEzBnM,QAAQ,GAAG,gBAAgB,GAAG;QAAoB;UAAA2M,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7C;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAEPrQ,OAAA,CAAC1G,IAAI;QAAC6V,IAAI;QAACQ,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAF,QAAA,eACvB1P,OAAA,CAAC5G,MAAM;UACLuX,SAAS;UACTL,OAAO,EAAC,UAAU;UAClBgB,SAAS,eAAEtR,OAAA,CAAClC,UAAU;YAAAoS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC1Be,OAAO,EAAE9C,eAAgB;UACzBoD,QAAQ,EAAE9Q,SAAS,KAAK,CAAC,IAAIY,sBAAsB,CAAC+E,MAAM,KAAK,CAAE;UAAAmJ,QAAA,EAEhE9O,SAAS,KAAK,CAAC,GAAG,8BAA8B,GAAG;QAAwB;UAAAsP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAEPrQ,OAAA,CAAC1G,IAAI;QAAC6V,IAAI;QAACQ,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAF,QAAA,eACvB1P,OAAA,CAAC5G,MAAM;UACLuX,SAAS;UACTL,OAAO,EAAC,WAAW;UACnBgB,SAAS,eAAEtR,OAAA,CAAC5D,OAAO;YAAA8T,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACvBe,OAAO,EAAEzG,gBAAiB;UAAA+E,QAAA,EAC3B;QAED;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGPrQ,OAAA,CAAC1E,QAAQ;MAACqW,EAAE,EAAEjQ,mBAAoB;MAAAgO,QAAA,gBAChC1P,OAAA,CAAChF,OAAO;QAACwU,EAAE,EAAE;UAAEoC,EAAE,EAAE;QAAE;MAAE;QAAA1B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC1BrQ,OAAA,CAAC7G,UAAU;QAACmX,OAAO,EAAC,OAAO;QAACR,KAAK,EAAC,gBAAgB;QAACN,EAAE,EAAE;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAC,QAAA,EAC9D9O,SAAS,KAAK,CAAC,GAAG,iBAAiB,GAAG;MAA2B;QAAAsP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxD,CAAC,eAEbrQ,OAAA,CAAC1G,IAAI;QAACgW,SAAS;QAACC,OAAO,EAAE,CAAE;QAAAG,QAAA,GAExB9O,SAAS,KAAK,CAAC,iBACdZ,OAAA,CAAAE,SAAA;UAAAwP,QAAA,gBACE1P,OAAA,CAAC1G,IAAI;YAAC6V,IAAI;YAACQ,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAF,QAAA,eACvB1P,OAAA,CAAClG,WAAW;cAAC6W,SAAS;cAACU,IAAI,EAAC,OAAO;cAAA3B,QAAA,gBACjC1P,OAAA,CAACjG,UAAU;gBAAA2V,QAAA,EAAC;cAAmB;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAC5CrQ,OAAA,CAAChG,MAAM;gBACLkR,KAAK,EAAEtJ,OAAO,CAACE,KAAM;gBACrB+O,QAAQ,EAAGC,CAAC,IAAKjP,UAAU,CAAC;kBAAC,GAAGD,OAAO;kBAAEE,KAAK,EAAEgP,CAAC,CAACC,MAAM,CAAC7F;gBAAK,CAAC,CAAE;gBAAAwE,QAAA,gBAEjE1P,OAAA,CAAC/F,QAAQ;kBAACiR,KAAK,EAAC,EAAE;kBAAAwE,QAAA,EAAC;gBAAK;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,eACnCrQ,OAAA,CAAC/F,QAAQ;kBAACiR,KAAK,EAAC,YAAY;kBAAAwE,QAAA,EAAC;gBAAU;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,eAClDrQ,OAAA,CAAC/F,QAAQ;kBAACiR,KAAK,EAAC,gBAAgB;kBAAAwE,QAAA,EAAC;gBAAc;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,eAC1DrQ,OAAA,CAAC/F,QAAQ;kBAACiR,KAAK,EAAC,UAAU;kBAAAwE,QAAA,EAAC;gBAAQ;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eAEPrQ,OAAA,CAAC1G,IAAI;YAAC6V,IAAI;YAACQ,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAF,QAAA,eACvB1P,OAAA,CAAClG,WAAW;cAAC6W,SAAS;cAACU,IAAI,EAAC,OAAO;cAAA3B,QAAA,gBACjC1P,OAAA,CAACjG,UAAU;gBAAA2V,QAAA,EAAC;cAAS;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAClCrQ,OAAA,CAAChG,MAAM;gBACLkR,KAAK,EAAEtJ,OAAO,CAACG,SAAU;gBACzB8O,QAAQ,EAAGC,CAAC,IAAKjP,UAAU,CAAC;kBAAC,GAAGD,OAAO;kBAAEG,SAAS,EAAE+O,CAAC,CAACC,MAAM,CAAC7F;gBAAK,CAAC,CAAE;gBAAAwE,QAAA,gBAErE1P,OAAA,CAAC/F,QAAQ;kBAACiR,KAAK,EAAC,EAAE;kBAAAwE,QAAA,EAAC;gBAAK;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,EAClC,CAAC,GAAG,IAAInB,GAAG,CAAClO,IAAI,CAACuI,GAAG,CAAC+B,CAAC,IAAIA,CAAC,CAACvJ,SAAS,CAAC,CAAC,CAAC,CAAC8E,MAAM,CAACuI,OAAO,CAAC,CAAC7F,GAAG,CAACsI,GAAG,iBAC/D7R,OAAA,CAAC/F,QAAQ;kBAAWiR,KAAK,EAAE2G,GAAI;kBAAAnC,QAAA,EAAEmC;gBAAG,GAArBA,GAAG;kBAAA3B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAA6B,CAChD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eAEPrQ,OAAA,CAAC1G,IAAI;YAAC6V,IAAI;YAACQ,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAF,QAAA,eACvB1P,OAAA,CAAClG,WAAW;cAAC6W,SAAS;cAACU,IAAI,EAAC,OAAO;cAAA3B,QAAA,gBACjC1P,OAAA,CAACjG,UAAU;gBAAA2V,QAAA,EAAC;cAAoB;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAC7CrQ,OAAA,CAAChG,MAAM;gBACLkR,KAAK,EAAEtJ,OAAO,CAACU,cAAe;gBAC9BuO,QAAQ,EAAGC,CAAC,IAAKjP,UAAU,CAAC;kBAAC,GAAGD,OAAO;kBAAEU,cAAc,EAAEwO,CAAC,CAACC,MAAM,CAAC7F;gBAAK,CAAC,CAAE;gBAAAwE,QAAA,gBAE1E1P,OAAA,CAAC/F,QAAQ;kBAACiR,KAAK,EAAC,EAAE;kBAAAwE,QAAA,EAAC;gBAAK;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,eACnCrQ,OAAA,CAAC/F,QAAQ;kBAACiR,KAAK,EAAC,aAAa;kBAAAwE,QAAA,EAAC;gBAAW;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,eACpDrQ,OAAA,CAAC/F,QAAQ;kBAACiR,KAAK,EAAC,iBAAiB;kBAAAwE,QAAA,EAAC;gBAAe;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA,eACP,CACH,EAGAzP,SAAS,KAAK,CAAC,iBACdZ,OAAA,CAAAE,SAAA;UAAAwP,QAAA,gBACE1P,OAAA,CAAC1G,IAAI;YAAC6V,IAAI;YAACQ,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAF,QAAA,eACvB1P,OAAA,CAAClG,WAAW;cAAC6W,SAAS;cAACU,IAAI,EAAC,OAAO;cAAA3B,QAAA,gBACjC1P,OAAA,CAACjG,UAAU;gBAAA2V,QAAA,EAAC;cAAS;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAClCrQ,OAAA,CAAChG,MAAM;gBACLkR,KAAK,EAAEtJ,OAAO,CAACI,SAAU;gBACzB6O,QAAQ,EAAGC,CAAC,IAAKjP,UAAU,CAAC;kBAAC,GAAGD,OAAO;kBAAEI,SAAS,EAAE8O,CAAC,CAACC,MAAM,CAAC7F;gBAAK,CAAC,CAAE;gBAAAwE,QAAA,gBAErE1P,OAAA,CAAC/F,QAAQ;kBAACiR,KAAK,EAAC,EAAE;kBAAAwE,QAAA,EAAC;gBAAK;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,EAClC,CAAC,GAAG,IAAInB,GAAG,CAACpO,cAAc,CAACyI,GAAG,CAAC+B,CAAC,IAAIA,CAAC,CAACtJ,SAAS,CAAC,CAAC,CAAC,CAAC6E,MAAM,CAACuI,OAAO,CAAC,CAAC7F,GAAG,CAACuI,EAAE,iBACxE9R,OAAA,CAAC/F,QAAQ;kBAAUiR,KAAK,EAAE4G,EAAG;kBAAApC,QAAA,EAAEoC;gBAAE,GAAlBA,EAAE;kBAAA5B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAA2B,CAC7C,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eAEPrQ,OAAA,CAAC1G,IAAI;YAAC6V,IAAI;YAACQ,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAF,QAAA,eACvB1P,OAAA,CAAClG,WAAW;cAAC6W,SAAS;cAACU,IAAI,EAAC,OAAO;cAAA3B,QAAA,gBACjC1P,OAAA,CAACjG,UAAU;gBAAA2V,QAAA,EAAC;cAAc;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACvCrQ,OAAA,CAAChG,MAAM;gBACLkR,KAAK,EAAEtJ,OAAO,CAACQ,aAAc;gBAC7ByO,QAAQ,EAAGC,CAAC,IAAKjP,UAAU,CAAC;kBAAC,GAAGD,OAAO;kBAAEQ,aAAa,EAAE0O,CAAC,CAACC,MAAM,CAAC7F;gBAAK,CAAC,CAAE;gBAAAwE,QAAA,gBAEzE1P,OAAA,CAAC/F,QAAQ;kBAACiR,KAAK,EAAC,EAAE;kBAAAwE,QAAA,EAAC;gBAAK;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,eACnCrQ,OAAA,CAAC/F,QAAQ;kBAACiR,KAAK,EAAC,UAAU;kBAAAwE,QAAA,EAAC;gBAAQ;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,eAC9CrQ,OAAA,CAAC/F,QAAQ;kBAACiR,KAAK,EAAC,cAAc;kBAAAwE,QAAA,EAAC;gBAAY;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,eACtDrQ,OAAA,CAAC/F,QAAQ;kBAACiR,KAAK,EAAC,eAAe;kBAAAwE,QAAA,EAAC;gBAAa;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eAEPrQ,OAAA,CAAC1G,IAAI;YAAC6V,IAAI;YAACQ,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAF,QAAA,eACvB1P,OAAA,CAACnG,SAAS;cACR8W,SAAS;cACTU,IAAI,EAAC,OAAO;cACZU,KAAK,EAAC,0BAAqB;cAC3BnF,IAAI,EAAC,QAAQ;cACb1B,KAAK,EAAEtJ,OAAO,CAACO,gBAAiB;cAChC0O,QAAQ,EAAGC,CAAC,IAAKjP,UAAU,CAAC;gBAAC,GAAGD,OAAO;gBAAEO,gBAAgB,EAAE2O,CAAC,CAACC,MAAM,CAAC7F;cAAK,CAAC,CAAE;cAC5E0F,WAAW,EAAC;YAAS;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAEPrQ,OAAA,CAAC1G,IAAI;YAAC6V,IAAI;YAACQ,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAF,QAAA,eACvB1P,OAAA,CAACnG,SAAS;cACR8W,SAAS;cACTU,IAAI,EAAC,OAAO;cACZU,KAAK,EAAC,aAAa;cACnBnF,IAAI,EAAC,MAAM;cACX1B,KAAK,EAAEtJ,OAAO,CAACK,UAAW;cAC1B4O,QAAQ,EAAGC,CAAC,IAAKjP,UAAU,CAAC;gBAAC,GAAGD,OAAO;gBAAEK,UAAU,EAAE6O,CAAC,CAACC,MAAM,CAAC7F;cAAK,CAAC,CAAE;cACtE8G,eAAe,EAAE;gBAAEC,MAAM,EAAE;cAAK;YAAE;cAAA/B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAEPrQ,OAAA,CAAC1G,IAAI;YAAC6V,IAAI;YAACQ,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAF,QAAA,eACvB1P,OAAA,CAACnG,SAAS;cACR8W,SAAS;cACTU,IAAI,EAAC,OAAO;cACZU,KAAK,EAAC,WAAW;cACjBnF,IAAI,EAAC,MAAM;cACX1B,KAAK,EAAEtJ,OAAO,CAACM,QAAS;cACxB2O,QAAQ,EAAGC,CAAC,IAAKjP,UAAU,CAAC;gBAAC,GAAGD,OAAO;gBAAEM,QAAQ,EAAE4O,CAAC,CAACC,MAAM,CAAC7F;cAAK,CAAC,CAAE;cACpE8G,eAAe,EAAE;gBAAEC,MAAM,EAAE;cAAK;YAAE;cAAA/B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA,eACP,CACH,eAEDrQ,OAAA,CAAC1G,IAAI;UAAC6V,IAAI;UAACQ,EAAE,EAAE,EAAG;UAAAD,QAAA,eAChB1P,OAAA,CAAC/E,KAAK;YAACiX,SAAS,EAAC,KAAK;YAAC3C,OAAO,EAAE,CAAE;YAAC4C,cAAc,EAAC,UAAU;YAAAzC,QAAA,eAC1D1P,OAAA,CAAC5G,MAAM;cACLkX,OAAO,EAAC,UAAU;cAClBe,IAAI,EAAC,OAAO;cACZD,OAAO,EAAEA,CAAA,KAAMvP,UAAU,CAAC;gBACxBC,KAAK,EAAE,EAAE;gBAAEC,SAAS,EAAE,EAAE;gBAAEC,SAAS,EAAE,EAAE;gBAAEC,UAAU,EAAE,EAAE;gBACvDC,QAAQ,EAAE,EAAE;gBAAEC,gBAAgB,EAAE,EAAE;gBAAEC,aAAa,EAAE,EAAE;gBAAEC,SAAS,EAAE,EAAE;gBACpEC,cAAc,EAAE;cAClB,CAAC,CAAE;cAAAoN,QAAA,EACJ;YAED;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,EAGV9M,QAAQ,IAAIF,aAAa,CAACkD,MAAM,GAAG,CAAC,iBACnCvG,OAAA,CAAAE,SAAA;MAAAwP,QAAA,gBACE1P,OAAA,CAAChF,OAAO;QAACwU,EAAE,EAAE;UAAEoC,EAAE,EAAE;QAAE;MAAE;QAAA1B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC1BrQ,OAAA,CAAC/E,KAAK;QAACiX,SAAS,EAAC,KAAK;QAAC3C,OAAO,EAAE,CAAE;QAACmB,UAAU,EAAC,QAAQ;QAAAhB,QAAA,gBACpD1P,OAAA,CAAC7G,UAAU;UAACmX,OAAO,EAAC,OAAO;UAAAZ,QAAA,GACxBrM,aAAa,CAACkD,MAAM,EAAC,uBACxB;QAAA;UAAA2J,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbrQ,OAAA,CAAC5G,MAAM;UACLiY,IAAI,EAAC,OAAO;UACZf,OAAO,EAAC,UAAU;UAClBc,OAAO,EAAE/H,cAAe;UAAAqG,QAAA,EACzB;QAED;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTrQ,OAAA,CAAC5G,MAAM;UACLiY,IAAI,EAAC,OAAO;UACZf,OAAO,EAAC,UAAU;UAClBc,OAAO,EAAE3H,cAAe;UAAAiG,QAAA,EACzB;QAED;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTrQ,OAAA,CAAC5G,MAAM;UACLiY,IAAI,EAAC,OAAO;UACZf,OAAO,EAAC,UAAU;UAClBgB,SAAS,eAAEtR,OAAA,CAAClC,UAAU;YAAAoS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC1Be,OAAO,EAAE/D,gBAAiB;UAAAqC,QAAA,EAC3B;QAED;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTrQ,OAAA,CAAC5G,MAAM;UACLiY,IAAI,EAAC,OAAO;UACZf,OAAO,EAAC,UAAU;UAClBR,KAAK,EAAC,OAAO;UACbwB,SAAS,eAAEtR,OAAA,CAAChD,UAAU;YAAAkT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC1Be,OAAO,EAAEhE,gBAAiB;UAAAsC,QAAA,EAC3B;QAED;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA,eACR,CACH;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACI,CACR;;EAED;EACA,MAAM+B,eAAe,GAAGA,CAAA,KAAM;IAC5B,MAAMC,YAAY,GAAG5D,mBAAmB,CAACnN,YAAY,CAAC;IAEtD,IAAIA,YAAY,CAACiF,MAAM,KAAK,CAAC,EAAE;MAC7B,oBACEvG,OAAA,CAAC5F,KAAK;QAACyJ,QAAQ,EAAC,MAAM;QAAA6L,QAAA,EACnBtO,UAAU,IAAIQ,OAAO,CAACE,KAAK,IAAIF,OAAO,CAACG,SAAS,GAC7C,4CAA4C,GAC5C;MAAyB;QAAAmO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxB,CAAC;IAEZ;IAEA,oBACErQ,OAAA,CAAAE,SAAA;MAAAwP,QAAA,gBACE1P,OAAA,CAACxF,cAAc;QAAC8X,SAAS,EAAEjZ,KAAM;QAAAqW,QAAA,eAC/B1P,OAAA,CAAC3F,KAAK;UAACgX,IAAI,EAAC,OAAO;UAAA3B,QAAA,gBACjB1P,OAAA,CAACvF,SAAS;YAAAiV,QAAA,eACR1P,OAAA,CAACtF,QAAQ;cAAAgV,QAAA,gBACP1P,OAAA,CAACzF,SAAS;gBAAAmV,QAAA,EAAC;cAAO;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC9BrQ,OAAA,CAACzF,SAAS;gBAAAmV,QAAA,EAAC;cAAS;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAChCrQ,OAAA,CAACzF,SAAS;gBAAAmV,QAAA,EAAC;cAAO;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC9BrQ,OAAA,CAACzF,SAAS;gBAAAmV,QAAA,EAAC;cAAQ;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC/BrQ,OAAA,CAACzF,SAAS;gBAAAmV,QAAA,EAAC;cAAM;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC7BrQ,OAAA,CAACzF,SAAS;gBAAAmV,QAAA,EAAC;cAAK;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC5BrQ,OAAA,CAACzF,SAAS;gBAAAmV,QAAA,EAAC;cAAK;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC5BrQ,OAAA,CAACzF,SAAS;gBAAAmV,QAAA,EAAC;cAAW;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAClCrQ,OAAA,CAACzF,SAAS;gBAAAmV,QAAA,EAAC;cAAM;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACZrQ,OAAA,CAAC1F,SAAS;YAAAoV,QAAA,EACP2C,YAAY,CAAC9I,GAAG,CAAEhC,IAAI,IAAK;cAC1B,MAAMgL,aAAa,GAAG7I,iBAAiB,CAACnC,IAAI,CAACnD,OAAO,CAAC;cACrD,MAAMoO,cAAc,GAAG5I,oBAAoB,CAACrC,IAAI,CAAC;cACjD,MAAMkL,eAAe,GAAG,CAACD,cAAc,GAAGrI,gCAAgC,CAAC5C,IAAI,CAAC,GAAG,EAAE;cAErF,oBACEvH,OAAA,CAACtF,QAAQ;gBAAAgV,QAAA,gBACP1P,OAAA,CAACzF,SAAS;kBAAAmV,QAAA,eACR1P,OAAA,CAAC7G,UAAU;oBAACmX,OAAO,EAAC,OAAO;oBAACC,UAAU,EAAC,QAAQ;oBAAAb,QAAA,EAC5CnI,IAAI,CAACnD;kBAAO;oBAAA8L,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eACZrQ,OAAA,CAACzF,SAAS;kBAAAmV,QAAA,EAAEnI,IAAI,CAACxF;gBAAS;kBAAAmO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACvCrQ,OAAA,CAACzF,SAAS;kBAAAmV,QAAA,EAAEnI,IAAI,CAACS;gBAAO;kBAAAkI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACrCrQ,OAAA,CAACzF,SAAS;kBAAAmV,QAAA,EAAEnI,IAAI,CAACO;gBAAmB;kBAAAoI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACjDrQ,OAAA,CAACzF,SAAS;kBAAAmV,QAAA,EAAEnI,IAAI,CAACQ;gBAAiB;kBAAAmI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC/CrQ,OAAA,CAACzF,SAAS;kBAAAmV,QAAA,GAAEnI,IAAI,CAACsD,eAAe,IAAItD,IAAI,CAACuD,aAAa,EAAC,IAAE;gBAAA;kBAAAoF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eACrErQ,OAAA,CAACzF,SAAS;kBAAAmV,QAAA,eACR1P,OAAA,CAAC9E,IAAI;oBACHmW,IAAI,EAAC,OAAO;oBACZU,KAAK,EAAExK,IAAI,CAACW,mBAAoB;oBAChC4H,KAAK,EAAEvI,IAAI,CAACW,mBAAmB,KAAK,YAAY,GAAG,SAAS,GAAG;kBAAU;oBAAAgI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1E;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACO,CAAC,eACZrQ,OAAA,CAACzF,SAAS;kBAAAmV,QAAA,EACP6C,aAAa,gBACZvS,OAAA,CAAC9E,IAAI;oBACHmW,IAAI,EAAC,OAAO;oBACZqB,IAAI,eAAE1S,OAAA,CAACtC,SAAS;sBAAAwS,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAE;oBACpB0B,KAAK,EAAC,aAAa;oBACnBjC,KAAK,EAAC;kBAAS;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChB,CAAC,gBAEFrQ,OAAA,CAAC9E,IAAI;oBACHmW,IAAI,EAAC,OAAO;oBACZqB,IAAI,eAAE1S,OAAA,CAACpC,WAAW;sBAAAsS,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAE;oBACtB0B,KAAK,EAAC,iBAAiB;oBACvBjC,KAAK,EAAC;kBAAS;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChB;gBACF;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACQ,CAAC,eACZrQ,OAAA,CAACzF,SAAS;kBAAAmV,QAAA,EACP6C,aAAa,gBACZvS,OAAA,CAAC7E,OAAO;oBAACwX,KAAK,EAAC,yBAAsB;oBAAAjD,QAAA,eACnC1P,OAAA,CAAC9E,IAAI;sBACHwX,IAAI,eAAE1S,OAAA,CAACtC,SAAS;wBAAAwS,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAE;sBACpB0B,KAAK,EAAC,aAAa;sBACnBjC,KAAK,EAAC,SAAS;sBACfuB,IAAI,EAAC;oBAAO;sBAAAnB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACb;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACK,CAAC,GACRmC,cAAc,gBAChBxS,OAAA,CAAC7E,OAAO;oBAACwX,KAAK,EAAC,qCAAqC;oBAAAjD,QAAA,eAClD1P,OAAA,CAACrF,UAAU;sBACT0W,IAAI,EAAC,OAAO;sBACZD,OAAO,EAAEA,CAAA,KAAMzG,gBAAgB,CAACpD,IAAI,CAAE;sBACtCuI,KAAK,EAAC,SAAS;sBAAAJ,QAAA,eAEf1P,OAAA,CAAC5D,OAAO;wBAAA8T,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACD;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC,gBAEVrQ,OAAA,CAAC7E,OAAO;oBAACwX,KAAK,EAAEF,eAAgB;oBAAA/C,QAAA,eAC9B1P,OAAA;sBAAA0P,QAAA,eACE1P,OAAA,CAACrF,UAAU;wBACT0W,IAAI,EAAC,OAAO;wBACZK,QAAQ;wBACRN,OAAO,EAAEA,CAAA,KAAMnL,YAAY,CAACwM,eAAe,EAAE,SAAS,CAAE;wBAAA/C,QAAA,eAExD1P,OAAA,CAACJ,SAAS;0BAAAsQ,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACT;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACA;gBACV;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACQ,CAAC;cAAA,GApEC9I,IAAI,CAACnD,OAAO;gBAAA8L,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAqEjB,CAAC;YAEf,CAAC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC,EAEhBvB,aAAa,CAACxN,YAAY,CAAC,GAAG,CAAC,iBAC9BtB,OAAA,CAAC9G,GAAG;QAACsW,EAAE,EAAE;UAAEoD,OAAO,EAAE,MAAM;UAAET,cAAc,EAAE,QAAQ;UAAEU,EAAE,EAAE;QAAE,CAAE;QAAAnD,QAAA,eAC5D1P,OAAA,CAAClF,UAAU;UACTgY,KAAK,EAAEhE,aAAa,CAACxN,YAAY,CAAE;UACnCyR,IAAI,EAAExQ,WAAY;UAClBsO,QAAQ,EAAEA,CAACpG,KAAK,EAAES,KAAK,KAAK1I,cAAc,CAAC0I,KAAK,CAAE;UAClD4E,KAAK,EAAC;QAAS;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CACN;IAAA,eACD,CAAC;EAEP,CAAC;;EAED;EACA,MAAM2C,yBAAyB,GAAGA,CAAA,KAAM;IACtC,MAAMX,YAAY,GAAG5D,mBAAmB,CAACjN,sBAAsB,CAAC;IAEhE,IAAIA,sBAAsB,CAAC+E,MAAM,KAAK,CAAC,EAAE;MACvC,oBACEvG,OAAA,CAAC5F,KAAK;QAACyJ,QAAQ,EAAC,MAAM;QAAA6L,QAAA,EACnBtO,UAAU,IAAIQ,OAAO,CAACI,SAAS,GAC5B,uDAAuD,GACvD;MAAoC;QAAAkO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnC,CAAC;IAEZ;IAEA,oBACErQ,OAAA,CAAAE,SAAA;MAAAwP,QAAA,gBACE1P,OAAA,CAACxF,cAAc;QAAC8X,SAAS,EAAEjZ,KAAM;QAAAqW,QAAA,eAC/B1P,OAAA,CAAC3F,KAAK;UAACgX,IAAI,EAAC,OAAO;UAAA3B,QAAA,gBACjB1P,OAAA,CAACvF,SAAS;YAAAiV,QAAA,eACR1P,OAAA,CAACtF,QAAQ;cAAAgV,QAAA,GACNnM,QAAQ,iBACPvD,OAAA,CAACzF,SAAS;gBAAC0Y,OAAO,EAAC,UAAU;gBAAAvD,QAAA,eAC3B1P,OAAA,CAACrF,UAAU;kBACT0W,IAAI,EAAC,OAAO;kBACZD,OAAO,EAAE/N,aAAa,CAACkD,MAAM,KAAK/E,sBAAsB,CAAC+E,MAAM,GAAGkD,cAAc,GAAGJ,cAAe;kBAAAqG,QAAA,EAEjGrM,aAAa,CAACkD,MAAM,KAAK/E,sBAAsB,CAAC+E,MAAM,gBAAGvG,OAAA,CAAC1C,SAAS;oBAAA4S,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,gBAAGrQ,OAAA,CAACtC,SAAS;oBAAAwS,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7E;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CACZ,eACDrQ,OAAA,CAACzF,SAAS;gBAAAmV,QAAA,eACR1P,OAAA,CAAC/E,KAAK;kBAACiX,SAAS,EAAC,KAAK;kBAACxB,UAAU,EAAC,QAAQ;kBAACnB,OAAO,EAAE,CAAE;kBAAAG,QAAA,gBACpD1P,OAAA,CAAC7G,UAAU;oBAACmX,OAAO,EAAC,OAAO;oBAACC,UAAU,EAAC,MAAM;oBAAAb,QAAA,EAAC;kBAAc;oBAAAQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACzErQ,OAAA,CAACrF,UAAU;oBAAC0W,IAAI,EAAC,OAAO;oBAACD,OAAO,EAAEA,CAAA,KAAM;sBACtCxO,SAAS,CAAC,oBAAoB,CAAC;sBAC/BE,YAAY,CAACD,SAAS,KAAK,KAAK,GAAG,MAAM,GAAG,KAAK,CAAC;oBACpD,CAAE;oBAAA6M,QAAA,EACC/M,MAAM,KAAK,oBAAoB,GAAIE,SAAS,KAAK,KAAK,gBAAG7C,OAAA,CAAClB,cAAc;sBAAAoR,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,gBAAGrQ,OAAA,CAACpB,cAAc;sBAAAsR,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,gBAAIrQ,OAAA,CAACpB,cAAc;sBAAAsR,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7G,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACZrQ,OAAA,CAACzF,SAAS;gBAAAmV,QAAA,EAAC;cAAO;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC9BrQ,OAAA,CAACzF,SAAS;gBAAAmV,QAAA,eACR1P,OAAA,CAAC/E,KAAK;kBAACiX,SAAS,EAAC,KAAK;kBAACxB,UAAU,EAAC,QAAQ;kBAACnB,OAAO,EAAE,CAAE;kBAAAG,QAAA,gBACpD1P,OAAA,CAAC7G,UAAU;oBAACmX,OAAO,EAAC,OAAO;oBAACC,UAAU,EAAC,MAAM;oBAAAb,QAAA,EAAC;kBAAI;oBAAAQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eAC/DrQ,OAAA,CAACrF,UAAU;oBAAC0W,IAAI,EAAC,OAAO;oBAACD,OAAO,EAAEA,CAAA,KAAM;sBACtCxO,SAAS,CAAC,qBAAqB,CAAC;sBAChCE,YAAY,CAACD,SAAS,KAAK,KAAK,GAAG,MAAM,GAAG,KAAK,CAAC;oBACpD,CAAE;oBAAA6M,QAAA,EACC/M,MAAM,KAAK,qBAAqB,GAAIE,SAAS,KAAK,KAAK,gBAAG7C,OAAA,CAAClB,cAAc;sBAAAoR,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,gBAAGrQ,OAAA,CAACpB,cAAc;sBAAAsR,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,gBAAIrQ,OAAA,CAACpB,cAAc;sBAAAsR,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9G,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACZrQ,OAAA,CAACzF,SAAS;gBAAAmV,QAAA,EAAC;cAAS;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAChCrQ,OAAA,CAACzF,SAAS;gBAAAmV,QAAA,EAAC;cAAS;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAChCrQ,OAAA,CAACzF,SAAS;gBAAAmV,QAAA,EAAC;cAAS;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAChCrQ,OAAA,CAACzF,SAAS;gBAAAmV,QAAA,EAAC;cAAU;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eACjCrQ,OAAA,CAACzF,SAAS;gBAAAmV,QAAA,EAAC;cAAS;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAChCrQ,OAAA,CAACzF,SAAS;gBAAAmV,QAAA,EAAC;cAAM;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACZrQ,OAAA,CAAC1F,SAAS;YAAAoV,QAAA,EACP2C,YAAY,CAAC9I,GAAG,CAAEzC,IAAI,iBACrB9G,OAAA,CAACtF,QAAQ;cAEPwY,QAAQ,EAAE7P,aAAa,CAACwE,QAAQ,CAACf,IAAI,CAAC0C,iBAAiB,CAAE;cACzD2J,KAAK;cAAAzD,QAAA,GAEJnM,QAAQ,iBACPvD,OAAA,CAACzF,SAAS;gBAAC0Y,OAAO,EAAC,UAAU;gBAAAvD,QAAA,eAC3B1P,OAAA,CAACrF,UAAU;kBACT0W,IAAI,EAAC,OAAO;kBACZD,OAAO,EAAEA,CAAA,KAAMpI,mBAAmB,CAAClC,IAAI,CAAC0C,iBAAiB,CAAE;kBAC3DsG,KAAK,EAAEzM,aAAa,CAACwE,QAAQ,CAACf,IAAI,CAAC0C,iBAAiB,CAAC,GAAG,SAAS,GAAG,SAAU;kBAAAkG,QAAA,EAE7ErM,aAAa,CAACwE,QAAQ,CAACf,IAAI,CAAC0C,iBAAiB,CAAC,gBAAGxJ,OAAA,CAACtC,SAAS;oBAAAwS,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,gBAAGrQ,OAAA,CAAC5D,OAAO;oBAAA8T,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CACZ,eACDrQ,OAAA,CAACzF,SAAS;gBAAAmV,QAAA,eACR1P,OAAA,CAAC7G,UAAU;kBAACmX,OAAO,EAAC,OAAO;kBAACC,UAAU,EAAC,QAAQ;kBAAAb,QAAA,EAC5C5I,IAAI,CAAC8B;gBAAkB;kBAAAsH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACd;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACZrQ,OAAA,CAACzF,SAAS;gBAAAmV,QAAA,eACR1P,OAAA,CAAC9E,IAAI;kBAACmW,IAAI,EAAC,OAAO;kBAACU,KAAK,EAAEjL,IAAI,CAAC1C,OAAQ;kBAACkM,OAAO,EAAC;gBAAU;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpD,CAAC,eACZrQ,OAAA,CAACzF,SAAS;gBAAAmV,QAAA,EAAE,IAAI/I,IAAI,CAACG,IAAI,CAACC,mBAAmB,CAAC,CAAC+G,kBAAkB,CAAC;cAAC;gBAAAoC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAChFrQ,OAAA,CAACzF,SAAS;gBAAAmV,QAAA,eACR1P,OAAA,CAAC/E,KAAK;kBAACiX,SAAS,EAAC,KAAK;kBAACxB,UAAU,EAAC,QAAQ;kBAACnB,OAAO,EAAE,CAAE;kBAAAG,QAAA,gBACpD1P,OAAA,CAACV,UAAU;oBAAC2Q,QAAQ,EAAC;kBAAO;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC/BrQ,OAAA,CAAC7G,UAAU;oBAACmX,OAAO,EAAC,OAAO;oBAAAZ,QAAA,EAAE5I,IAAI,CAAC9E,SAAS,IAAI8E,IAAI,CAACzC;kBAAY;oBAAA6L,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAa,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACZrQ,OAAA,CAACzF,SAAS;gBAAAmV,QAAA,eACR1P,OAAA,CAAC7G,UAAU;kBAACmX,OAAO,EAAC,OAAO;kBAAAZ,QAAA,EACxB5I,IAAI,CAACxC,YAAY,GAChB,CAAC,MAAM;oBACL,MAAMjC,SAAS,GAAGnB,SAAS,CAACmK,IAAI,CAAC+H,CAAC,IAAIA,CAAC,CAAC9O,YAAY,KAAKwC,IAAI,CAACxC,YAAY,CAAC;oBAC3E,OAAOjC,SAAS,GAAG,GAAGA,SAAS,CAACgR,IAAI,MAAMhR,SAAS,CAACiR,KAAK,EAAE,GAAG,uBAAuB;kBACvF,CAAC,EAAE,CAAC,GACDxM,IAAI,CAACyM,oBAAoB,IAAI;gBAAM;kBAAArD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAE9B;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACZrQ,OAAA,CAACzF,SAAS;gBAAAmV,QAAA,eACR1P,OAAA,CAAC7G,UAAU;kBAACmX,OAAO,EAAC,OAAO;kBAAAZ,QAAA,GAAE5I,IAAI,CAACvC,kBAAkB,EAAC,IAAE;gBAAA;kBAAA2L,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3D,CAAC,eACZrQ,OAAA,CAACzF,SAAS;gBAAAmV,QAAA,eACR1P,OAAA,CAAC9E,IAAI;kBACHmW,IAAI,EAAC,OAAO;kBACZU,KAAK,EAAE,GAAGjL,IAAI,CAACrC,iBAAiB,KAAM;kBACtCqL,KAAK,EAAEhH,UAAU,CAAChC,IAAI,CAACrC,iBAAiB,CAAC,IAAI,GAAG,GAAG,SAAS,GAAG,SAAU;kBACzEiO,IAAI,EAAE5J,UAAU,CAAChC,IAAI,CAACrC,iBAAiB,CAAC,IAAI,GAAG,gBAAGzE,OAAA,CAACtC,SAAS;oBAAAwS,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,gBAAGrQ,OAAA,CAACpC,WAAW;oBAAAsS,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAE;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnF;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO,CAAC,eACZrQ,OAAA,CAACzF,SAAS;gBAAAmV,QAAA,eACR1P,OAAA,CAAC9E,IAAI;kBACHmW,IAAI,EAAC,OAAO;kBACZU,KAAK,EAAEjL,IAAI,CAAC9B,gBAAgB,IAAI,UAAW;kBAC3C8K,KAAK,EAAEhJ,IAAI,CAAC9B,gBAAgB,KAAK,UAAU,GAAG,SAAS,GAAG8B,IAAI,CAAC9B,gBAAgB,KAAK,cAAc,GAAG,OAAO,GAAG;gBAAU;kBAAAkL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1H;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO,CAAC,eACZrQ,OAAA,CAACzF,SAAS;gBAAAmV,QAAA,eACR1P,OAAA,CAAC/E,KAAK;kBAACiX,SAAS,EAAC,KAAK;kBAAC3C,OAAO,EAAE,GAAI;kBAAAG,QAAA,gBAClC1P,OAAA,CAAC7E,OAAO;oBAACwX,KAAK,EAAC,qBAAqB;oBAAAjD,QAAA,eAClC1P,OAAA,CAACrF,UAAU;sBACT0W,IAAI,EAAC,OAAO;sBACZD,OAAO,EAAEA,CAAA,KAAM;wBACbhO,eAAe,CAAC0D,IAAI,CAAC;wBACrB5D,aAAa,CAAC,MAAM,CAAC;wBACrBF,aAAa,CAAC,IAAI,CAAC;sBACrB,CAAE;sBAAA0M,QAAA,eAEF1P,OAAA,CAAClD,QAAQ;wBAAAoT,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACF;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC,eACVrQ,OAAA,CAAC7E,OAAO;oBAACwX,KAAK,EAAC,YAAY;oBAAAjD,QAAA,eACzB1P,OAAA,CAACrF,UAAU;sBACT0W,IAAI,EAAC,OAAO;sBACZD,OAAO,EAAEA,CAAA,KAAM3F,iBAAiB,CAAC3E,IAAI,CAAE;sBACvC4K,QAAQ,EAAE1N,mBAAoB;sBAAA0L,QAAA,eAE9B1P,OAAA,CAACtD,OAAO;wBAAAwT,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACD;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC,eACVrQ,OAAA,CAAC7E,OAAO;oBAACwX,KAAK,EAAC,SAAS;oBAAAjD,QAAA,eACtB1P,OAAA,CAACrF,UAAU;sBACT0W,IAAI,EAAC,OAAO;sBACZvB,KAAK,EAAC,OAAO;sBACbsB,OAAO,EAAEA,CAAA,KAAMnE,0BAA0B,CAACnG,IAAI,CAAE;sBAChD4K,QAAQ,EAAE1N,mBAAoB;sBAAA0L,QAAA,eAE9B1P,OAAA,CAAChD,UAAU;wBAAAkT,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA,GA7FPvJ,IAAI,CAAC0C,iBAAiB;cAAA0G,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA8FnB,CACX;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC,EAEhBvB,aAAa,CAACtN,sBAAsB,CAAC,GAAG,CAAC,iBACxCxB,OAAA,CAAC9G,GAAG;QAACsW,EAAE,EAAE;UAAEoD,OAAO,EAAE,MAAM;UAAET,cAAc,EAAE,QAAQ;UAAEU,EAAE,EAAE;QAAE,CAAE;QAAAnD,QAAA,eAC5D1P,OAAA,CAAClF,UAAU;UACTgY,KAAK,EAAEhE,aAAa,CAACtN,sBAAsB,CAAE;UAC7CuR,IAAI,EAAExQ,WAAY;UAClBsO,QAAQ,EAAEA,CAACpG,KAAK,EAAES,KAAK,KAAK1I,cAAc,CAAC0I,KAAK,CAAE;UAClD4E,KAAK,EAAC;QAAS;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CACN;IAAA,eACD,CAAC;EAEP,CAAC;;EAED;EACA,MAAMmD,0BAA0B,GAAGA,CAAA,KAAM;IACvC,IAAIvQ,UAAU,KAAK,QAAQ,IAAIA,UAAU,KAAK,MAAM,EAAE,OAAO,IAAI;IAEjE,oBACEjD,OAAA,CAACvG,MAAM;MAACkK,IAAI,EAAEZ,UAAW;MAAC0Q,OAAO,EAAE1I,WAAY;MAAC2I,QAAQ,EAAC,IAAI;MAAC/C,SAAS;MAAAjB,QAAA,gBACrE1P,OAAA,CAACtG,WAAW;QAAAgW,QAAA,EACTzM,UAAU,KAAK,QAAQ,GAAG,sBAAsB,GAAG;MAAyB;QAAAiN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClE,CAAC,eACdrQ,OAAA,CAACrG,aAAa;QAAA+V,QAAA,eACZ1P,OAAA,CAAC1G,IAAI;UAACgW,SAAS;UAACC,OAAO,EAAE,CAAE;UAACC,EAAE,EAAE;YAAEqD,EAAE,EAAE;UAAE,CAAE;UAAAnD,QAAA,gBACxC1P,OAAA,CAAC1G,IAAI;YAAC6V,IAAI;YAACQ,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAF,QAAA,eACvB1P,OAAA,CAAC9F,YAAY;cACXyZ,OAAO,EAAE3S,IAAI,CAAC6F,MAAM,CAACU,IAAI,IACvB,CAACzG,cAAc,CAACqH,IAAI,CAACrB,IAAI,IAAIA,IAAI,CAAC1C,OAAO,KAAKmD,IAAI,CAACnD,OAAO,CAAC,IAC3DmD,IAAI,CAACnD,OAAO,KAAKF,QAAQ,CAACE,OAC5B,CAAE;cACFwP,cAAc,EAAGpF,MAAM,IAAK,GAAGA,MAAM,CAACpK,OAAO,MAAMoK,MAAM,CAACzM,SAAS,EAAG;cACtEmJ,KAAK,EAAElK,IAAI,CAACqK,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAClH,OAAO,KAAKF,QAAQ,CAACE,OAAO,CAAC,IAAI,IAAK;cAC9DyM,QAAQ,EAAEA,CAACpG,KAAK,EAAEC,QAAQ,KAAK;gBAC7B,IAAIA,QAAQ,EAAE;kBACZS,gBAAgB,CAACT,QAAQ,CAAC;gBAC5B,CAAC,MAAM;kBACLvG,WAAW,CAAC+E,IAAI,KAAK;oBAAE,GAAGA,IAAI;oBAAE9E,OAAO,EAAE,EAAE;oBAAEG,kBAAkB,EAAE;kBAAG,CAAC,CAAC,CAAC;gBACzE;cACF,CAAE;cACFsP,WAAW,EAAGC,MAAM,iBAClB9T,OAAA,CAACnG,SAAS;gBAAA,GACJia,MAAM;gBACV/B,KAAK,EAAC,QAAQ;gBACdnB,WAAW,EAAC,mBAAmB;gBAC/BmD,QAAQ;cAAA;gBAAA7D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CACD;cACF2D,YAAY,EAAEA,CAACC,KAAK,EAAEzF,MAAM,kBAC1BxO,OAAA,CAAC9G,GAAG;gBAACoZ,SAAS,EAAC,IAAI;gBAAA,GAAK2B,KAAK;gBAAAvE,QAAA,eAC3B1P,OAAA,CAAC9G,GAAG;kBAAAwW,QAAA,gBACF1P,OAAA,CAAC7G,UAAU;oBAACmX,OAAO,EAAC,OAAO;oBAACC,UAAU,EAAC,QAAQ;oBAAAb,QAAA,EAC5ClB,MAAM,CAACpK;kBAAO;oBAAA8L,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC,eACbrQ,OAAA,CAAC7G,UAAU;oBAACmX,OAAO,EAAC,SAAS;oBAACR,KAAK,EAAC,gBAAgB;oBAAAJ,QAAA,GACjDlB,MAAM,CAACzM,SAAS,EAAC,KAAG,EAACyM,MAAM,CAAC1G,mBAAmB,EAAC,UAAG,EAAC0G,MAAM,CAACzG,iBAAiB;kBAAA;oBAAAmI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YACL;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAEPrQ,OAAA,CAAC1G,IAAI;YAAC6V,IAAI;YAACQ,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAF,QAAA,eACvB1P,OAAA,CAACnG,SAAS;cACR8W,SAAS;cACToB,KAAK,EAAC,aAAa;cACnB7G,KAAK,EAAEhH,QAAQ,CAACG,YAAa;cAC7BwM,QAAQ,EAAGC,CAAC,IAAK9F,gBAAgB,CAAC,cAAc,EAAE8F,CAAC,CAACC,MAAM,CAAC7F,KAAK,CAAE;cAClE6I,QAAQ;YAAA;cAAA7D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAEPrQ,OAAA,CAAC1G,IAAI;YAAC6V,IAAI;YAACQ,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAF,QAAA,eACvB1P,OAAA,CAAClG,WAAW;cAAC6W,SAAS;cAACoD,QAAQ;cAAArE,QAAA,gBAC7B1P,OAAA,CAACjG,UAAU;gBAAA2V,QAAA,EAAC;cAAW;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACpCrQ,OAAA,CAAChG,MAAM;gBACLkR,KAAK,EAAEhH,QAAQ,CAACI,YAAa;gBAC7BuM,QAAQ,EAAGC,CAAC,IAAK9F,gBAAgB,CAAC,cAAc,EAAE8F,CAAC,CAACC,MAAM,CAAC7F,KAAK,CAAE;gBAClE6G,KAAK,EAAC,aAAa;gBAAArC,QAAA,EAElBxO,SAAS,CAACqI,GAAG,CAAElH,SAAS,iBACvBrC,OAAA,CAAC/F,QAAQ;kBAA8BiR,KAAK,EAAE7I,SAAS,CAACiC,YAAa;kBAAAoL,QAAA,GAClErN,SAAS,CAACgR,IAAI,EAAC,KAAG,EAAChR,SAAS,CAACiR,KAAK,EAAC,GAAC,EAACjR,SAAS,CAAC6R,OAAO,EAAC,SAAO,EAAC7R,SAAS,CAAC8R,YAAY,EAAC,GACzF;gBAAA,GAFe9R,SAAS,CAACiC,YAAY;kBAAA4L,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAE3B,CACX;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eAEPrQ,OAAA,CAAC1G,IAAI;YAAC6V,IAAI;YAACQ,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAF,QAAA,eACvB1P,OAAA,CAACnG,SAAS;cACR8W,SAAS;cACToB,KAAK,EAAC,0BAA0B;cAChCnF,IAAI,EAAC,QAAQ;cACb1B,KAAK,EAAEhH,QAAQ,CAACK,kBAAmB;cACnCsM,QAAQ,EAAGC,CAAC,IAAK9F,gBAAgB,CAAC,oBAAoB,EAAE8F,CAAC,CAACC,MAAM,CAAC7F,KAAK,CAAE;cACxE6I,QAAQ;YAAA;cAAA7D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAEPrQ,OAAA,CAAC1G,IAAI;YAAC6V,IAAI;YAACQ,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAF,QAAA,eACvB1P,OAAA,CAAClG,WAAW;cAAC6W,SAAS;cAAAjB,QAAA,gBACpB1P,OAAA,CAACjG,UAAU;gBAAA2V,QAAA,EAAC;cAAU;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACnCrQ,OAAA,CAAChG,MAAM;gBACLkR,KAAK,EAAEhH,QAAQ,CAACM,iBAAkB;gBAClCqM,QAAQ,EAAGC,CAAC,IAAK9F,gBAAgB,CAAC,mBAAmB,EAAE8F,CAAC,CAACC,MAAM,CAAC7F,KAAK,CAAE;gBACvE6G,KAAK,EAAC,eAAY;gBAAArC,QAAA,gBAElB1P,OAAA,CAAC/F,QAAQ;kBAACiR,KAAK,EAAC,IAAI;kBAAAwE,QAAA,EAAC;gBAAE;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,eAClCrQ,OAAA,CAAC/F,QAAQ;kBAACiR,KAAK,EAAC,KAAK;kBAAAwE,QAAA,EAAC;gBAAG;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eAEPrQ,OAAA,CAAC1G,IAAI;YAAC6V,IAAI;YAACQ,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAF,QAAA,eACvB1P,OAAA,CAACnG,SAAS;cACR8W,SAAS;cACToB,KAAK,EAAC,wBAAmB;cACzBnF,IAAI,EAAC,QAAQ;cACb1B,KAAK,EAAEhH,QAAQ,CAACO,iBAAkB;cAClCoM,QAAQ,EAAGC,CAAC,IAAK9F,gBAAgB,CAAC,mBAAmB,EAAE8F,CAAC,CAACC,MAAM,CAAC7F,KAAK,CAAE;cACvE6I,QAAQ;cACRK,UAAU,EAAC;YAAmC;cAAAlE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/C;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAEPrQ,OAAA,CAAC1G,IAAI;YAAC6V,IAAI;YAACQ,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAF,QAAA,eACvB1P,OAAA,CAAClG,WAAW;cAAC6W,SAAS;cAAAjB,QAAA,gBACpB1P,OAAA,CAACjG,UAAU;gBAAA2V,QAAA,EAAC;cAAU;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACnCrQ,OAAA,CAAChG,MAAM;gBACLkR,KAAK,EAAEhH,QAAQ,CAACQ,iBAAkB;gBAClCmM,QAAQ,EAAGC,CAAC,IAAK9F,gBAAgB,CAAC,mBAAmB,EAAE8F,CAAC,CAACC,MAAM,CAAC7F,KAAK,CAAE;gBACvE6G,KAAK,EAAC,YAAY;gBAAArC,QAAA,gBAElB1P,OAAA,CAAC/F,QAAQ;kBAACiR,KAAK,EAAC,IAAI;kBAAAwE,QAAA,EAAC;gBAAE;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,eAClCrQ,OAAA,CAAC/F,QAAQ;kBAACiR,KAAK,EAAC,KAAK;kBAAAwE,QAAA,EAAC;gBAAG;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eAGPrQ,OAAA,CAAC1G,IAAI;YAAC6V,IAAI;YAACQ,EAAE,EAAE,EAAG;YAAAD,QAAA,eAChB1P,OAAA,CAAChF,OAAO;cAACwU,EAAE,EAAE;gBAAEoC,EAAE,EAAE;cAAE,CAAE;cAAAlC,QAAA,eACrB1P,OAAA,CAAC7G,UAAU;gBAACmX,OAAO,EAAC,OAAO;gBAACR,KAAK,EAAC,gBAAgB;gBAAAJ,QAAA,EAAC;cAEnD;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAEPrQ,OAAA,CAAC1G,IAAI;YAAC6V,IAAI;YAACQ,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAF,QAAA,eACvB1P,OAAA,CAACnG,SAAS;cACR8W,SAAS;cACToB,KAAK,EAAC,8BAA2B;cACjCnF,IAAI,EAAC,QAAQ;cACb1B,KAAK,EAAEhH,QAAQ,CAACU,oBAAqB;cACrCiM,QAAQ,EAAGC,CAAC,IAAK9F,gBAAgB,CAAC,sBAAsB,EAAE8F,CAAC,CAACC,MAAM,CAAC7F,KAAK,CAAE;cAC1EkJ,UAAU,EAAC;YAA6B;cAAAlE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAEPrQ,OAAA,CAAC1G,IAAI;YAAC6V,IAAI;YAACQ,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAF,QAAA,eACvB1P,OAAA,CAACnG,SAAS;cACR8W,SAAS;cACToB,KAAK,EAAC,gBAAa;cACnBnF,IAAI,EAAC,QAAQ;cACb1B,KAAK,EAAEhH,QAAQ,CAACW,OAAQ;cACxBgM,QAAQ,EAAGC,CAAC,IAAK9F,gBAAgB,CAAC,SAAS,EAAE8F,CAAC,CAACC,MAAM,CAAC7F,KAAK,CAAE;cAC7DkJ,UAAU,EAAC;YAAkB;cAAAlE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAEPrQ,OAAA,CAAC1G,IAAI;YAAC6V,IAAI;YAACQ,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAF,QAAA,eACvB1P,OAAA,CAACnG,SAAS;cACR8W,SAAS;cACToB,KAAK,EAAC,uBAAuB;cAC7BnF,IAAI,EAAC,QAAQ;cACb1B,KAAK,EAAEhH,QAAQ,CAACY,cAAe;cAC/B+L,QAAQ,EAAGC,CAAC,IAAK9F,gBAAgB,CAAC,gBAAgB,EAAE8F,CAAC,CAACC,MAAM,CAAC7F,KAAK,CAAE;cACpEkJ,UAAU,EAAC;YAAgC;cAAAlE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5C;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAEPrQ,OAAA,CAAC1G,IAAI;YAAC6V,IAAI;YAACQ,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAF,QAAA,eACvB1P,OAAA,CAACnG,SAAS;cACR8W,SAAS;cACToB,KAAK,EAAC,oBAAoB;cAC1BnF,IAAI,EAAC,QAAQ;cACb1B,KAAK,EAAEhH,QAAQ,CAACa,YAAa;cAC7B8L,QAAQ,EAAGC,CAAC,IAAK9F,gBAAgB,CAAC,cAAc,EAAE8F,CAAC,CAACC,MAAM,CAAC7F,KAAK,CAAE;cAClEkJ,UAAU,EAAC;YAA2B;cAAAlE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAEPrQ,OAAA,CAAC1G,IAAI;YAAC6V,IAAI;YAACQ,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAF,QAAA,eACvB1P,OAAA,CAAClG,WAAW;cAAC6W,SAAS;cAAAjB,QAAA,gBACpB1P,OAAA,CAACjG,UAAU;gBAAA2V,QAAA,EAAC;cAAgB;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACzCrQ,OAAA,CAAChG,MAAM;gBACLkR,KAAK,EAAEhH,QAAQ,CAACc,gBAAiB;gBACjC6L,QAAQ,EAAGC,CAAC,IAAK9F,gBAAgB,CAAC,kBAAkB,EAAE8F,CAAC,CAACC,MAAM,CAAC7F,KAAK,CAAE;gBACtE6G,KAAK,EAAC,kBAAkB;gBAAArC,QAAA,gBAExB1P,OAAA,CAAC/F,QAAQ;kBAACiR,KAAK,EAAC,UAAU;kBAAAwE,QAAA,eACxB1P,OAAA,CAAC/E,KAAK;oBAACiX,SAAS,EAAC,KAAK;oBAACxB,UAAU,EAAC,QAAQ;oBAACnB,OAAO,EAAE,CAAE;oBAAAG,QAAA,gBACpD1P,OAAA,CAACtC,SAAS;sBAACoS,KAAK,EAAC;oBAAS;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAC7BrQ,OAAA,CAAC7G,UAAU;sBAAAuW,QAAA,EAAC;oBAAQ;sBAAAQ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5B;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA,CAAC,eACXrQ,OAAA,CAAC/F,QAAQ;kBAACiR,KAAK,EAAC,cAAc;kBAAAwE,QAAA,eAC5B1P,OAAA,CAAC/E,KAAK;oBAACiX,SAAS,EAAC,KAAK;oBAACxB,UAAU,EAAC,QAAQ;oBAACnB,OAAO,EAAE,CAAE;oBAAAG,QAAA,gBACpD1P,OAAA,CAACd,SAAS;sBAAC4Q,KAAK,EAAC;oBAAO;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAC3BrQ,OAAA,CAAC7G,UAAU;sBAAAuW,QAAA,EAAC;oBAAY;sBAAAQ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA,CAAC,eACXrQ,OAAA,CAAC/F,QAAQ;kBAACiR,KAAK,EAAC,eAAe;kBAAAwE,QAAA,eAC7B1P,OAAA,CAAC/E,KAAK;oBAACiX,SAAS,EAAC,KAAK;oBAACxB,UAAU,EAAC,QAAQ;oBAACnB,OAAO,EAAE,CAAE;oBAAAG,QAAA,gBACpD1P,OAAA,CAACpC,WAAW;sBAACkS,KAAK,EAAC;oBAAS;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAC/BrQ,OAAA,CAAC7G,UAAU;sBAAAuW,QAAA,EAAC;oBAAa;sBAAAQ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eAEPrQ,OAAA,CAAC1G,IAAI;YAAC6V,IAAI;YAACQ,EAAE,EAAE,EAAG;YAAAD,QAAA,eAChB1P,OAAA,CAACnG,SAAS;cACR8W,SAAS;cACToB,KAAK,EAAC,MAAM;cACZsC,SAAS;cACTxG,IAAI,EAAE,CAAE;cACR3C,KAAK,EAAEhH,QAAQ,CAACS,IAAK;cACrBkM,QAAQ,EAAGC,CAAC,IAAK9F,gBAAgB,CAAC,MAAM,EAAE8F,CAAC,CAACC,MAAM,CAAC7F,KAAK,CAAE;cAC1D0F,WAAW,EAAC;YAAkF;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/F;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC,eAChBrQ,OAAA,CAACpG,aAAa;QAAA8V,QAAA,gBACZ1P,OAAA,CAAC5G,MAAM;UAACgY,OAAO,EAAErG,WAAY;UAAA2E,QAAA,EAAC;QAAO;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAC9CrQ,OAAA,CAAC5G,MAAM;UACLgY,OAAO,EAAEhG,0BAA2B;UACpCkF,OAAO,EAAC,WAAW;UACnBoB,QAAQ,EAAEhR,OAAO,IAAI,CAACwD,QAAQ,CAACE,OAAO,IAAI,CAACF,QAAQ,CAACG,YAAY,IAAI,CAACH,QAAQ,CAACI,YAAY,IAAI,CAACJ,QAAQ,CAACO,iBAAkB;UAC1H6M,SAAS,EAAE5Q,OAAO,gBAAGV,OAAA,CAAC7F,gBAAgB;YAACkX,IAAI,EAAE;UAAG;YAAAnB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAAGrQ,OAAA,CAAC5C,QAAQ;YAAA8S,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAAX,QAAA,EAElEzM,UAAU,KAAK,QAAQ,GAAG,qBAAqB,GAAG;QAAiB;UAAAiN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9D,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAEb,CAAC;;EAED;EACA,MAAMiE,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAIrR,UAAU,KAAK,MAAM,IAAI,CAACE,YAAY,EAAE,OAAO,IAAI;IAEvD,oBACEnD,OAAA,CAACvG,MAAM;MAACkK,IAAI,EAAEZ,UAAW;MAAC0Q,OAAO,EAAE1I,WAAY;MAAC2I,QAAQ,EAAC,IAAI;MAAC/C,SAAS;MAAAjB,QAAA,gBACrE1P,OAAA,CAACtG,WAAW;QAAAgW,QAAA,GAAC,4BACe,EAACvM,YAAY,CAACyF,kBAAkB;MAAA;QAAAsH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/C,CAAC,eACdrQ,OAAA,CAACrG,aAAa;QAAA+V,QAAA,eACZ1P,OAAA,CAAC1G,IAAI;UAACgW,SAAS;UAACC,OAAO,EAAE,CAAE;UAACC,EAAE,EAAE;YAAEqD,EAAE,EAAE;UAAE,CAAE;UAAAnD,QAAA,gBACxC1P,OAAA,CAAC1G,IAAI;YAAC6V,IAAI;YAACQ,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAF,QAAA,eACvB1P,OAAA,CAACzG,IAAI;cAAC+W,OAAO,EAAC,UAAU;cAAAZ,QAAA,eACtB1P,OAAA,CAACxG,WAAW;gBAAAkW,QAAA,gBACV1P,OAAA,CAAC7G,UAAU;kBAACmX,OAAO,EAAC,IAAI;kBAACiE,YAAY;kBAAA7E,QAAA,EAAC;gBAEtC;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACbrQ,OAAA,CAAC7G,UAAU;kBAACmX,OAAO,EAAC,OAAO;kBAACR,KAAK,EAAC,gBAAgB;kBAAAJ,QAAA,GAAC,WACxC,eAAA1P,OAAA;oBAAA0P,QAAA,EAASvM,YAAY,CAACiB;kBAAO;oBAAA8L,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAS,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtC,CAAC,eACbrQ,OAAA,CAAC7G,UAAU;kBAACmX,OAAO,EAAC,OAAO;kBAACR,KAAK,EAAC,gBAAgB;kBAAAJ,QAAA,GAAC,sBAC7B,eAAA1P,OAAA;oBAAA0P,QAAA,GAASvM,YAAY,CAACoB,kBAAkB,EAAC,IAAE;kBAAA;oBAAA2L,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9D,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEPrQ,OAAA,CAAC1G,IAAI;YAAC6V,IAAI;YAACQ,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAF,QAAA,eACvB1P,OAAA,CAACzG,IAAI;cAAC+W,OAAO,EAAC,UAAU;cAAAZ,QAAA,eACtB1P,OAAA,CAACxG,WAAW;gBAAAkW,QAAA,gBACV1P,OAAA,CAAC7G,UAAU;kBAACmX,OAAO,EAAC,IAAI;kBAACiE,YAAY;kBAAA7E,QAAA,EAAC;gBAEtC;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACbrQ,OAAA,CAAC7G,UAAU;kBAACmX,OAAO,EAAC,OAAO;kBAACR,KAAK,EAAC,gBAAgB;kBAAAJ,QAAA,GAAC,UACzC,eAAA1P,OAAA;oBAAA0P,QAAA,EAASvM,YAAY,CAACyF;kBAAkB;oBAAAsH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAS,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChD,CAAC,eACbrQ,OAAA,CAAC7G,UAAU;kBAACmX,OAAO,EAAC,OAAO;kBAACR,KAAK,EAAC,gBAAgB;kBAAAJ,QAAA,GAAC,QAC3C,eAAA1P,OAAA;oBAAA0P,QAAA,EAAS,IAAI/I,IAAI,CAACxD,YAAY,CAAC4D,mBAAmB,CAAC,CAAC+G,kBAAkB,CAAC;kBAAC;oBAAAoC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAS,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9E,CAAC,eACbrQ,OAAA,CAAC7G,UAAU;kBAACmX,OAAO,EAAC,OAAO;kBAACR,KAAK,EAAC,gBAAgB;kBAAAJ,QAAA,GAAC,aACtC,eAAA1P,OAAA;oBAAA0P,QAAA,EAASvM,YAAY,CAACnB,SAAS,IAAImB,YAAY,CAACkB;kBAAY;oBAAA6L,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAS,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEPrQ,OAAA,CAAC1G,IAAI;YAAC6V,IAAI;YAACQ,EAAE,EAAE,EAAG;YAAAD,QAAA,eAChB1P,OAAA,CAACzG,IAAI;cAAC+W,OAAO,EAAC,UAAU;cAAAZ,QAAA,eACtB1P,OAAA,CAACxG,WAAW;gBAAAkW,QAAA,gBACV1P,OAAA,CAAC7G,UAAU;kBAACmX,OAAO,EAAC,IAAI;kBAACiE,YAAY;kBAAA7E,QAAA,EAAC;gBAEtC;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACbrQ,OAAA,CAAC1G,IAAI;kBAACgW,SAAS;kBAACC,OAAO,EAAE,CAAE;kBAAAG,QAAA,gBACzB1P,OAAA,CAAC1G,IAAI;oBAAC6V,IAAI;oBAACQ,EAAE,EAAE,CAAE;oBAAAD,QAAA,gBACf1P,OAAA,CAAC7G,UAAU;sBAACmX,OAAO,EAAC,OAAO;sBAACR,KAAK,EAAC,gBAAgB;sBAAAJ,QAAA,EAAC;oBAEnD;sBAAAQ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACbrQ,OAAA,CAAC9E,IAAI;sBACHmW,IAAI,EAAC,OAAO;sBACZU,KAAK,EAAE5O,YAAY,CAACqB,iBAAkB;sBACtCsL,KAAK,EAAE3M,YAAY,CAACqB,iBAAiB,KAAK,IAAI,GAAG,SAAS,GAAG;oBAAQ;sBAAA0L,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC,eACPrQ,OAAA,CAAC1G,IAAI;oBAAC6V,IAAI;oBAACQ,EAAE,EAAE,CAAE;oBAAAD,QAAA,gBACf1P,OAAA,CAAC7G,UAAU;sBAACmX,OAAO,EAAC,OAAO;sBAACR,KAAK,EAAC,gBAAgB;sBAAAJ,QAAA,EAAC;oBAEnD;sBAAAQ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACbrQ,OAAA,CAAC9E,IAAI;sBACHmW,IAAI,EAAC,OAAO;sBACZU,KAAK,EAAE,GAAG5O,YAAY,CAACsB,iBAAiB,KAAM;sBAC9CqL,KAAK,EAAEhH,UAAU,CAAC3F,YAAY,CAACsB,iBAAiB,CAAC,IAAI,GAAG,GAAG,SAAS,GAAG;oBAAU;sBAAAyL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAClF,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC,eACPrQ,OAAA,CAAC1G,IAAI;oBAAC6V,IAAI;oBAACQ,EAAE,EAAE,CAAE;oBAAAD,QAAA,gBACf1P,OAAA,CAAC7G,UAAU;sBAACmX,OAAO,EAAC,OAAO;sBAACR,KAAK,EAAC,gBAAgB;sBAAAJ,QAAA,EAAC;oBAEnD;sBAAAQ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACbrQ,OAAA,CAAC9E,IAAI;sBACHmW,IAAI,EAAC,OAAO;sBACZU,KAAK,EAAE5O,YAAY,CAACuB,iBAAkB;sBACtCoL,KAAK,EAAE3M,YAAY,CAACuB,iBAAiB,KAAK,IAAI,GAAG,SAAS,GAAG;oBAAQ;sBAAAwL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EAENlN,YAAY,CAACwB,IAAI,iBAChB3E,OAAA,CAAC1G,IAAI;YAAC6V,IAAI;YAACQ,EAAE,EAAE,EAAG;YAAAD,QAAA,eAChB1P,OAAA,CAACzG,IAAI;cAAC+W,OAAO,EAAC,UAAU;cAAAZ,QAAA,eACtB1P,OAAA,CAACxG,WAAW;gBAAAkW,QAAA,gBACV1P,OAAA,CAAC7G,UAAU;kBAACmX,OAAO,EAAC,IAAI;kBAACiE,YAAY;kBAAA7E,QAAA,EAAC;gBAEtC;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACbrQ,OAAA,CAAC7G,UAAU;kBAACmX,OAAO,EAAC,OAAO;kBAAAZ,QAAA,EACxBvM,YAAY,CAACwB;gBAAI;kBAAAuL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACP;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC,eAChBrQ,OAAA,CAACpG,aAAa;QAAA8V,QAAA,gBACZ1P,OAAA,CAAC5G,MAAM;UAACgY,OAAO,EAAErG,WAAY;UAAA2E,QAAA,EAAC;QAAM;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAC7CrQ,OAAA,CAAC5G,MAAM;UACLgY,OAAO,EAAEA,CAAA,KAAM3F,iBAAiB,CAACtI,YAAY,CAAE;UAC/CmN,OAAO,EAAC,WAAW;UACnBgB,SAAS,eAAEtR,OAAA,CAACtD,OAAO;YAAAwT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACvBqB,QAAQ,EAAEhR,OAAQ;UAAAgP,QAAA,EACnB;QAED;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAEb,CAAC;;EAED;EACA,MAAMmE,WAAW,GAAGA,CAAA,KAAM;IACxB,MAAMC,SAAS,GAAGzT,IAAI,CAACuF,MAAM;IAC7B,MAAMmO,cAAc,GAAG1T,IAAI,CAAC6F,MAAM,CAACyE,CAAC,IAAIA,CAAC,CAACpD,mBAAmB,KAAK,YAAY,CAAC,CAAC3B,MAAM;IACtF,MAAMnB,eAAe,GAAGtE,cAAc,CAACyF,MAAM;IAC7C,MAAMoO,yBAAyB,GAAGF,SAAS,GAAG,CAAC,GAAGjO,IAAI,CAACC,KAAK,CAAErB,eAAe,GAAGsP,cAAc,GAAI,GAAG,CAAC,GAAG,CAAC;IAE1G,oBACE1U,OAAA,CAAC1G,IAAI;MAACgW,SAAS;MAACC,OAAO,EAAE,CAAE;MAACC,EAAE,EAAE;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAC,QAAA,gBACxC1P,OAAA,CAAC1G,IAAI;QAAC6V,IAAI;QAACQ,EAAE,EAAE,EAAG;QAACiF,EAAE,EAAE,CAAE;QAAChF,EAAE,EAAE,CAAE;QAAAF,QAAA,eAC9B1P,OAAA,CAACzG,IAAI;UAAAmW,QAAA,eACH1P,OAAA,CAACxG,WAAW;YAAAkW,QAAA,gBACV1P,OAAA,CAAC7G,UAAU;cAAC2W,KAAK,EAAC,gBAAgB;cAACyE,YAAY;cAAA7E,QAAA,EAAC;YAEhD;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbrQ,OAAA,CAAC7G,UAAU;cAACmX,OAAO,EAAC,IAAI;cAAAZ,QAAA,EACrB+E;YAAS;cAAAvE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACPrQ,OAAA,CAAC1G,IAAI;QAAC6V,IAAI;QAACQ,EAAE,EAAE,EAAG;QAACiF,EAAE,EAAE,CAAE;QAAChF,EAAE,EAAE,CAAE;QAAAF,QAAA,eAC9B1P,OAAA,CAACzG,IAAI;UAAAmW,QAAA,eACH1P,OAAA,CAACxG,WAAW;YAAAkW,QAAA,gBACV1P,OAAA,CAAC7G,UAAU;cAAC2W,KAAK,EAAC,gBAAgB;cAACyE,YAAY;cAAA7E,QAAA,EAAC;YAEhD;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbrQ,OAAA,CAAC7G,UAAU;cAACmX,OAAO,EAAC,IAAI;cAAAZ,QAAA,EACrBgF;YAAc;cAAAxE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACPrQ,OAAA,CAAC1G,IAAI;QAAC6V,IAAI;QAACQ,EAAE,EAAE,EAAG;QAACiF,EAAE,EAAE,CAAE;QAAChF,EAAE,EAAE,CAAE;QAAAF,QAAA,eAC9B1P,OAAA,CAACzG,IAAI;UAAAmW,QAAA,eACH1P,OAAA,CAACxG,WAAW;YAAAkW,QAAA,gBACV1P,OAAA,CAAC7G,UAAU;cAAC2W,KAAK,EAAC,gBAAgB;cAACyE,YAAY;cAAA7E,QAAA,EAAC;YAEhD;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbrQ,OAAA,CAAC7G,UAAU;cAACmX,OAAO,EAAC,IAAI;cAAAZ,QAAA,EACrBtK;YAAe;cAAA8K,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACPrQ,OAAA,CAAC1G,IAAI;QAAC6V,IAAI;QAACQ,EAAE,EAAE,EAAG;QAACiF,EAAE,EAAE,CAAE;QAAChF,EAAE,EAAE,CAAE;QAAAF,QAAA,eAC9B1P,OAAA,CAACzG,IAAI;UAAAmW,QAAA,eACH1P,OAAA,CAACxG,WAAW;YAAAkW,QAAA,gBACV1P,OAAA,CAAC7G,UAAU;cAAC2W,KAAK,EAAC,gBAAgB;cAACyE,YAAY;cAAA7E,QAAA,EAAC;YAEhD;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbrQ,OAAA,CAAC7G,UAAU;cAACmX,OAAO,EAAC,IAAI;cAACR,KAAK,EAAE6E,yBAAyB,IAAI,EAAE,GAAG,cAAc,GAAG,cAAe;cAAAjF,QAAA,GAC/FiF,yBAAyB,EAAC,GAC7B;YAAA;cAAAzE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEX,CAAC;EAED,oBACErQ,OAAA,CAAClE,SAAS;IAAC4X,QAAQ,EAAC,IAAI;IAAClE,EAAE,EAAE;MAAEQ,EAAE,EAAE;IAAE,CAAE;IAAAN,QAAA,gBAErC1P,OAAA,CAAC9G,GAAG;MAACsW,EAAE,EAAE;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAC,QAAA,gBACjB1P,OAAA,CAAC7G,UAAU;QAACmX,OAAO,EAAC,IAAI;QAACgC,SAAS,EAAC,IAAI;QAACiC,YAAY;QAAChE,UAAU,EAAC,MAAM;QAAAb,QAAA,EAAC;MAEvE;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACbrQ,OAAA,CAAC7G,UAAU;QAACmX,OAAO,EAAC,OAAO;QAACR,KAAK,EAAC,gBAAgB;QAACN,EAAE,EAAE;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAC,QAAA,EAAC;MAElE;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAGbrQ,OAAA,CAAC3G,KAAK;QAACmW,EAAE,EAAE;UAAEiB,CAAC,EAAE,CAAC;UAAEoE,OAAO,EAAE,YAAY;UAAE/E,KAAK,EAAE,mBAAmB;UAAEL,EAAE,EAAE;QAAE,CAAE;QAAAC,QAAA,eAC5E1P,OAAA,CAAC/E,KAAK;UAACiX,SAAS,EAAC,KAAK;UAACxB,UAAU,EAAC,QAAQ;UAACnB,OAAO,EAAE,CAAE;UAAAG,QAAA,gBACpD1P,OAAA,CAAChB,QAAQ;YAAAkR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACZrQ,OAAA,CAAC9G,GAAG;YAAAwW,QAAA,gBACF1P,OAAA,CAAC7G,UAAU;cAACmX,OAAO,EAAC,OAAO;cAACC,UAAU,EAAC,MAAM;cAAAb,QAAA,EAAC;YAE9C;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbrQ,OAAA,CAAC7G,UAAU;cAACmX,OAAO,EAAC,SAAS;cAAAZ,QAAA,GAAC,SAC1B,eAAA1P,OAAA;gBAAA0P,QAAA,EAAQ;cAA0B;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,qFAC3C,eAAArQ,OAAA;gBAAA0P,QAAA,EAAQ;cAAgC;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,kGACjD,eAAArQ,OAAA;gBAAA0P,QAAA,EAAQ;cAAO;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,kFAC5B;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,EAGLhB,eAAe,CAAC,CAAC,EAGjB,CAAC3O,OAAO,IAAIsD,mBAAmB,kBAC9BhE,OAAA,CAAC9G,GAAG;MAACsW,EAAE,EAAE;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAC,QAAA,gBACjB1P,OAAA,CAAC3E,cAAc;QAAA6U,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,EACjBvM,QAAQ,GAAG,CAAC,iBACX9D,OAAA,CAAC7G,UAAU;QAACmX,OAAO,EAAC,SAAS;QAACR,KAAK,EAAC,gBAAgB;QAACN,EAAE,EAAE;UAAEqD,EAAE,EAAE;QAAE,CAAE;QAAAnD,QAAA,GAAC,iBACnD,EAAC5L,QAAQ,EAAC,GAC3B;MAAA;QAAAoM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CACb;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CACN,eAGDrQ,OAAA,CAAC3G,KAAK;MAACmW,EAAE,EAAE;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAC,QAAA,eACnB1P,OAAA,CAACpF,IAAI;QACHsQ,KAAK,EAAEtK,SAAU;QACjBiQ,QAAQ,EAAErG,eAAgB;QAC1BsK,cAAc,EAAC,SAAS;QACxBC,SAAS,EAAC,SAAS;QACnBzE,OAAO,EAAC,WAAW;QAAAZ,QAAA,gBAEnB1P,OAAA,CAACnF,GAAG;UACFkX,KAAK,eACH/R,OAAA,CAAC/E,KAAK;YAACiX,SAAS,EAAC,KAAK;YAACxB,UAAU,EAAC,QAAQ;YAACnB,OAAO,EAAE,CAAE;YAAAG,QAAA,gBACpD1P,OAAA,CAACR,SAAS;cAAA0Q,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACbrQ,OAAA,CAAC9G,GAAG;cAAAwW,QAAA,gBACF1P,OAAA,CAAC7G,UAAU;gBAACmX,OAAO,EAAC,OAAO;gBAACC,UAAU,EAAC,MAAM;gBAAAb,QAAA,EAAC;cAE9C;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACbrQ,OAAA,CAAC7G,UAAU;gBAACmX,OAAO,EAAC,SAAS;gBAACR,KAAK,EAAC,gBAAgB;gBAAAJ,QAAA,GACjDpO,YAAY,CAACiF,MAAM,EAAC,cACvB;cAAA;gBAAA2J,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,EACLpL,UAAU,CAACI,kBAAkB,GAAG,CAAC,iBAChCrF,OAAA,CAAC5E,KAAK;cAAC4Z,YAAY,EAAE/P,UAAU,CAACI,kBAAmB;cAACyK,KAAK,EAAC,SAAS;cAAAJ,QAAA,eACjE1P,OAAA,CAACpC,WAAW;gBAAAsS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CACR;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI;QACR;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACFrQ,OAAA,CAACnF,GAAG;UACFkX,KAAK,eACH/R,OAAA,CAAC/E,KAAK;YAACiX,SAAS,EAAC,KAAK;YAACxB,UAAU,EAAC,QAAQ;YAACnB,OAAO,EAAE,CAAE;YAAAG,QAAA,gBACpD1P,OAAA,CAACN,WAAW;cAAAwQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACfrQ,OAAA,CAAC9G,GAAG;cAAAwW,QAAA,gBACF1P,OAAA,CAAC7G,UAAU;gBAACmX,OAAO,EAAC,OAAO;gBAACC,UAAU,EAAC,MAAM;gBAAAb,QAAA,EAAC;cAE9C;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACbrQ,OAAA,CAAC7G,UAAU;gBAACmX,OAAO,EAAC,SAAS;gBAACR,KAAK,EAAC,gBAAgB;gBAAAJ,QAAA,GACjDlO,sBAAsB,CAAC+E,MAAM,EAAC,iBACjC;cAAA;gBAAA2J,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,EACLpL,UAAU,CAACM,kBAAkB,GAAG,CAAC,iBAChCvF,OAAA,CAAC5E,KAAK;cAAC4Z,YAAY,EAAE/P,UAAU,CAACM,kBAAmB;cAACuK,KAAK,EAAC,SAAS;cAAAJ,QAAA,eACjE1P,OAAA,CAACtC,SAAS;gBAAAwS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CACR;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI;QACR;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,EAGPG,sBAAsB,CAAC,CAAC,EAGxB,CAAC9P,OAAO,IAAIE,SAAS,KAAK,CAAC,IAAIwR,eAAe,CAAC,CAAC,EAChD,CAAC1R,OAAO,IAAIE,SAAS,KAAK,CAAC,IAAIoS,yBAAyB,CAAC,CAAC,EAG1DQ,0BAA0B,CAAC,CAAC,EAC5Bc,gBAAgB,CAAC,CAAC,eAGnBtU,OAAA,CAACrE,QAAQ;MACPgI,IAAI,EAAEF,QAAQ,CAACE,IAAK;MACpBsR,gBAAgB,EAAE,IAAK;MACvBxB,OAAO,EAAEtM,aAAc;MACvB+N,YAAY,EAAE;QAAEC,QAAQ,EAAE,QAAQ;QAAEC,UAAU,EAAE;MAAQ,CAAE;MAAA1F,QAAA,eAE1D1P,OAAA,CAAC5F,KAAK;QAACqZ,OAAO,EAAEtM,aAAc;QAACtD,QAAQ,EAAEJ,QAAQ,CAACI,QAAS;QAAC2L,EAAE,EAAE;UAAE6F,KAAK,EAAE;QAAO,CAAE;QAAA3F,QAAA,EAC/EjM,QAAQ,CAACG;MAAO;QAAAsM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACZ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eAGXrQ,OAAA,CAAChE,SAAS;MACRsZ,SAAS,EAAC,eAAe;MACzB9F,EAAE,EAAE;QAAE0B,QAAQ,EAAE,OAAO;QAAEqE,MAAM,EAAE,EAAE;QAAEC,KAAK,EAAE;MAAG,CAAE;MACjD9C,IAAI,eAAE1S,OAAA,CAAC9D,aAAa;QAAAgU,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAE;MAAAX,QAAA,gBAExB1P,OAAA,CAAC/D,eAAe;QACdyW,IAAI,eAAE1S,OAAA,CAAC5D,OAAO;UAAA8T,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAClBoF,YAAY,EAAC,sBAAsB;QACnCrE,OAAO,EAAEzG;MAAiB;QAAAuF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3B,CAAC,eACFrQ,OAAA,CAAC/D,eAAe;QACdyW,IAAI,eAAE1S,OAAA,CAAClC,UAAU;UAAAoS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACrBoF,YAAY,EAAC,eAAe;QAC5BrE,OAAO,EAAE9C;MAAgB;QAAA4B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1B,CAAC,eACFrQ,OAAA,CAAC/D,eAAe;QACdyW,IAAI,eAAE1S,OAAA,CAACtB,WAAW;UAAAwR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACtBoF,YAAY,EAAC,eAAe;QAC5BrE,OAAO,EAAE3L;MAAgB;QAAAyK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1B,CAAC,eACFrQ,OAAA,CAAC/D,eAAe;QACdyW,IAAI,eAAE1S,OAAA,CAAC1B,UAAU;UAAA4R,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACrBoF,YAAY,EAAC,iBAAiB;QAC9BrE,OAAO,EAAEA,CAAA,KAAMnL,YAAY,CAAC,0BAA0B,EAAE,MAAM;MAAE;QAAAiK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACO,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEhB,CAAC,kCAAC;AAACqF,GAAA,GA93DGvV,0BAA0B;AAg4DhC,eAAeA,0BAA0B;AAAC,IAAAE,EAAA,EAAAqV,GAAA;AAAAC,YAAA,CAAAtV,EAAA;AAAAsV,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}