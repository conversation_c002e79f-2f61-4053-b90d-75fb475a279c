import { SnackbarTypeMap } from './Snackbar.types';
import { PolymorphicComponent } from '../utils';
/**
 *
 * Demos:
 *
 * - [Snackbar](https://mui.com/base-ui/react-snackbar/)
 * - [Snackbar](https://mui.com/joy-ui/react-snackbar/)
 * - [Snackbar](https://mui.com/material-ui/react-snackbar/)
 *
 * API:
 *
 * - [Snackbar API](https://mui.com/base-ui/react-snackbar/components-api/#snackbar)
 */
declare const Snackbar: PolymorphicComponent<SnackbarTypeMap<{}, "div">>;
export { Snackbar };
