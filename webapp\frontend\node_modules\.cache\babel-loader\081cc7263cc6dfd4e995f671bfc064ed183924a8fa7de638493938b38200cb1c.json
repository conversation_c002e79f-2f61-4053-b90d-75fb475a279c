{"ast": null, "code": "var formatRelativeLocale = {\n  lastWeek: \"'të' eeee 'e shkuar në' p\",\n  yesterday: \"'dje në' p\",\n  today: \"'sot në' p\",\n  tomorrow: \"'nesër në' p\",\n  nextWeek: \"eeee 'at' p\",\n  other: 'P'\n};\nvar formatRelative = function formatRelative(token, _date, _baseDate, _options) {\n  return formatRelativeLocale[token];\n};\nexport default formatRelative;", "map": {"version": 3, "names": ["formatRelativeLocale", "lastWeek", "yesterday", "today", "tomorrow", "nextWeek", "other", "formatRelative", "token", "_date", "_baseDate", "_options"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/esm/locale/sq/_lib/formatRelative/index.js"], "sourcesContent": ["var formatRelativeLocale = {\n  lastWeek: \"'të' eeee 'e shkuar në' p\",\n  yesterday: \"'dje në' p\",\n  today: \"'sot në' p\",\n  tomorrow: \"'nesër në' p\",\n  nextWeek: \"eeee 'at' p\",\n  other: 'P'\n};\nvar formatRelative = function formatRelative(token, _date, _baseDate, _options) {\n  return formatRelativeLocale[token];\n};\nexport default formatRelative;"], "mappings": "AAAA,IAAIA,oBAAoB,GAAG;EACzBC,QAAQ,EAAE,2BAA2B;EACrCC,SAAS,EAAE,YAAY;EACvBC,KAAK,EAAE,YAAY;EACnBC,QAAQ,EAAE,cAAc;EACxBC,QAAQ,EAAE,aAAa;EACvBC,KAAK,EAAE;AACT,CAAC;AACD,IAAIC,cAAc,GAAG,SAASA,cAAcA,CAACC,KAAK,EAAEC,KAAK,EAAEC,SAAS,EAAEC,QAAQ,EAAE;EAC9E,OAAOX,oBAAoB,CAACQ,KAAK,CAAC;AACpC,CAAC;AACD,eAAeD,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}