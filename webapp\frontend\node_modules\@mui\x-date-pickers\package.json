{"name": "@mui/x-date-pickers", "version": "6.20.2", "description": "The community edition of the date picker components (MUI X).", "author": "MUI Team", "main": "./node/index.js", "license": "MIT", "bugs": {"url": "https://github.com/mui/mui-x/issues"}, "homepage": "https://mui.com/x/react-date-pickers/", "funding": {"type": "opencollective", "url": "https://opencollective.com/mui"}, "sideEffects": false, "publishConfig": {"access": "public"}, "keywords": ["react", "react-component", "mui", "material-ui", "material design", "datepicker", "timepicker", "datetimepicker"], "repository": {"type": "git", "url": "https://github.com/mui/mui-x.git", "directory": "packages/x-date-pickers"}, "dependencies": {"@babel/runtime": "^7.23.2", "@mui/base": "^5.0.0-beta.22", "@mui/utils": "^5.14.16", "@types/react-transition-group": "^4.4.8", "clsx": "^2.0.0", "prop-types": "^15.8.1", "react-transition-group": "^4.4.5"}, "peerDependencies": {"@emotion/react": "^11.9.0", "@emotion/styled": "^11.8.1", "@mui/material": "^5.8.6", "@mui/system": "^5.8.0", "date-fns": "^2.25.0 || ^3.2.0", "date-fns-jalali": "^2.13.0-0", "dayjs": "^1.10.7", "luxon": "^3.0.2", "moment": "^2.29.4", "moment-hijri": "^2.1.2", "moment-jalaali": "^0.7.4 || ^0.8.0 || ^0.9.0 || ^0.10.0", "react": "^17.0.0 || ^18.0.0", "react-dom": "^17.0.0 || ^18.0.0"}, "peerDependenciesMeta": {"@emotion/react": {"optional": true}, "@emotion/styled": {"optional": true}, "date-fns": {"optional": true}, "date-fns-jalali": {"optional": true}, "dayjs": {"optional": true}, "luxon": {"optional": true}, "moment": {"optional": true}, "moment-hijri": {"optional": true}, "moment-jalaali": {"optional": true}}, "setupFiles": ["<rootDir>/src/setupTests.js"], "engines": {"node": ">=14.0.0"}, "private": false, "module": "./index.js", "types": "./index.d.ts"}