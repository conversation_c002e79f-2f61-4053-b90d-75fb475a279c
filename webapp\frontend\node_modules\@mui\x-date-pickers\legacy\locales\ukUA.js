import { getPickersLocalization } from './utils/getPickersLocalization';
var timeViews = {
  hours: 'годин',
  minutes: 'хвилин',
  seconds: 'секунд',
  meridiem: 'Південь'
};
var ukUAPickers = {
  // Calendar navigation
  previousMonth: 'Попередній місяць',
  nextMonth: 'Наступний місяць',
  // View navigation
  openPreviousView: 'відкрити попередній вигляд',
  openNextView: 'відкрити наступний вигляд',
  calendarViewSwitchingButtonAriaLabel: function calendarViewSwitchingButtonAriaLabel(view) {
    return view === 'year' ? 'річний вигляд відкрито, перейти до календарного вигляду' : 'календарний вигляд відкрито, перейти до річного вигляду';
  },
  // DateRange placeholders
  start: 'Початок',
  end: 'Кінець',
  // Action bar
  cancelButtonLabel: 'Відміна',
  clearButtonLabel: 'Очистити',
  okButtonLabel: 'OK',
  todayButtonLabel: 'Сьогодні',
  // Toolbar titles
  datePickerToolbarTitle: 'Вибрати дату',
  dateTimePickerToolbarTitle: 'Вибрати дату і час',
  timePickerToolbarTitle: 'Вибрати час',
  dateRangePickerToolbarTitle: 'Вибрати календарний період',
  // Clock labels
  clockLabelText: function clockLabelText(view, time, adapter) {
    return "\u0412\u0438\u0431\u0440\u0430\u0442\u0438 ".concat(timeViews[view], ". ").concat(time === null ? 'Час не вибраний' : "\u0412\u0438\u0431\u0440\u0430\u043D\u043E \u0447\u0430\u0441 ".concat(adapter.format(time, 'fullTime')));
  },
  hoursClockNumberText: function hoursClockNumberText(hours) {
    return "".concat(hours, " \u0433\u043E\u0434\u0438\u043D");
  },
  minutesClockNumberText: function minutesClockNumberText(minutes) {
    return "".concat(minutes, " \u0445\u0432\u0438\u043B\u0438\u043D");
  },
  secondsClockNumberText: function secondsClockNumberText(seconds) {
    return "".concat(seconds, " \u0441\u0435\u043A\u0443\u043D\u0434");
  },
  // Digital clock labels
  selectViewText: function selectViewText(view) {
    return "\u0412\u0438\u0431\u0440\u0430\u0442\u0438 ".concat(timeViews[view]);
  },
  // Calendar labels
  calendarWeekNumberHeaderLabel: 'Номер тижня',
  calendarWeekNumberHeaderText: '#',
  calendarWeekNumberAriaLabelText: function calendarWeekNumberAriaLabelText(weekNumber) {
    return "\u0422\u0438\u0436\u0434\u0435\u043D\u044C ".concat(weekNumber);
  },
  calendarWeekNumberText: function calendarWeekNumberText(weekNumber) {
    return "".concat(weekNumber);
  },
  // Open picker labels
  openDatePickerDialogue: function openDatePickerDialogue(value, utils) {
    return value !== null && utils.isValid(value) ? "\u041E\u0431\u0435\u0440\u0456\u0442\u044C \u0434\u0430\u0442\u0443, \u043E\u0431\u0440\u0430\u043D\u0430 \u0434\u0430\u0442\u0430  ".concat(utils.format(value, 'fullDate')) : 'Оберіть дату';
  },
  openTimePickerDialogue: function openTimePickerDialogue(value, utils) {
    return value !== null && utils.isValid(value) ? "\u041E\u0431\u0435\u0440\u0456\u0442\u044C \u0447\u0430\u0441, \u043E\u0431\u0440\u0430\u043D\u0438\u0439 \u0447\u0430\u0441  ".concat(utils.format(value, 'fullTime')) : 'Оберіть час';
  },
  // fieldClearLabel: 'Clear value',

  // Table labels
  timeTableLabel: 'оберіть час',
  dateTableLabel: 'оберіть дату',
  // Field section placeholders
  fieldYearPlaceholder: function fieldYearPlaceholder(params) {
    return 'Y'.repeat(params.digitAmount);
  },
  fieldMonthPlaceholder: function fieldMonthPlaceholder(params) {
    return params.contentType === 'letter' ? 'MMMM' : 'MM';
  },
  fieldDayPlaceholder: function fieldDayPlaceholder() {
    return 'DD';
  },
  fieldWeekDayPlaceholder: function fieldWeekDayPlaceholder(params) {
    return params.contentType === 'letter' ? 'EEEE' : 'EE';
  },
  fieldHoursPlaceholder: function fieldHoursPlaceholder() {
    return 'hh';
  },
  fieldMinutesPlaceholder: function fieldMinutesPlaceholder() {
    return 'mm';
  },
  fieldSecondsPlaceholder: function fieldSecondsPlaceholder() {
    return 'ss';
  },
  fieldMeridiemPlaceholder: function fieldMeridiemPlaceholder() {
    return 'aa';
  }
};
export var ukUA = getPickersLocalization(ukUAPickers);