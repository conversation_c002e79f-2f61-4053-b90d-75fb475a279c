import _extends from "@babel/runtime/helpers/esm/extends";
import _objectWithoutProperties from "@babel/runtime/helpers/esm/objectWithoutProperties";
var _excluded = ["align", "className", "selected", "typographyClassName", "value", "variant", "width"];
import * as React from 'react';
import clsx from 'clsx';
import Button from '@mui/material/Button';
import { styled, useThemeProps } from '@mui/material/styles';
import { unstable_composeClasses as composeClasses } from '@mui/utils';
import { PickersToolbarText } from './PickersToolbarText';
import { getPickersToolbarUtilityClass } from './pickersToolbarClasses';
import { jsx as _jsx } from "react/jsx-runtime";
var useUtilityClasses = function useUtilityClasses(ownerState) {
  var classes = ownerState.classes;
  var slots = {
    root: ['root']
  };
  return composeClasses(slots, getPickersToolbarUtilityClass, classes);
};
var PickersToolbarButtonRoot = styled(But<PERSON>, {
  name: 'MuiPickersToolbarButton',
  slot: 'Root',
  overridesResolver: function overridesResolver(_, styles) {
    return styles.root;
  }
})({
  padding: 0,
  minWidth: 16,
  textTransform: 'none'
});
export var PickersToolbarButton = /*#__PURE__*/React.forwardRef(function PickersToolbarButton(inProps, ref) {
  var props = useThemeProps({
    props: inProps,
    name: 'MuiPickersToolbarButton'
  });
  var align = props.align,
    className = props.className,
    selected = props.selected,
    typographyClassName = props.typographyClassName,
    value = props.value,
    variant = props.variant,
    width = props.width,
    other = _objectWithoutProperties(props, _excluded);
  var classes = useUtilityClasses(props);
  return /*#__PURE__*/_jsx(PickersToolbarButtonRoot, _extends({
    variant: "text",
    ref: ref,
    className: clsx(className, classes.root)
  }, width ? {
    sx: {
      width: width
    }
  } : {}, other, {
    children: /*#__PURE__*/_jsx(PickersToolbarText, {
      align: align,
      className: typographyClassName,
      variant: variant,
      value: value,
      selected: selected
    })
  }));
});