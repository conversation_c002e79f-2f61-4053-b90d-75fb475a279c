{"ast": null, "code": "// Source: https://www.unicode.org/cldr/charts/32/summary/te.html\n\nvar formatDistanceLocale = {\n  lessThanXSeconds: {\n    standalone: {\n      one: 'సెకను కన్నా తక్కువ',\n      other: '{{count}} సెకన్ల కన్నా తక్కువ'\n    },\n    withPreposition: {\n      one: 'సెకను',\n      other: '{{count}} సెకన్ల'\n    }\n  },\n  xSeconds: {\n    standalone: {\n      one: 'ఒక సెకను',\n      // CLDR #1314\n      other: '{{count}} సెకన్ల'\n    },\n    withPreposition: {\n      one: 'ఒక సెకను',\n      other: '{{count}} సెకన్ల'\n    }\n  },\n  halfAMinute: {\n    standalone: 'అర నిమిషం',\n    withPreposition: 'అర నిమిషం'\n  },\n  lessThanXMinutes: {\n    standalone: {\n      one: 'ఒక నిమిషం కన్నా తక్కువ',\n      other: '{{count}} నిమిషాల కన్నా తక్కువ'\n    },\n    withPreposition: {\n      one: 'ఒక నిమిషం',\n      other: '{{count}} నిమిషాల'\n    }\n  },\n  xMinutes: {\n    standalone: {\n      one: 'ఒక నిమిషం',\n      // CLDR #1311\n      other: '{{count}} నిమిషాలు'\n    },\n    withPreposition: {\n      one: 'ఒక నిమిషం',\n      // CLDR #1311\n      other: '{{count}} నిమిషాల'\n    }\n  },\n  aboutXHours: {\n    standalone: {\n      one: 'సుమారు ఒక గంట',\n      other: 'సుమారు {{count}} గంటలు'\n    },\n    withPreposition: {\n      one: 'సుమారు ఒక గంట',\n      other: 'సుమారు {{count}} గంటల'\n    }\n  },\n  xHours: {\n    standalone: {\n      one: 'ఒక గంట',\n      // CLDR #1308\n      other: '{{count}} గంటలు'\n    },\n    withPreposition: {\n      one: 'ఒక గంట',\n      other: '{{count}} గంటల'\n    }\n  },\n  xDays: {\n    standalone: {\n      one: 'ఒక రోజు',\n      // CLDR #1292\n      other: '{{count}} రోజులు'\n    },\n    withPreposition: {\n      one: 'ఒక రోజు',\n      other: '{{count}} రోజుల'\n    }\n  },\n  aboutXWeeks: {\n    standalone: {\n      one: 'సుమారు ఒక వారం',\n      other: 'సుమారు {{count}} వారాలు'\n    },\n    withPreposition: {\n      one: 'సుమారు ఒక వారం',\n      other: 'సుమారు {{count}} వారాలల'\n    }\n  },\n  xWeeks: {\n    standalone: {\n      one: 'ఒక వారం',\n      other: '{{count}} వారాలు'\n    },\n    withPreposition: {\n      one: 'ఒక వారం',\n      other: '{{count}} వారాలల'\n    }\n  },\n  aboutXMonths: {\n    standalone: {\n      one: 'సుమారు ఒక నెల',\n      other: 'సుమారు {{count}} నెలలు'\n    },\n    withPreposition: {\n      one: 'సుమారు ఒక నెల',\n      other: 'సుమారు {{count}} నెలల'\n    }\n  },\n  xMonths: {\n    standalone: {\n      one: 'ఒక నెల',\n      // CLDR #1281\n      other: '{{count}} నెలలు'\n    },\n    withPreposition: {\n      one: 'ఒక నెల',\n      other: '{{count}} నెలల'\n    }\n  },\n  aboutXYears: {\n    standalone: {\n      one: 'సుమారు ఒక సంవత్సరం',\n      other: 'సుమారు {{count}} సంవత్సరాలు'\n    },\n    withPreposition: {\n      one: 'సుమారు ఒక సంవత్సరం',\n      other: 'సుమారు {{count}} సంవత్సరాల'\n    }\n  },\n  xYears: {\n    standalone: {\n      one: 'ఒక సంవత్సరం',\n      // CLDR #1275\n      other: '{{count}} సంవత్సరాలు'\n    },\n    withPreposition: {\n      one: 'ఒక సంవత్సరం',\n      other: '{{count}} సంవత్సరాల'\n    }\n  },\n  overXYears: {\n    standalone: {\n      one: 'ఒక సంవత్సరం పైగా',\n      other: '{{count}} సంవత్సరాలకు పైగా'\n    },\n    withPreposition: {\n      one: 'ఒక సంవత్సరం',\n      other: '{{count}} సంవత్సరాల'\n    }\n  },\n  almostXYears: {\n    standalone: {\n      one: 'దాదాపు ఒక సంవత్సరం',\n      other: 'దాదాపు {{count}} సంవత్సరాలు'\n    },\n    withPreposition: {\n      one: 'దాదాపు ఒక సంవత్సరం',\n      other: 'దాదాపు {{count}} సంవత్సరాల'\n    }\n  }\n};\nvar formatDistance = function formatDistance(token, count, options) {\n  var result;\n  var tokenValue = options !== null && options !== void 0 && options.addSuffix ? formatDistanceLocale[token].withPreposition : formatDistanceLocale[token].standalone;\n  if (typeof tokenValue === 'string') {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else {\n    result = tokenValue.other.replace('{{count}}', String(count));\n  }\n  if (options !== null && options !== void 0 && options.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return result + 'లో';\n    } else {\n      return result + ' క్రితం';\n    }\n  }\n  return result;\n};\nexport default formatDistance;", "map": {"version": 3, "names": ["formatDistanceLocale", "lessThanXSeconds", "standalone", "one", "other", "withPreposition", "xSeconds", "halfAMinute", "lessThanXMinutes", "xMinutes", "aboutXHours", "xHours", "xDays", "aboutXWeeks", "xWeeks", "aboutXMonths", "xMonths", "aboutXYears", "xYears", "overXYears", "almostXYears", "formatDistance", "token", "count", "options", "result", "tokenValue", "addSuffix", "replace", "String", "comparison"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/esm/locale/te/_lib/formatDistance/index.js"], "sourcesContent": ["// Source: https://www.unicode.org/cldr/charts/32/summary/te.html\n\nvar formatDistanceLocale = {\n  lessThanXSeconds: {\n    standalone: {\n      one: 'సెకను కన్నా తక్కువ',\n      other: '{{count}} సెకన్ల కన్నా తక్కువ'\n    },\n    withPreposition: {\n      one: 'సెకను',\n      other: '{{count}} సెకన్ల'\n    }\n  },\n  xSeconds: {\n    standalone: {\n      one: 'ఒక సెకను',\n      // CLDR #1314\n      other: '{{count}} సెకన్ల'\n    },\n    withPreposition: {\n      one: 'ఒక సెకను',\n      other: '{{count}} సెకన్ల'\n    }\n  },\n  halfAMinute: {\n    standalone: 'అర నిమిషం',\n    withPreposition: 'అర నిమిషం'\n  },\n  lessThanXMinutes: {\n    standalone: {\n      one: 'ఒక నిమిషం కన్నా తక్కువ',\n      other: '{{count}} నిమిషాల కన్నా తక్కువ'\n    },\n    withPreposition: {\n      one: 'ఒక నిమిషం',\n      other: '{{count}} నిమిషాల'\n    }\n  },\n  xMinutes: {\n    standalone: {\n      one: 'ఒక నిమిషం',\n      // CLDR #1311\n      other: '{{count}} నిమిషాలు'\n    },\n    withPreposition: {\n      one: 'ఒక నిమిషం',\n      // CLDR #1311\n      other: '{{count}} నిమిషాల'\n    }\n  },\n  aboutXHours: {\n    standalone: {\n      one: 'సుమారు ఒక గంట',\n      other: 'సుమారు {{count}} గంటలు'\n    },\n    withPreposition: {\n      one: 'సుమారు ఒక గంట',\n      other: 'సుమారు {{count}} గంటల'\n    }\n  },\n  xHours: {\n    standalone: {\n      one: 'ఒక గంట',\n      // CLDR #1308\n      other: '{{count}} గంటలు'\n    },\n    withPreposition: {\n      one: 'ఒక గంట',\n      other: '{{count}} గంటల'\n    }\n  },\n  xDays: {\n    standalone: {\n      one: 'ఒక రోజు',\n      // CLDR #1292\n      other: '{{count}} రోజులు'\n    },\n    withPreposition: {\n      one: 'ఒక రోజు',\n      other: '{{count}} రోజుల'\n    }\n  },\n  aboutXWeeks: {\n    standalone: {\n      one: 'సుమారు ఒక వారం',\n      other: 'సుమారు {{count}} వారాలు'\n    },\n    withPreposition: {\n      one: 'సుమారు ఒక వారం',\n      other: 'సుమారు {{count}} వారాలల'\n    }\n  },\n  xWeeks: {\n    standalone: {\n      one: 'ఒక వారం',\n      other: '{{count}} వారాలు'\n    },\n    withPreposition: {\n      one: 'ఒక వారం',\n      other: '{{count}} వారాలల'\n    }\n  },\n  aboutXMonths: {\n    standalone: {\n      one: 'సుమారు ఒక నెల',\n      other: 'సుమారు {{count}} నెలలు'\n    },\n    withPreposition: {\n      one: 'సుమారు ఒక నెల',\n      other: 'సుమారు {{count}} నెలల'\n    }\n  },\n  xMonths: {\n    standalone: {\n      one: 'ఒక నెల',\n      // CLDR #1281\n      other: '{{count}} నెలలు'\n    },\n    withPreposition: {\n      one: 'ఒక నెల',\n      other: '{{count}} నెలల'\n    }\n  },\n  aboutXYears: {\n    standalone: {\n      one: 'సుమారు ఒక సంవత్సరం',\n      other: 'సుమారు {{count}} సంవత్సరాలు'\n    },\n    withPreposition: {\n      one: 'సుమారు ఒక సంవత్సరం',\n      other: 'సుమారు {{count}} సంవత్సరాల'\n    }\n  },\n  xYears: {\n    standalone: {\n      one: 'ఒక సంవత్సరం',\n      // CLDR #1275\n      other: '{{count}} సంవత్సరాలు'\n    },\n    withPreposition: {\n      one: 'ఒక సంవత్సరం',\n      other: '{{count}} సంవత్సరాల'\n    }\n  },\n  overXYears: {\n    standalone: {\n      one: 'ఒక సంవత్సరం పైగా',\n      other: '{{count}} సంవత్సరాలకు పైగా'\n    },\n    withPreposition: {\n      one: 'ఒక సంవత్సరం',\n      other: '{{count}} సంవత్సరాల'\n    }\n  },\n  almostXYears: {\n    standalone: {\n      one: 'దాదాపు ఒక సంవత్సరం',\n      other: 'దాదాపు {{count}} సంవత్సరాలు'\n    },\n    withPreposition: {\n      one: 'దాదాపు ఒక సంవత్సరం',\n      other: 'దాదాపు {{count}} సంవత్సరాల'\n    }\n  }\n};\nvar formatDistance = function formatDistance(token, count, options) {\n  var result;\n  var tokenValue = options !== null && options !== void 0 && options.addSuffix ? formatDistanceLocale[token].withPreposition : formatDistanceLocale[token].standalone;\n  if (typeof tokenValue === 'string') {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else {\n    result = tokenValue.other.replace('{{count}}', String(count));\n  }\n  if (options !== null && options !== void 0 && options.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return result + 'లో';\n    } else {\n      return result + ' క్రితం';\n    }\n  }\n  return result;\n};\nexport default formatDistance;"], "mappings": "AAAA;;AAEA,IAAIA,oBAAoB,GAAG;EACzBC,gBAAgB,EAAE;IAChBC,UAAU,EAAE;MACVC,GAAG,EAAE,oBAAoB;MACzBC,KAAK,EAAE;IACT,CAAC;IACDC,eAAe,EAAE;MACfF,GAAG,EAAE,OAAO;MACZC,KAAK,EAAE;IACT;EACF,CAAC;EACDE,QAAQ,EAAE;IACRJ,UAAU,EAAE;MACVC,GAAG,EAAE,UAAU;MACf;MACAC,KAAK,EAAE;IACT,CAAC;IACDC,eAAe,EAAE;MACfF,GAAG,EAAE,UAAU;MACfC,KAAK,EAAE;IACT;EACF,CAAC;EACDG,WAAW,EAAE;IACXL,UAAU,EAAE,WAAW;IACvBG,eAAe,EAAE;EACnB,CAAC;EACDG,gBAAgB,EAAE;IAChBN,UAAU,EAAE;MACVC,GAAG,EAAE,wBAAwB;MAC7BC,KAAK,EAAE;IACT,CAAC;IACDC,eAAe,EAAE;MACfF,GAAG,EAAE,WAAW;MAChBC,KAAK,EAAE;IACT;EACF,CAAC;EACDK,QAAQ,EAAE;IACRP,UAAU,EAAE;MACVC,GAAG,EAAE,WAAW;MAChB;MACAC,KAAK,EAAE;IACT,CAAC;IACDC,eAAe,EAAE;MACfF,GAAG,EAAE,WAAW;MAChB;MACAC,KAAK,EAAE;IACT;EACF,CAAC;EACDM,WAAW,EAAE;IACXR,UAAU,EAAE;MACVC,GAAG,EAAE,eAAe;MACpBC,KAAK,EAAE;IACT,CAAC;IACDC,eAAe,EAAE;MACfF,GAAG,EAAE,eAAe;MACpBC,KAAK,EAAE;IACT;EACF,CAAC;EACDO,MAAM,EAAE;IACNT,UAAU,EAAE;MACVC,GAAG,EAAE,QAAQ;MACb;MACAC,KAAK,EAAE;IACT,CAAC;IACDC,eAAe,EAAE;MACfF,GAAG,EAAE,QAAQ;MACbC,KAAK,EAAE;IACT;EACF,CAAC;EACDQ,KAAK,EAAE;IACLV,UAAU,EAAE;MACVC,GAAG,EAAE,SAAS;MACd;MACAC,KAAK,EAAE;IACT,CAAC;IACDC,eAAe,EAAE;MACfF,GAAG,EAAE,SAAS;MACdC,KAAK,EAAE;IACT;EACF,CAAC;EACDS,WAAW,EAAE;IACXX,UAAU,EAAE;MACVC,GAAG,EAAE,gBAAgB;MACrBC,KAAK,EAAE;IACT,CAAC;IACDC,eAAe,EAAE;MACfF,GAAG,EAAE,gBAAgB;MACrBC,KAAK,EAAE;IACT;EACF,CAAC;EACDU,MAAM,EAAE;IACNZ,UAAU,EAAE;MACVC,GAAG,EAAE,SAAS;MACdC,KAAK,EAAE;IACT,CAAC;IACDC,eAAe,EAAE;MACfF,GAAG,EAAE,SAAS;MACdC,KAAK,EAAE;IACT;EACF,CAAC;EACDW,YAAY,EAAE;IACZb,UAAU,EAAE;MACVC,GAAG,EAAE,eAAe;MACpBC,KAAK,EAAE;IACT,CAAC;IACDC,eAAe,EAAE;MACfF,GAAG,EAAE,eAAe;MACpBC,KAAK,EAAE;IACT;EACF,CAAC;EACDY,OAAO,EAAE;IACPd,UAAU,EAAE;MACVC,GAAG,EAAE,QAAQ;MACb;MACAC,KAAK,EAAE;IACT,CAAC;IACDC,eAAe,EAAE;MACfF,GAAG,EAAE,QAAQ;MACbC,KAAK,EAAE;IACT;EACF,CAAC;EACDa,WAAW,EAAE;IACXf,UAAU,EAAE;MACVC,GAAG,EAAE,oBAAoB;MACzBC,KAAK,EAAE;IACT,CAAC;IACDC,eAAe,EAAE;MACfF,GAAG,EAAE,oBAAoB;MACzBC,KAAK,EAAE;IACT;EACF,CAAC;EACDc,MAAM,EAAE;IACNhB,UAAU,EAAE;MACVC,GAAG,EAAE,aAAa;MAClB;MACAC,KAAK,EAAE;IACT,CAAC;IACDC,eAAe,EAAE;MACfF,GAAG,EAAE,aAAa;MAClBC,KAAK,EAAE;IACT;EACF,CAAC;EACDe,UAAU,EAAE;IACVjB,UAAU,EAAE;MACVC,GAAG,EAAE,kBAAkB;MACvBC,KAAK,EAAE;IACT,CAAC;IACDC,eAAe,EAAE;MACfF,GAAG,EAAE,aAAa;MAClBC,KAAK,EAAE;IACT;EACF,CAAC;EACDgB,YAAY,EAAE;IACZlB,UAAU,EAAE;MACVC,GAAG,EAAE,oBAAoB;MACzBC,KAAK,EAAE;IACT,CAAC;IACDC,eAAe,EAAE;MACfF,GAAG,EAAE,oBAAoB;MACzBC,KAAK,EAAE;IACT;EACF;AACF,CAAC;AACD,IAAIiB,cAAc,GAAG,SAASA,cAAcA,CAACC,KAAK,EAAEC,KAAK,EAAEC,OAAO,EAAE;EAClE,IAAIC,MAAM;EACV,IAAIC,UAAU,GAAGF,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,IAAIA,OAAO,CAACG,SAAS,GAAG3B,oBAAoB,CAACsB,KAAK,CAAC,CAACjB,eAAe,GAAGL,oBAAoB,CAACsB,KAAK,CAAC,CAACpB,UAAU;EACnK,IAAI,OAAOwB,UAAU,KAAK,QAAQ,EAAE;IAClCD,MAAM,GAAGC,UAAU;EACrB,CAAC,MAAM,IAAIH,KAAK,KAAK,CAAC,EAAE;IACtBE,MAAM,GAAGC,UAAU,CAACvB,GAAG;EACzB,CAAC,MAAM;IACLsB,MAAM,GAAGC,UAAU,CAACtB,KAAK,CAACwB,OAAO,CAAC,WAAW,EAAEC,MAAM,CAACN,KAAK,CAAC,CAAC;EAC/D;EACA,IAAIC,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,IAAIA,OAAO,CAACG,SAAS,EAAE;IAC/D,IAAIH,OAAO,CAACM,UAAU,IAAIN,OAAO,CAACM,UAAU,GAAG,CAAC,EAAE;MAChD,OAAOL,MAAM,GAAG,IAAI;IACtB,CAAC,MAAM;MACL,OAAOA,MAAM,GAAG,SAAS;IAC3B;EACF;EACA,OAAOA,MAAM;AACf,CAAC;AACD,eAAeJ,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}