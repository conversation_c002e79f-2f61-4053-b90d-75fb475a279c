import * as React from 'react';
import { SlotComponentProps } from '@mui/base/utils';
import { PickersDay, PickersDayProps, ExportedPickersDayProps } from '../PickersDay/PickersDay';
import { PickerOnChangeFn } from '../internals/hooks/useViews';
import { SlideDirection, SlideTransitionProps } from './PickersSlideTransition';
import { BaseDateValidationProps, DayValidationProps, MonthValidationProps, YearValidationProps } from '../internals/models/validation';
import { DayCalendarClasses } from './dayCalendarClasses';
import { SlotsAndSlotProps } from '../internals/utils/slots-migration';
import { TimezoneProps } from '../models';
import { DefaultizedProps } from '../internals/models/helpers';
export interface DayCalendarSlotsComponent<TDate> {
    /**
     * Custom component for day.
     * Check the [PickersDay](https://mui.com/x/api/date-pickers/pickers-day/) component.
     * @default PickersDay
     */
    Day?: React.ElementType<PickersDayProps<TDate>>;
}
export interface DayCalendarSlotsComponentsProps<TDate> {
    day?: SlotComponentProps<typeof PickersDay, {}, DayCalendarProps<TDate> & {
        day: TDate;
        selected: boolean;
    }>;
}
export interface ExportedDayCalendarProps<TDate> extends ExportedPickersDayProps {
    /**
     * If `true`, calls `renderLoading` instead of rendering the day calendar.
     * Can be used to preload information and show it in calendar.
     * @default false
     */
    loading?: boolean;
    /**
     * Component displaying when passed `loading` true.
     * @returns {React.ReactNode} The node to render when loading.
     * @default () => "..."
     */
    renderLoading?: () => React.ReactNode;
    /**
     * Formats the day of week displayed in the calendar header.
     * @param {string} day The day of week provided by the adapter.  Deprecated, will be removed in v7: Use `date` instead.
     * @param {TDate} date The date of the day of week provided by the adapter.
     * @returns {string} The name to display.
     * @default (_day: string, date: TDate) => adapter.format(date, 'weekdayShort').charAt(0).toUpperCase()
     */
    dayOfWeekFormatter?: (day: string, date: TDate) => string;
    /**
     * If `true`, the week number will be display in the calendar.
     */
    displayWeekNumber?: boolean;
    /**
     * Calendar will show more weeks in order to match this value.
     * Put it to 6 for having fix number of week in Gregorian calendars
     * @default undefined
     */
    fixedWeekNumber?: number;
}
export interface DayCalendarProps<TDate> extends ExportedDayCalendarProps<TDate>, DayValidationProps<TDate>, MonthValidationProps<TDate>, YearValidationProps<TDate>, Required<BaseDateValidationProps<TDate>>, DefaultizedProps<TimezoneProps, 'timezone'>, SlotsAndSlotProps<DayCalendarSlotsComponent<TDate>, DayCalendarSlotsComponentsProps<TDate>> {
    autoFocus?: boolean;
    className?: string;
    currentMonth: TDate;
    selectedDays: (TDate | null)[];
    onSelectedDaysChange: PickerOnChangeFn<TDate>;
    disabled?: boolean;
    focusedDay: TDate | null;
    isMonthSwitchingAnimating: boolean;
    onFocusedDayChange: (newFocusedDay: TDate) => void;
    onMonthSwitchingAnimationEnd: () => void;
    readOnly?: boolean;
    reduceAnimations: boolean;
    slideDirection: SlideDirection;
    TransitionProps?: Partial<SlideTransitionProps>;
    hasFocus?: boolean;
    onFocusedViewChange?: (newHasFocus: boolean) => void;
    gridLabelId?: string;
    /**
     * Override or extend the styles applied to the component.
     */
    classes?: Partial<DayCalendarClasses>;
}
/**
 * @ignore - do not document.
 */
export declare function DayCalendar<TDate>(inProps: DayCalendarProps<TDate>): React.JSX.Element;
