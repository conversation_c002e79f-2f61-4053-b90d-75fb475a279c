import { getPickersLocalization } from './utils/getPickersLocalization';
var timeViews = {
  hours: 'godzin',
  minutes: 'minut',
  seconds: 'sekund',
  meridiem: 'popołudnie'
};
var plPLPickers = {
  // Calendar navigation
  previousMonth: 'Poprzedni miesiąc',
  nextMonth: 'Następny miesiąc',
  // View navigation
  openPreviousView: 'otwórz poprzedni widok',
  openNextView: 'otwórz następny widok',
  calendarViewSwitchingButtonAriaLabel: function calendarViewSwitchingButtonAriaLabel(view) {
    return view === 'year' ? 'otwarty jest widok roku, przełącz na widok kalendarza' : 'otwarty jest widok kalendarza, przełącz na widok roku';
  },
  // DateRange placeholders
  start: 'Początek',
  end: 'Koniec',
  // Action bar
  cancelButtonLabel: 'Anuluj',
  clearButtonLabel: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',
  okButtonLabel: '<PERSON>at<PERSON><PERSON><PERSON>',
  todayButtonLabel: 'Dzisiaj',
  // Toolbar titles
  datePickerToolbarTitle: 'Wybierz datę',
  dateTimePickerToolbarTitle: 'Wybierz datę i czas',
  timePickerToolbarTitle: 'Wybierz czas',
  dateRangePickerToolbarTitle: 'Wybierz zakres dat',
  // Clock labels
  clockLabelText: function clockLabelText(view, time, adapter) {
    return "Wybierz ".concat(timeViews[view], ". ").concat(time === null ? 'Nie wybrano czasu' : "Wybrany czas to ".concat(adapter.format(time, 'fullTime')));
  },
  hoursClockNumberText: function hoursClockNumberText(hours) {
    return "".concat(hours, " godzin");
  },
  minutesClockNumberText: function minutesClockNumberText(minutes) {
    return "".concat(minutes, " minut");
  },
  secondsClockNumberText: function secondsClockNumberText(seconds) {
    return "".concat(seconds, " sekund");
  },
  // Digital clock labels
  selectViewText: function selectViewText(view) {
    return "Wybierz ".concat(timeViews[view]);
  },
  // Calendar labels
  calendarWeekNumberHeaderLabel: 'Numer tygodnia',
  calendarWeekNumberHeaderText: '#',
  calendarWeekNumberAriaLabelText: function calendarWeekNumberAriaLabelText(weekNumber) {
    return "Tydzie\u0144 ".concat(weekNumber);
  },
  calendarWeekNumberText: function calendarWeekNumberText(weekNumber) {
    return "".concat(weekNumber);
  },
  // Open picker labels
  openDatePickerDialogue: function openDatePickerDialogue(value, utils) {
    return value != null && utils.isValid(value) ? "Wybierz dat\u0119, obecnie wybrana data to ".concat(utils.format(value, 'fullDate')) : 'Wybierz datę';
  },
  openTimePickerDialogue: function openTimePickerDialogue(value, utils) {
    return value !== null && utils.isValid(value) ? "Wybierz czas, obecnie wybrany czas to ".concat(utils.format(value, 'fullTime')) : 'Wybierz czas';
  },
  // fieldClearLabel: 'Clear value',

  // Table labels
  timeTableLabel: 'wybierz czas',
  dateTableLabel: 'wybierz datę'

  // Field section placeholders
  // fieldYearPlaceholder: params => 'Y'.repeat(params.digitAmount),
  // fieldMonthPlaceholder: params => params.contentType === 'letter' ? 'MMMM' : 'MM',
  // fieldDayPlaceholder: () => 'DD',
  // fieldWeekDayPlaceholder: params => params.contentType === 'letter' ? 'EEEE' : 'EE',
  // fieldHoursPlaceholder: () => 'hh',
  // fieldMinutesPlaceholder: () => 'mm',
  // fieldSecondsPlaceholder: () => 'ss',
  // fieldMeridiemPlaceholder: () => 'aa',
};
export var plPL = getPickersLocalization(plPLPickers);