import { getPickersLocalization } from './utils/getPickersLocalization';
// Translation map for Clock Label
var timeViews = {
  hours: 'часы',
  minutes: 'минуты',
  seconds: 'секунды',
  meridiem: 'меридием'
};
var ruRUPickers = {
  // Calendar navigation
  previousMonth: 'Предыдущий месяц',
  nextMonth: 'Следующий месяц',
  // View navigation
  openPreviousView: 'открыть предыдущий вид',
  openNextView: 'открыть следующий вид',
  calendarViewSwitchingButtonAriaLabel: function calendarViewSwitchingButtonAriaLabel(view) {
    return view === 'year' ? 'открыт годовой вид, переключить на календарный вид' : 'открыт календарный вид, переключить на годовой вид';
  },
  // DateRange placeholders
  start: 'Начало',
  end: 'Конец',
  // Action bar
  cancelButtonLabel: 'Отмена',
  clearButtonLabel: 'Очистить',
  okButtonLabel: 'Ок',
  todayButtonLabel: 'Сегодня',
  // Toolbar titles
  datePickerToolbarTitle: 'Выбрать дату',
  dateTimePickerToolbarTitle: 'Выбрать дату и время',
  timePickerToolbarTitle: 'Выбрать время',
  dateRangePickerToolbarTitle: 'Выбрать период',
  // Clock labels
  clockLabelText: function clockLabelText(view, time, adapter) {
    return "\u0412\u044B\u0431\u0440\u0430\u0442\u044C ".concat(timeViews[view], ". ").concat(time === null ? 'Время не выбрано' : "\u0412\u044B\u0431\u0440\u0430\u043D\u043E \u0432\u0440\u0435\u043C\u044F ".concat(adapter.format(time, 'fullTime')));
  },
  hoursClockNumberText: function hoursClockNumberText(hours) {
    return "".concat(hours, " \u0447\u0430\u0441\u043E\u0432");
  },
  minutesClockNumberText: function minutesClockNumberText(minutes) {
    return "".concat(minutes, " \u043C\u0438\u043D\u0443\u0442");
  },
  secondsClockNumberText: function secondsClockNumberText(seconds) {
    return "".concat(seconds, " \u0441\u0435\u043A\u0443\u043D\u0434");
  },
  // Digital clock labels
  selectViewText: function selectViewText(view) {
    return "\u0412\u044B\u0431\u0440\u0430\u0442\u044C ".concat(timeViews[view]);
  },
  // Calendar labels
  calendarWeekNumberHeaderLabel: 'Номер недели',
  calendarWeekNumberHeaderText: '№',
  calendarWeekNumberAriaLabelText: function calendarWeekNumberAriaLabelText(weekNumber) {
    return "\u041D\u0435\u0434\u0435\u043B\u044F ".concat(weekNumber);
  },
  calendarWeekNumberText: function calendarWeekNumberText(weekNumber) {
    return "".concat(weekNumber);
  },
  // Open picker labels
  openDatePickerDialogue: function openDatePickerDialogue(value, utils) {
    return value !== null && utils.isValid(value) ? "\u0412\u044B\u0431\u0435\u0440\u0438\u0442\u0435 \u0434\u0430\u0442\u0443, \u0432\u044B\u0431\u0440\u0430\u043D\u0430 \u0434\u0430\u0442\u0430 ".concat(utils.format(value, 'fullDate')) : 'Выберите дату';
  },
  openTimePickerDialogue: function openTimePickerDialogue(value, utils) {
    return value !== null && utils.isValid(value) ? "\u0412\u044B\u0431\u0435\u0440\u0438\u0442\u0435 \u0432\u0440\u0435\u043C\u044F, \u0432\u044B\u0431\u0440\u0430\u043D\u043E \u0432\u0440\u0435\u043C\u044F ".concat(utils.format(value, 'fullTime')) : 'Выберите время';
  },
  fieldClearLabel: 'Очистить значение',
  // Table labels
  timeTableLabel: 'выбрать время',
  dateTableLabel: 'выбрать дату',
  // Field section placeholders
  fieldYearPlaceholder: function fieldYearPlaceholder(params) {
    return 'Г'.repeat(params.digitAmount);
  },
  fieldMonthPlaceholder: function fieldMonthPlaceholder(params) {
    return params.contentType === 'letter' ? 'ММММ' : 'ММ';
  },
  fieldDayPlaceholder: function fieldDayPlaceholder() {
    return 'ДД';
  },
  // fieldWeekDayPlaceholder: params => params.contentType === 'letter' ? 'EEEE' : 'EE',
  fieldHoursPlaceholder: function fieldHoursPlaceholder() {
    return 'чч';
  },
  fieldMinutesPlaceholder: function fieldMinutesPlaceholder() {
    return 'мм';
  },
  fieldSecondsPlaceholder: function fieldSecondsPlaceholder() {
    return 'сс';
  },
  fieldMeridiemPlaceholder: function fieldMeridiemPlaceholder() {
    return '(д|п)п';
  }
};
export var ruRU = getPickersLocalization(ruRUPickers);