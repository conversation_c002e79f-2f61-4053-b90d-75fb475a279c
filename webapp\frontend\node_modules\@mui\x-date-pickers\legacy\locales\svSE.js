import { getPickersLocalization } from './utils/getPickersLocalization';
var timeViews = {
  hours: 'timmar',
  minutes: 'minuter',
  seconds: 'sekunder',
  meridiem: 'meridiem'
};
var svSEPickers = {
  // Calendar navigation
  previousMonth: 'Föregående månad',
  nextMonth: 'Nästa månad',
  // View navigation
  openPreviousView: 'öppna föregående vy',
  openNextView: 'öppna nästa vy',
  calendarViewSwitchingButtonAriaLabel: function calendarViewSwitchingButtonAriaLabel(view) {
    return view === 'year' ? 'årsvyn är öppen, byt till kalendervy' : 'kalendervyn är öppen, byt till årsvy';
  },
  // DateRange placeholders
  start: 'Start',
  end: 'Slut',
  // Action bar
  cancelButtonLabel: 'Avbryt',
  clearButtonLabel: '<PERSON><PERSON>',
  okButtonLabel: 'OK',
  todayButtonLabel: 'Idag',
  // Toolbar titles
  datePickerToolbarTitle: 'Välj datum',
  dateTimePickerToolbarTitle: 'Välj datum & tid',
  timePickerToolbarTitle: 'Välj tid',
  dateRangePickerToolbarTitle: 'Välj datumintervall',
  // Clock labels
  clockLabelText: function clockLabelText(view, time, adapter) {
    return "V\xE4lj ".concat(timeViews[view], ". ").concat(time === null ? 'Ingen tid vald' : "Vald tid \xE4r ".concat(adapter.format(time, 'fullTime')));
  },
  hoursClockNumberText: function hoursClockNumberText(hours) {
    return "".concat(hours, " timmar");
  },
  minutesClockNumberText: function minutesClockNumberText(minutes) {
    return "".concat(minutes, " minuter");
  },
  secondsClockNumberText: function secondsClockNumberText(seconds) {
    return "".concat(seconds, " sekunder");
  },
  // Digital clock labels
  selectViewText: function selectViewText(view) {
    return "V\xE4lj ".concat(timeViews[view]);
  },
  // Calendar labels
  calendarWeekNumberHeaderLabel: 'Vecka nummer',
  calendarWeekNumberHeaderText: '#',
  calendarWeekNumberAriaLabelText: function calendarWeekNumberAriaLabelText(weekNumber) {
    return "Vecka ".concat(weekNumber);
  },
  calendarWeekNumberText: function calendarWeekNumberText(weekNumber) {
    return "".concat(weekNumber);
  },
  // Open picker labels
  openDatePickerDialogue: function openDatePickerDialogue(value, utils) {
    return value !== null && utils.isValid(value) ? "V\xE4lj datum, valt datum \xE4r ".concat(utils.format(value, 'fullDate')) : 'Välj datum';
  },
  openTimePickerDialogue: function openTimePickerDialogue(value, utils) {
    return value !== null && utils.isValid(value) ? "V\xE4lj tid, vald tid \xE4r ".concat(utils.format(value, 'fullTime')) : 'Välj tid';
  },
  // fieldClearLabel: 'Clear value',

  // Table labels
  timeTableLabel: 'välj tid',
  dateTableLabel: 'välj datum'

  // Field section placeholders
  // fieldYearPlaceholder: params => 'Y'.repeat(params.digitAmount),
  // fieldMonthPlaceholder: params => params.contentType === 'letter' ? 'MMMM' : 'MM',
  // fieldDayPlaceholder: () => 'DD',
  // fieldWeekDayPlaceholder: params => params.contentType === 'letter' ? 'EEEE' : 'EE',
  // fieldHoursPlaceholder: () => 'hh',
  // fieldMinutesPlaceholder: () => 'mm',
  // fieldSecondsPlaceholder: () => 'ss',
  // fieldMeridiemPlaceholder: () => 'aa',
};
export var svSE = getPickersLocalization(svSEPickers);