import _extends from "@babel/runtime/helpers/esm/extends";
import _objectWithoutProperties from "@babel/runtime/helpers/esm/objectWithoutProperties";
var _excluded = ["ampm", "ampmInClock", "autoFocus", "components", "componentsProps", "slots", "slotProps", "value", "defaultValue", "referenceDate", "disableIgnoringDatePartForTimeValidation", "maxTime", "minTime", "disableFuture", "disablePast", "minutesStep", "shouldDisableClock", "shouldDisableTime", "showViewSwitcher", "onChange", "view", "views", "openTo", "onViewChange", "focusedView", "onFocusedViewChange", "className", "disabled", "readOnly", "timezone"];
import * as React from 'react';
import clsx from 'clsx';
import PropTypes from 'prop-types';
import { styled, useThemeProps } from '@mui/material/styles';
import { unstable_composeClasses as composeClasses, unstable_useId as useId } from '@mui/utils';
import { useUtils, useNow, useLocaleText } from '../internals/hooks/useUtils';
import { PickersArrowSwitcher } from '../internals/components/PickersArrowSwitcher';
import { convertValueToMeridiem, createIsAfterIgnoreDatePart } from '../internals/utils/time-utils';
import { useViews } from '../internals/hooks/useViews';
import { useMeridiemMode } from '../internals/hooks/date-helpers-hooks';
import { PickerViewRoot } from '../internals/components/PickerViewRoot';
import { getTimeClockUtilityClass } from './timeClockClasses';
import { Clock } from './Clock';
import { getHourNumbers, getMinutesNumbers } from './ClockNumbers';
import { useControlledValueWithTimezone } from '../internals/hooks/useValueWithTimezone';
import { singleItemValueManager } from '../internals/utils/valueManagers';
import { uncapitalizeObjectKeys } from '../internals/utils/slots-migration';
import { useClockReferenceDate } from '../internals/hooks/useClockReferenceDate';
import { jsx as _jsx } from "react/jsx-runtime";
import { jsxs as _jsxs } from "react/jsx-runtime";
var useUtilityClasses = function useUtilityClasses(ownerState) {
  var classes = ownerState.classes;
  var slots = {
    root: ['root'],
    arrowSwitcher: ['arrowSwitcher']
  };
  return composeClasses(slots, getTimeClockUtilityClass, classes);
};
var TimeClockRoot = styled(PickerViewRoot, {
  name: 'MuiTimeClock',
  slot: 'Root',
  overridesResolver: function overridesResolver(props, styles) {
    return styles.root;
  }
})({
  display: 'flex',
  flexDirection: 'column',
  position: 'relative'
});
var TimeClockArrowSwitcher = styled(PickersArrowSwitcher, {
  name: 'MuiTimeClock',
  slot: 'ArrowSwitcher',
  overridesResolver: function overridesResolver(props, styles) {
    return styles.arrowSwitcher;
  }
})({
  position: 'absolute',
  right: 12,
  top: 15
});
var TIME_CLOCK_DEFAULT_VIEWS = ['hours', 'minutes'];

/**
 * Demos:
 *
 * - [TimePicker](https://mui.com/x/react-date-pickers/time-picker/)
 * - [TimeClock](https://mui.com/x/react-date-pickers/time-clock/)
 *
 * API:
 *
 * - [TimeClock API](https://mui.com/x/api/date-pickers/time-clock/)
 */
export var TimeClock = /*#__PURE__*/React.forwardRef(function TimeClock(inProps, ref) {
  var utils = useUtils();
  var props = useThemeProps({
    props: inProps,
    name: 'MuiTimeClock'
  });
  var _props$ampm = props.ampm,
    ampm = _props$ampm === void 0 ? utils.is12HourCycleInCurrentLocale() : _props$ampm,
    _props$ampmInClock = props.ampmInClock,
    ampmInClock = _props$ampmInClock === void 0 ? false : _props$ampmInClock,
    autoFocus = props.autoFocus,
    components = props.components,
    componentsProps = props.componentsProps,
    innerSlots = props.slots,
    innerSlotProps = props.slotProps,
    valueProp = props.value,
    defaultValue = props.defaultValue,
    referenceDateProp = props.referenceDate,
    _props$disableIgnorin = props.disableIgnoringDatePartForTimeValidation,
    disableIgnoringDatePartForTimeValidation = _props$disableIgnorin === void 0 ? false : _props$disableIgnorin,
    maxTime = props.maxTime,
    minTime = props.minTime,
    disableFuture = props.disableFuture,
    disablePast = props.disablePast,
    _props$minutesStep = props.minutesStep,
    minutesStep = _props$minutesStep === void 0 ? 1 : _props$minutesStep,
    shouldDisableClock = props.shouldDisableClock,
    shouldDisableTime = props.shouldDisableTime,
    showViewSwitcher = props.showViewSwitcher,
    onChange = props.onChange,
    inView = props.view,
    _props$views = props.views,
    views = _props$views === void 0 ? TIME_CLOCK_DEFAULT_VIEWS : _props$views,
    openTo = props.openTo,
    onViewChange = props.onViewChange,
    focusedView = props.focusedView,
    onFocusedViewChange = props.onFocusedViewChange,
    className = props.className,
    disabled = props.disabled,
    readOnly = props.readOnly,
    timezoneProp = props.timezone,
    other = _objectWithoutProperties(props, _excluded);
  var slots = innerSlots != null ? innerSlots : uncapitalizeObjectKeys(components);
  var slotProps = innerSlotProps != null ? innerSlotProps : componentsProps;
  var _useControlledValueWi = useControlledValueWithTimezone({
      name: 'TimeClock',
      timezone: timezoneProp,
      value: valueProp,
      defaultValue: defaultValue,
      onChange: onChange,
      valueManager: singleItemValueManager
    }),
    value = _useControlledValueWi.value,
    handleValueChange = _useControlledValueWi.handleValueChange,
    timezone = _useControlledValueWi.timezone;
  var valueOrReferenceDate = useClockReferenceDate({
    value: value,
    referenceDate: referenceDateProp,
    utils: utils,
    props: props,
    timezone: timezone
  });
  var localeText = useLocaleText();
  var now = useNow(timezone);
  var _useViews = useViews({
      view: inView,
      views: views,
      openTo: openTo,
      onViewChange: onViewChange,
      onChange: handleValueChange,
      focusedView: focusedView,
      onFocusedViewChange: onFocusedViewChange
    }),
    view = _useViews.view,
    setView = _useViews.setView,
    previousView = _useViews.previousView,
    nextView = _useViews.nextView,
    setValueAndGoToNextView = _useViews.setValueAndGoToNextView;
  var _useMeridiemMode = useMeridiemMode(valueOrReferenceDate, ampm, setValueAndGoToNextView),
    meridiemMode = _useMeridiemMode.meridiemMode,
    handleMeridiemChange = _useMeridiemMode.handleMeridiemChange;
  var isTimeDisabled = React.useCallback(function (rawValue, viewType) {
    var isAfter = createIsAfterIgnoreDatePart(disableIgnoringDatePartForTimeValidation, utils);
    var shouldCheckPastEnd = viewType === 'hours' || viewType === 'minutes' && views.includes('seconds');
    var containsValidTime = function containsValidTime(_ref) {
      var start = _ref.start,
        end = _ref.end;
      if (minTime && isAfter(minTime, end)) {
        return false;
      }
      if (maxTime && isAfter(start, maxTime)) {
        return false;
      }
      if (disableFuture && isAfter(start, now)) {
        return false;
      }
      if (disablePast && isAfter(now, shouldCheckPastEnd ? end : start)) {
        return false;
      }
      return true;
    };
    var isValidValue = function isValidValue(timeValue) {
      var step = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 1;
      if (timeValue % step !== 0) {
        return false;
      }
      if (shouldDisableClock != null && shouldDisableClock(timeValue, viewType)) {
        return false;
      }
      if (shouldDisableTime) {
        switch (viewType) {
          case 'hours':
            return !shouldDisableTime(utils.setHours(valueOrReferenceDate, timeValue), 'hours');
          case 'minutes':
            return !shouldDisableTime(utils.setMinutes(valueOrReferenceDate, timeValue), 'minutes');
          case 'seconds':
            return !shouldDisableTime(utils.setSeconds(valueOrReferenceDate, timeValue), 'seconds');
          default:
            return false;
        }
      }
      return true;
    };
    switch (viewType) {
      case 'hours':
        {
          var valueWithMeridiem = convertValueToMeridiem(rawValue, meridiemMode, ampm);
          var dateWithNewHours = utils.setHours(valueOrReferenceDate, valueWithMeridiem);
          var start = utils.setSeconds(utils.setMinutes(dateWithNewHours, 0), 0);
          var end = utils.setSeconds(utils.setMinutes(dateWithNewHours, 59), 59);
          return !containsValidTime({
            start: start,
            end: end
          }) || !isValidValue(valueWithMeridiem);
        }
      case 'minutes':
        {
          var dateWithNewMinutes = utils.setMinutes(valueOrReferenceDate, rawValue);
          var _start = utils.setSeconds(dateWithNewMinutes, 0);
          var _end = utils.setSeconds(dateWithNewMinutes, 59);
          return !containsValidTime({
            start: _start,
            end: _end
          }) || !isValidValue(rawValue, minutesStep);
        }
      case 'seconds':
        {
          var dateWithNewSeconds = utils.setSeconds(valueOrReferenceDate, rawValue);
          var _start2 = dateWithNewSeconds;
          var _end2 = dateWithNewSeconds;
          return !containsValidTime({
            start: _start2,
            end: _end2
          }) || !isValidValue(rawValue);
        }
      default:
        throw new Error('not supported');
    }
  }, [ampm, valueOrReferenceDate, disableIgnoringDatePartForTimeValidation, maxTime, meridiemMode, minTime, minutesStep, shouldDisableClock, shouldDisableTime, utils, disableFuture, disablePast, now, views]);
  var selectedId = useId();
  var viewProps = React.useMemo(function () {
    switch (view) {
      case 'hours':
        {
          var handleHoursChange = function handleHoursChange(hourValue, isFinish) {
            var valueWithMeridiem = convertValueToMeridiem(hourValue, meridiemMode, ampm);
            setValueAndGoToNextView(utils.setHours(valueOrReferenceDate, valueWithMeridiem), isFinish);
          };
          return {
            onChange: handleHoursChange,
            viewValue: utils.getHours(valueOrReferenceDate),
            children: getHourNumbers({
              value: value,
              utils: utils,
              ampm: ampm,
              onChange: handleHoursChange,
              getClockNumberText: localeText.hoursClockNumberText,
              isDisabled: function isDisabled(hourValue) {
                return disabled || isTimeDisabled(hourValue, 'hours');
              },
              selectedId: selectedId
            })
          };
        }
      case 'minutes':
        {
          var minutesValue = utils.getMinutes(valueOrReferenceDate);
          var handleMinutesChange = function handleMinutesChange(minuteValue, isFinish) {
            setValueAndGoToNextView(utils.setMinutes(valueOrReferenceDate, minuteValue), isFinish);
          };
          return {
            viewValue: minutesValue,
            onChange: handleMinutesChange,
            children: getMinutesNumbers({
              utils: utils,
              value: minutesValue,
              onChange: handleMinutesChange,
              getClockNumberText: localeText.minutesClockNumberText,
              isDisabled: function isDisabled(minuteValue) {
                return disabled || isTimeDisabled(minuteValue, 'minutes');
              },
              selectedId: selectedId
            })
          };
        }
      case 'seconds':
        {
          var secondsValue = utils.getSeconds(valueOrReferenceDate);
          var handleSecondsChange = function handleSecondsChange(secondValue, isFinish) {
            setValueAndGoToNextView(utils.setSeconds(valueOrReferenceDate, secondValue), isFinish);
          };
          return {
            viewValue: secondsValue,
            onChange: handleSecondsChange,
            children: getMinutesNumbers({
              utils: utils,
              value: secondsValue,
              onChange: handleSecondsChange,
              getClockNumberText: localeText.secondsClockNumberText,
              isDisabled: function isDisabled(secondValue) {
                return disabled || isTimeDisabled(secondValue, 'seconds');
              },
              selectedId: selectedId
            })
          };
        }
      default:
        throw new Error('You must provide the type for ClockView');
    }
  }, [view, utils, value, ampm, localeText.hoursClockNumberText, localeText.minutesClockNumberText, localeText.secondsClockNumberText, meridiemMode, setValueAndGoToNextView, valueOrReferenceDate, isTimeDisabled, selectedId, disabled]);
  var ownerState = props;
  var classes = useUtilityClasses(ownerState);
  return /*#__PURE__*/_jsxs(TimeClockRoot, _extends({
    ref: ref,
    className: clsx(classes.root, className),
    ownerState: ownerState
  }, other, {
    children: [/*#__PURE__*/_jsx(Clock, _extends({
      autoFocus: autoFocus != null ? autoFocus : !!focusedView,
      ampmInClock: ampmInClock && views.includes('hours'),
      value: value,
      type: view,
      ampm: ampm,
      minutesStep: minutesStep,
      isTimeDisabled: isTimeDisabled,
      meridiemMode: meridiemMode,
      handleMeridiemChange: handleMeridiemChange,
      selectedId: selectedId,
      disabled: disabled,
      readOnly: readOnly
    }, viewProps)), showViewSwitcher && /*#__PURE__*/_jsx(TimeClockArrowSwitcher, {
      className: classes.arrowSwitcher,
      slots: slots,
      slotProps: slotProps,
      onGoToPrevious: function onGoToPrevious() {
        return setView(previousView);
      },
      isPreviousDisabled: !previousView,
      previousLabel: localeText.openPreviousView,
      onGoToNext: function onGoToNext() {
        return setView(nextView);
      },
      isNextDisabled: !nextView,
      nextLabel: localeText.openNextView,
      ownerState: ownerState
    })]
  }));
});
process.env.NODE_ENV !== "production" ? TimeClock.propTypes = {
  // ----------------------------- Warning --------------------------------
  // | These PropTypes are generated from the TypeScript type definitions |
  // | To update them edit the TypeScript types and run "yarn proptypes"  |
  // ----------------------------------------------------------------------
  /**
   * 12h/24h view for hour selection clock.
   * @default `utils.is12HourCycleInCurrentLocale()`
   */
  ampm: PropTypes.bool,
  /**
   * Display ampm controls under the clock (instead of in the toolbar).
   * @default false
   */
  ampmInClock: PropTypes.bool,
  /**
   * If `true`, the main element is focused during the first mount.
   * This main element is:
   * - the element chosen by the visible view if any (i.e: the selected day on the `day` view).
   * - the `input` element if there is a field rendered.
   */
  autoFocus: PropTypes.bool,
  /**
   * Override or extend the styles applied to the component.
   */
  classes: PropTypes.object,
  className: PropTypes.string,
  /**
   * Overridable components.
   * @default {}
   * @deprecated Please use `slots`.
   */
  components: PropTypes.object,
  /**
   * The props used for each component slot.
   * @default {}
   * @deprecated Please use `slotProps`.
   */
  componentsProps: PropTypes.object,
  /**
   * The default selected value.
   * Used when the component is not controlled.
   */
  defaultValue: PropTypes.any,
  /**
   * If `true`, the picker views and text field are disabled.
   * @default false
   */
  disabled: PropTypes.bool,
  /**
   * If `true`, disable values after the current date for date components, time for time components and both for date time components.
   * @default false
   */
  disableFuture: PropTypes.bool,
  /**
   * Do not ignore date part when validating min/max time.
   * @default false
   */
  disableIgnoringDatePartForTimeValidation: PropTypes.bool,
  /**
   * If `true`, disable values before the current date for date components, time for time components and both for date time components.
   * @default false
   */
  disablePast: PropTypes.bool,
  /**
   * Controlled focused view.
   */
  focusedView: PropTypes.oneOf(['hours', 'minutes', 'seconds']),
  /**
   * Maximal selectable time.
   * The date part of the object will be ignored unless `props.disableIgnoringDatePartForTimeValidation === true`.
   */
  maxTime: PropTypes.any,
  /**
   * Minimal selectable time.
   * The date part of the object will be ignored unless `props.disableIgnoringDatePartForTimeValidation === true`.
   */
  minTime: PropTypes.any,
  /**
   * Step over minutes.
   * @default 1
   */
  minutesStep: PropTypes.number,
  /**
   * Callback fired when the value changes.
   * @template TValue The value type. Will be either the same type as `value` or `null`. Can be in `[start, end]` format in case of range value.
   * @template TView The view type. Will be one of date or time views.
   * @param {TValue} value The new value.
   * @param {PickerSelectionState | undefined} selectionState Indicates if the date selection is complete.
   * @param {TView | undefined} selectedView Indicates the view in which the selection has been made.
   */
  onChange: PropTypes.func,
  /**
   * Callback fired on focused view change.
   * @template TView
   * @param {TView} view The new view to focus or not.
   * @param {boolean} hasFocus `true` if the view should be focused.
   */
  onFocusedViewChange: PropTypes.func,
  /**
   * Callback fired on view change.
   * @template TView
   * @param {TView} view The new view.
   */
  onViewChange: PropTypes.func,
  /**
   * The default visible view.
   * Used when the component view is not controlled.
   * Must be a valid option from `views` list.
   */
  openTo: PropTypes.oneOf(['hours', 'minutes', 'seconds']),
  /**
   * If `true`, the picker views and text field are read-only.
   * @default false
   */
  readOnly: PropTypes.bool,
  /**
   * The date used to generate the new value when both `value` and `defaultValue` are empty.
   * @default The closest valid time using the validation props, except callbacks such as `shouldDisableTime`.
   */
  referenceDate: PropTypes.any,
  /**
   * Disable specific clock time.
   * @param {number} clockValue The value to check.
   * @param {TimeView} view The clock type of the timeValue.
   * @returns {boolean} If `true` the time will be disabled.
   * @deprecated Consider using `shouldDisableTime`.
   */
  shouldDisableClock: PropTypes.func,
  /**
   * Disable specific time.
   * @template TDate
   * @param {TDate} value The value to check.
   * @param {TimeView} view The clock type of the timeValue.
   * @returns {boolean} If `true` the time will be disabled.
   */
  shouldDisableTime: PropTypes.func,
  showViewSwitcher: PropTypes.bool,
  /**
   * The props used for each component slot.
   * @default {}
   */
  slotProps: PropTypes.object,
  /**
   * Overridable component slots.
   * @default {}
   */
  slots: PropTypes.object,
  /**
   * The system prop that allows defining system overrides as well as additional CSS styles.
   */
  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),
  /**
   * Choose which timezone to use for the value.
   * Example: "default", "system", "UTC", "America/New_York".
   * If you pass values from other timezones to some props, they will be converted to this timezone before being used.
   * @see See the {@link https://mui.com/x/react-date-pickers/timezone/ timezones documention} for more details.
   * @default The timezone of the `value` or `defaultValue` prop is defined, 'default' otherwise.
   */
  timezone: PropTypes.string,
  /**
   * The selected value.
   * Used when the component is controlled.
   */
  value: PropTypes.any,
  /**
   * The visible view.
   * Used when the component view is controlled.
   * Must be a valid option from `views` list.
   */
  view: PropTypes.oneOf(['hours', 'minutes', 'seconds']),
  /**
   * Available views.
   * @default ['hours', 'minutes']
   */
  views: PropTypes.arrayOf(PropTypes.oneOf(['hours', 'minutes', 'seconds']).isRequired)
} : void 0;