{"ast": null, "code": "import buildFormatLongFn from \"../../../_lib/buildFormatLongFn/index.js\"; // https://www.unicode.org/cldr/charts/32/summary/sk.html?hide#1986\nvar dateFormats = {\n  full: 'EEEE d. MMMM y',\n  long: 'd. MMMM y',\n  medium: 'd. M. y',\n  short: 'd. M. y'\n};\n\n// https://www.unicode.org/cldr/charts/32/summary/sk.html?hide#2149\nvar timeFormats = {\n  full: 'H:mm:ss zzzz',\n  long: 'H:mm:ss z',\n  medium: 'H:mm:ss',\n  short: 'H:mm'\n};\n\n// https://www.unicode.org/cldr/charts/32/summary/sk.html?hide#1994\nvar dateTimeFormats = {\n  full: '{{date}}, {{time}}',\n  long: '{{date}}, {{time}}',\n  medium: '{{date}}, {{time}}',\n  short: '{{date}} {{time}}'\n};\nvar formatLong = {\n  date: buildFormatLongFn({\n    formats: dateFormats,\n    defaultWidth: 'full'\n  }),\n  time: buildFormatLongFn({\n    formats: timeFormats,\n    defaultWidth: 'full'\n  }),\n  dateTime: buildFormatLongFn({\n    formats: dateTimeFormats,\n    defaultWidth: 'full'\n  })\n};\nexport default formatLong;", "map": {"version": 3, "names": ["buildFormatLongFn", "dateFormats", "full", "long", "medium", "short", "timeFormats", "dateTimeFormats", "formatLong", "date", "formats", "defaultWidth", "time", "dateTime"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/esm/locale/sk/_lib/formatLong/index.js"], "sourcesContent": ["import buildFormatLongFn from \"../../../_lib/buildFormatLongFn/index.js\"; // https://www.unicode.org/cldr/charts/32/summary/sk.html?hide#1986\nvar dateFormats = {\n  full: 'EEEE d. MMMM y',\n  long: 'd. MMMM y',\n  medium: 'd. M. y',\n  short: 'd. M. y'\n};\n\n// https://www.unicode.org/cldr/charts/32/summary/sk.html?hide#2149\nvar timeFormats = {\n  full: 'H:mm:ss zzzz',\n  long: 'H:mm:ss z',\n  medium: 'H:mm:ss',\n  short: 'H:mm'\n};\n\n// https://www.unicode.org/cldr/charts/32/summary/sk.html?hide#1994\nvar dateTimeFormats = {\n  full: '{{date}}, {{time}}',\n  long: '{{date}}, {{time}}',\n  medium: '{{date}}, {{time}}',\n  short: '{{date}} {{time}}'\n};\nvar formatLong = {\n  date: buildFormatLongFn({\n    formats: dateFormats,\n    defaultWidth: 'full'\n  }),\n  time: buildFormatLongFn({\n    formats: timeFormats,\n    defaultWidth: 'full'\n  }),\n  dateTime: buildFormatLongFn({\n    formats: dateTimeFormats,\n    defaultWidth: 'full'\n  })\n};\nexport default formatLong;"], "mappings": "AAAA,OAAOA,iBAAiB,MAAM,0CAA0C,CAAC,CAAC;AAC1E,IAAIC,WAAW,GAAG;EAChBC,IAAI,EAAE,gBAAgB;EACtBC,IAAI,EAAE,WAAW;EACjBC,MAAM,EAAE,SAAS;EACjBC,KAAK,EAAE;AACT,CAAC;;AAED;AACA,IAAIC,WAAW,GAAG;EAChBJ,IAAI,EAAE,cAAc;EACpBC,IAAI,EAAE,WAAW;EACjBC,MAAM,EAAE,SAAS;EACjBC,KAAK,EAAE;AACT,CAAC;;AAED;AACA,IAAIE,eAAe,GAAG;EACpBL,IAAI,EAAE,oBAAoB;EAC1BC,IAAI,EAAE,oBAAoB;EAC1BC,MAAM,EAAE,oBAAoB;EAC5BC,KAAK,EAAE;AACT,CAAC;AACD,IAAIG,UAAU,GAAG;EACfC,IAAI,EAAET,iBAAiB,CAAC;IACtBU,OAAO,EAAET,WAAW;IACpBU,YAAY,EAAE;EAChB,CAAC,CAAC;EACFC,IAAI,EAAEZ,iBAAiB,CAAC;IACtBU,OAAO,EAAEJ,WAAW;IACpBK,YAAY,EAAE;EAChB,CAAC,CAAC;EACFE,QAAQ,EAAEb,iBAAiB,CAAC;IAC1BU,OAAO,EAAEH,eAAe;IACxBI,YAAY,EAAE;EAChB,CAAC;AACH,CAAC;AACD,eAAeH,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}